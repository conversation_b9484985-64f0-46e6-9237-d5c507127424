/**
 * Trading Sessions Configuration
 *
 * Defines the hierarchical structure of trading sessions and macro periods
 * with your specific time blocks and characteristics.
 */

import {
  TradingSession,
  MacroPeriod,
  SessionType,
  MacroPeriodType,
  SessionHierarchy,
} from '../types/tradingSessions';

/**
 * Macro Period Definitions
 */
export const MACRO_PERIODS: Record<MacroPeriodType, Omit<MacroPeriod, 'id'>> = {
  [MacroPeriodType.MORNING_BREAKOUT]: {
    type: MacroPeriodType.MORNING_BREAKOUT,
    name: '9:50-10:10 Macro',
    timeRange: { start: '09:50:00', end: '10:10:00' },
    description: 'Morning breakout period - high volatility after market open',
    characteristics: ['High Volume', 'Breakout Setups', 'Gap Fills', 'Opening Range'],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: true,
  },

  [MacroPeriodType.MID_MORNING_REVERSION]: {
    type: MacroPeriodType.MID_MORNING_REVERSION,
    name: '10:50-11:10 Macro',
    timeRange: { start: '10:50:00', end: '11:10:00' },
    description: 'Mid-morning reversion period - mean reversion opportunities',
    characteristics: ['Mean Reversion', 'Pullback Setups', 'Support/Resistance Tests'],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: true,
  },

  [MacroPeriodType.PRE_LUNCH]: {
    type: MacroPeriodType.PRE_LUNCH,
    name: '11:50-12:10 Macro',
    timeRange: { start: '11:50:00', end: '12:10:00' },
    description: 'Pre-lunch macro window',
    characteristics: ['Consolidation', 'Range Trading'],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: false,
  },

  [MacroPeriodType.LUNCH_MACRO]: {
    type: MacroPeriodType.LUNCH_MACRO,
    name: 'Lunch Macro',
    timeRange: { start: '12:00:00', end: '13:30:00' },
    description: 'Lunch time trading - typically lower volume',
    characteristics: ['Low Volume', 'Range Bound', 'Choppy Price Action'],
    volatilityLevel: 2,
    volumeLevel: 1,
    isHighProbability: false,
  },

  [MacroPeriodType.POST_LUNCH]: {
    type: MacroPeriodType.POST_LUNCH,
    name: '13:50-14:10 Macro',
    timeRange: { start: '13:50:00', end: '14:10:00' },
    description: 'Post-lunch macro window',
    characteristics: ['Volume Pickup', 'Trend Resumption'],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: false,
  },

  [MacroPeriodType.PRE_CLOSE]: {
    type: MacroPeriodType.PRE_CLOSE,
    name: '14:50-15:10 Macro',
    timeRange: { start: '14:50:00', end: '15:10:00' },
    description: 'Pre-close macro window',
    characteristics: ['Institutional Activity', 'Position Adjustments'],
    volatilityLevel: 3,
    volumeLevel: 4,
    isHighProbability: false,
  },

  [MacroPeriodType.POWER_HOUR]: {
    type: MacroPeriodType.POWER_HOUR,
    name: '15:15-15:45 Macro (Power Hour)',
    timeRange: { start: '15:15:00', end: '15:45:00' },
    description: 'Last hour macro - high activity before close',
    characteristics: ['High Volume', 'Institutional Flows', 'EOD Positioning'],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: true,
  },

  [MacroPeriodType.MOC]: {
    type: MacroPeriodType.MOC,
    name: 'MOC (Market on Close)',
    timeRange: { start: '15:45:00', end: '16:00:00' },
    description: 'Market on close period',
    characteristics: ['MOC Orders', 'Final Positioning', 'High Volume'],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: false,
  },

  [MacroPeriodType.LONDON_OPEN]: {
    type: MacroPeriodType.LONDON_OPEN,
    name: 'London Open',
    timeRange: { start: '08:00:00', end: '09:00:00' },
    description: 'London market opening hour',
    characteristics: ['European Activity', 'Currency Moves', 'News Reactions'],
    volatilityLevel: 4,
    volumeLevel: 4,
    isHighProbability: true,
  },

  [MacroPeriodType.LONDON_NY_OVERLAP]: {
    type: MacroPeriodType.LONDON_NY_OVERLAP,
    name: 'London/NY Overlap',
    timeRange: { start: '14:00:00', end: '16:00:00' },
    description: 'London and New York session overlap',
    characteristics: ['Highest Volume', 'Major Moves', 'Cross-Market Activity'],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: true,
  },

  [MacroPeriodType.CUSTOM]: {
    type: MacroPeriodType.CUSTOM,
    name: 'Custom Period',
    timeRange: { start: '00:00:00', end: '23:59:59' },
    description: 'User-defined custom time period',
    characteristics: ['Custom'],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: false,
  },
};

/**
 * Trading Session Definitions
 */
/**
 * Build session hierarchy from database sessions
 */
export const buildSessionHierarchyFromDatabase = (
  dbSessions: Array<{
    name: string;
    start_time: string;
    end_time: string;
    description: string;
  }>
): SessionHierarchy => {
  // Map database sessions to our hierarchical structure
  const sessionMap: Record<string, { session: SessionType; macro?: MacroPeriodType }> = {
    'Pre-Market': { session: SessionType.PRE_MARKET },
    'NY Open': { session: SessionType.NEW_YORK_AM },
    '10:50-11:10': {
      session: SessionType.NEW_YORK_AM,
      macro: MacroPeriodType.MID_MORNING_REVERSION,
    },
    '11:50-12:10': { session: SessionType.NEW_YORK_AM, macro: MacroPeriodType.PRE_LUNCH },
    'Lunch Macro': { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.LUNCH_MACRO },
    '13:50-14:10': { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.POST_LUNCH },
    '14:50-15:10': { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.PRE_CLOSE },
    '15:15-15:45': { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.POWER_HOUR },
    MOC: { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.MOC },
    'Post MOC': { session: SessionType.AFTER_HOURS },
  };

  // Build sessions with their macro periods
  const sessions: TradingSession[] = Object.values(TRADING_SESSIONS).map((sessionConfig) => ({
    id: sessionConfig.type,
    ...sessionConfig,
  }));

  // Add database-defined macro periods to appropriate sessions
  dbSessions.forEach((dbSession) => {
    const mapping = sessionMap[dbSession.name];
    if (mapping?.macro) {
      const parentSession = sessions.find((s) => s.type === mapping.session);
      if (parentSession) {
        const macroConfig = MACRO_PERIODS[mapping.macro];
        if (macroConfig) {
          parentSession.macroPeriods.push({
            id: `${mapping.macro}-db`,
            ...macroConfig,
            timeRange: {
              start: dbSession.start_time,
              end: dbSession.end_time,
            },
            description: dbSession.description,
          });
        }
      }
    }
  });

  return {
    sessions,
    sessionsByType: sessions.reduce((acc, session) => {
      acc[session.type] = session;
      return acc;
    }, {} as Record<SessionType, TradingSession>),
    macrosByType: sessions.reduce((acc, session) => {
      session.macroPeriods.forEach((macro) => {
        acc[macro.type] = { ...macro, parentSession: session.type };
      });
      return acc;
    }, {} as Record<MacroPeriodType, MacroPeriod & { parentSession: SessionType }>),
  };
};

export const TRADING_SESSIONS: Record<SessionType, Omit<TradingSession, 'id'>> = {
  [SessionType.NEW_YORK_AM]: {
    type: SessionType.NEW_YORK_AM,
    name: 'New York AM Session',
    timeRange: { start: '09:30:00', end: '12:00:00' },
    description: 'New York morning session - high activity and volatility',
    timezone: 'America/New_York',
    characteristics: ['High Volume', 'Trend Development', 'Breakout Opportunities'],
    color: '#dc2626', // F1 Red
    macroPeriods: [
      { ...MACRO_PERIODS[MacroPeriodType.MORNING_BREAKOUT], id: 'morning-breakout' },
      { ...MACRO_PERIODS[MacroPeriodType.MID_MORNING_REVERSION], id: 'mid-morning-reversion' },
      { ...MACRO_PERIODS[MacroPeriodType.PRE_LUNCH], id: 'pre-lunch' },
    ],
  },

  [SessionType.NEW_YORK_PM]: {
    type: SessionType.NEW_YORK_PM,
    name: 'New York PM Session',
    timeRange: { start: '12:00:00', end: '16:00:00' },
    description: 'New York afternoon session - institutional activity increases toward close',
    timezone: 'America/New_York',
    characteristics: ['Institutional Flows', 'EOD Positioning', 'Power Hour Activity'],
    color: '#dc2626', // F1 Red
    macroPeriods: [
      { ...MACRO_PERIODS[MacroPeriodType.LUNCH_MACRO], id: 'lunch-macro' },
      { ...MACRO_PERIODS[MacroPeriodType.POST_LUNCH], id: 'post-lunch' },
      { ...MACRO_PERIODS[MacroPeriodType.PRE_CLOSE], id: 'pre-close' },
      { ...MACRO_PERIODS[MacroPeriodType.POWER_HOUR], id: 'power-hour' },
      { ...MACRO_PERIODS[MacroPeriodType.MOC], id: 'moc' },
    ],
  },

  [SessionType.LONDON]: {
    type: SessionType.LONDON,
    name: 'London Session',
    timeRange: { start: '08:00:00', end: '16:00:00' },
    description: 'London trading session - European market activity',
    timezone: 'Europe/London',
    characteristics: ['European Activity', 'Currency Focus', 'News-Driven'],
    color: '#1f2937', // Dark Gray
    macroPeriods: [
      { ...MACRO_PERIODS[MacroPeriodType.LONDON_OPEN], id: 'london-open' },
      { ...MACRO_PERIODS[MacroPeriodType.LONDON_NY_OVERLAP], id: 'london-ny-overlap' },
    ],
  },

  [SessionType.ASIA]: {
    type: SessionType.ASIA,
    name: 'Asia Session',
    timeRange: { start: '18:00:00', end: '03:00:00' },
    description: 'Asian trading session - typically lower volatility',
    timezone: 'Asia/Tokyo',
    characteristics: ['Lower Volume', 'Range Trading', 'News Reactions'],
    color: '#4b5563', // Gray
    macroPeriods: [],
  },

  [SessionType.PRE_MARKET]: {
    type: SessionType.PRE_MARKET,
    name: 'Pre-Market',
    timeRange: { start: '04:00:00', end: '09:30:00' },
    description: 'Pre-market trading hours',
    timezone: 'America/New_York',
    characteristics: ['Low Volume', 'News Reactions', 'Gap Setups'],
    color: '#6b7280', // Light Gray
    macroPeriods: [],
  },

  [SessionType.AFTER_HOURS]: {
    type: SessionType.AFTER_HOURS,
    name: 'After Hours',
    timeRange: { start: '16:00:00', end: '20:00:00' },
    description: 'After-hours trading',
    timezone: 'America/New_York',
    characteristics: ['Low Volume', 'Earnings Reactions', 'News-Driven'],
    color: '#6b7280', // Light Gray
    macroPeriods: [],
  },

  [SessionType.OVERNIGHT]: {
    type: SessionType.OVERNIGHT,
    name: 'Overnight',
    timeRange: { start: '20:00:00', end: '04:00:00' },
    description: 'Overnight session',
    timezone: 'America/New_York',
    characteristics: ['Very Low Volume', 'Futures Activity'],
    color: '#374151', // Dark Gray
    macroPeriods: [],
  },
};
