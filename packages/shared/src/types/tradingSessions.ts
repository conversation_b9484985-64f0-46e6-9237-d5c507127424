/**
 * Trading Sessions - Hierarchical Session System
 *
 * Provides a layered structure for trading sessions and macro periods:
 * - Broad sessions (NY AM, NY PM, London, etc.)
 * - Specific macro periods within each session
 * - Time validation and inheritance logic
 */

/**
 * Time range interface
 */
export interface TimeRange {
  start: string; // HH:MM:SS format
  end: string; // HH:MM:SS format
}

/**
 * Session type enumeration
 */
export enum SessionType {
  // Major Sessions
  LONDON = 'london',
  NEW_YORK_AM = 'new-york-am',
  NEW_YORK_PM = 'new-york-pm',
  ASIA = 'asia',

  // Extended Sessions
  PRE_MARKET = 'pre-market',
  AFTER_HOURS = 'after-hours',
  OVERNIGHT = 'overnight',
}

/**
 * Macro period types
 */
export enum MacroPeriodType {
  // Morning Macros
  MORNING_BREAKOUT = 'morning-breakout', // 9:50-10:10
  MID_MORNING_REVERSION = 'mid-morning-reversion', // 10:50-11:10

  // Lunch Macros
  PRE_LUNCH = 'pre-lunch', // 11:50-12:10
  LUNCH_MACRO_EXTENDED = 'lunch-macro-extended', // 11:30-13:30 (spans sessions)
  LUNCH_MACRO = 'lunch-macro', // 12:00-13:30
  POST_LUNCH = 'post-lunch', // 13:50-14:10

  // Afternoon Macros
  PRE_CLOSE = 'pre-close', // 14:50-15:10
  POWER_HOUR = 'power-hour', // 15:15-15:45
  MOC = 'moc', // 15:45-16:00

  // London Macros
  LONDON_OPEN = 'london-open',
  LONDON_NY_OVERLAP = 'london-ny-overlap',

  // Custom/Other
  CUSTOM = 'custom',
}

/**
 * Macro period definition
 */
export interface MacroPeriod {
  /** Unique identifier */
  id: string;
  /** Macro period type */
  type: MacroPeriodType;
  /** Display name */
  name: string;
  /** Time range */
  timeRange: TimeRange;
  /** Description */
  description: string;
  /** Trading characteristics */
  characteristics: string[];
  /** Typical volatility level (1-5) */
  volatilityLevel: number;
  /** Typical volume level (1-5) */
  volumeLevel: number;
  /** Whether this is a high-probability setup period */
  isHighProbability: boolean;
  /** Nested sub-periods within this macro (for overlapping scenarios) */
  subPeriods?: MacroPeriod[];
  /** Parent macro period (if this is a sub-period) */
  parentMacro?: MacroPeriodType;
  /** Whether this macro spans multiple sessions */
  isMultiSession?: boolean;
  /** Sessions this macro spans (if multi-session) */
  spansSessions?: SessionType[];
}

/**
 * Trading session definition
 */
export interface TradingSession {
  /** Unique identifier */
  id: string;
  /** Session type */
  type: SessionType;
  /** Display name */
  name: string;
  /** Time range for the entire session */
  timeRange: TimeRange;
  /** Description */
  description: string;
  /** Timezone (e.g., 'America/New_York', 'Europe/London') */
  timezone: string;
  /** Market characteristics */
  characteristics: string[];
  /** Child macro periods */
  macroPeriods: MacroPeriod[];
  /** Whether this session is currently active */
  isActive?: boolean;
  /** Session color for UI display */
  color: string;
}

/**
 * Session hierarchy structure
 */
export interface SessionHierarchy {
  /** All available sessions */
  sessions: TradingSession[];
  /** Quick lookup by session type */
  sessionsByType: Record<SessionType, TradingSession>;
  /** Quick lookup by macro period type */
  macrosByType: Record<
    MacroPeriodType,
    MacroPeriod & { parentSession?: SessionType; spansSessions?: SessionType[] }
  >;
  /** Multi-session macro periods that span across sessions */
  multiSessionMacros: MacroPeriod[];
}

/**
 * Session selection state
 */
export interface SessionSelection {
  /** Selected session (if any) */
  session?: SessionType;
  /** Selected macro period (if any) */
  macroPeriod?: MacroPeriodType;
  /** Custom time range (if neither session nor macro selected) */
  customTimeRange?: TimeRange;
  /** Display label for the selection */
  displayLabel: string;
  /** Whether this is a broad session or specific macro */
  selectionType: 'session' | 'macro' | 'custom';
}

/**
 * Time validation result
 */
export interface TimeValidationResult {
  /** Whether the time is valid */
  isValid: boolean;
  /** Error message if invalid */
  error?: string;
  /** Warning message if applicable */
  warning?: string;
  /** Suggested session/macro if time falls within known periods */
  suggestedSession?: SessionType;
  /** Suggested macro period if time falls within known periods */
  suggestedMacro?: MacroPeriodType;
}

/**
 * Session filter options
 */
export interface SessionFilterOptions {
  /** Include only active sessions */
  activeOnly?: boolean;
  /** Include only high-probability periods */
  highProbabilityOnly?: boolean;
  /** Minimum volatility level */
  minVolatility?: number;
  /** Maximum volatility level */
  maxVolatility?: number;
  /** Specific session types to include */
  sessionTypes?: SessionType[];
  /** Specific macro types to include */
  macroTypes?: MacroPeriodType[];
}

/**
 * Session analytics data
 */
export interface SessionAnalytics {
  /** Session or macro period identifier */
  id: string;
  /** Total trades in this period */
  totalTrades: number;
  /** Win rate percentage */
  winRate: number;
  /** Average R-multiple */
  averageRMultiple: number;
  /** Total P&L */
  totalPnl: number;
  /** Best performing setups in this period */
  topSetups: string[];
  /** Performance trend (improving, declining, stable) */
  trend: 'improving' | 'declining' | 'stable';
}
