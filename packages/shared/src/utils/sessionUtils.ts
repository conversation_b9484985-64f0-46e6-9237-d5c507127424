/**
 * Session Utilities
 *
 * Provides utilities for working with the hierarchical session system:
 * - Time validation and parsing
 * - Session/macro period lookup
 * - Intelligent selection logic
 * - Time range validation
 */

import {
  TradingSession,
  MacroPeriod,
  SessionType,
  MacroPeriodType,
  SessionHierarchy,
  SessionSelection,
  TimeValidationResult,
  TimeRange,
  SessionFilterOptions,
} from '../types/tradingSessions';
import { TRADING_SESSIONS, MACRO_PERIODS } from '../config/tradingSessionsConfig';

/**
 * Session Utilities Class
 */
export class SessionUtils {
  private static hierarchy: SessionHierarchy | null = null;

  /**
   * Initialize and get the session hierarchy
   */
  static getSessionHierarchy(): SessionHierarchy {
    if (!this.hierarchy) {
      this.hierarchy = this.buildHierarchy();
    }
    return this.hierarchy;
  }

  /**
   * Build the complete session hierarchy
   */
  private static buildHierarchy(): SessionHierarchy {
    const sessions: TradingSession[] = Object.entries(TRADING_SESSIONS).map(([key, config]) => ({
      id: key,
      ...config,
    }));

    const sessionsByType: Record<SessionType, TradingSession> = {} as any;
    const macrosByType: Record<MacroPeriodType, MacroPeriod & { parentSession: SessionType }> =
      {} as any;

    sessions.forEach((session) => {
      sessionsByType[session.type] = session;

      session.macroPeriods.forEach((macro) => {
        macrosByType[macro.type] = {
          ...macro,
          parentSession: session.type,
        };
      });
    });

    return {
      sessions,
      sessionsByType,
      macrosByType,
    };
  }

  /**
   * Parse time string to minutes since midnight
   */
  static timeToMinutes(timeStr: string): number {
    const [hours, minutes, seconds = 0] = timeStr.split(':').map(Number);
    return hours * 60 + minutes + seconds / 60;
  }

  /**
   * Convert minutes since midnight to time string
   */
  static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    const secs = Math.floor((minutes % 1) * 60);
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  }

  /**
   * Check if a time falls within a time range
   */
  static isTimeInRange(time: string, range: TimeRange): boolean {
    const timeMinutes = this.timeToMinutes(time);
    const startMinutes = this.timeToMinutes(range.start);
    const endMinutes = this.timeToMinutes(range.end);

    // Handle overnight ranges (end < start)
    if (endMinutes < startMinutes) {
      return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
    }

    return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
  }

  /**
   * Validate a time and suggest appropriate session/macro
   */
  static validateTime(time: string): TimeValidationResult {
    // Basic time format validation
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
    if (!timeRegex.test(time)) {
      return {
        isValid: false,
        error: 'Invalid time format. Use HH:MM or HH:MM:SS format.',
      };
    }

    const hierarchy = this.getSessionHierarchy();

    // Check for macro period matches first (more specific)
    for (const [macroType, macro] of Object.entries(hierarchy.macrosByType)) {
      if (this.isTimeInRange(time, macro.timeRange)) {
        return {
          isValid: true,
          suggestedMacro: macroType as MacroPeriodType,
          suggestedSession: macro.parentSession,
        };
      }
    }

    // Check for session matches
    for (const session of hierarchy.sessions) {
      if (this.isTimeInRange(time, session.timeRange)) {
        return {
          isValid: true,
          suggestedSession: session.type,
          warning: 'Time falls within session but not in a specific macro period.',
        };
      }
    }

    return {
      isValid: true,
      warning: 'Time does not fall within any defined session or macro period.',
    };
  }

  /**
   * Get session by type
   */
  static getSession(sessionType: SessionType): TradingSession | null {
    const hierarchy = this.getSessionHierarchy();
    return hierarchy.sessionsByType[sessionType] || null;
  }

  /**
   * Get macro period by type
   */
  static getMacroPeriod(
    macroType: MacroPeriodType
  ): (MacroPeriod & { parentSession: SessionType }) | null {
    const hierarchy = this.getSessionHierarchy();
    return hierarchy.macrosByType[macroType] || null;
  }

  /**
   * Get all macro periods for a session
   */
  static getMacroPeriodsForSession(sessionType: SessionType): MacroPeriod[] {
    const session = this.getSession(sessionType);
    return session?.macroPeriods || [];
  }

  /**
   * Create a session selection
   */
  static createSessionSelection(
    session?: SessionType,
    macroPeriod?: MacroPeriodType,
    customTimeRange?: TimeRange
  ): SessionSelection {
    if (macroPeriod) {
      const macro = this.getMacroPeriod(macroPeriod);
      return {
        session: macro?.parentSession,
        macroPeriod,
        displayLabel: macro?.name || 'Unknown Macro',
        selectionType: 'macro',
      };
    }

    if (session) {
      const sessionData = this.getSession(session);
      return {
        session,
        displayLabel: sessionData?.name || 'Unknown Session',
        selectionType: 'session',
      };
    }

    if (customTimeRange) {
      return {
        customTimeRange,
        displayLabel: `${customTimeRange.start} - ${customTimeRange.end}`,
        selectionType: 'custom',
      };
    }

    return {
      displayLabel: 'No Selection',
      selectionType: 'custom',
    };
  }

  /**
   * Filter sessions and macros based on criteria
   */
  static filterSessions(options: SessionFilterOptions = {}): {
    sessions: TradingSession[];
    macros: (MacroPeriod & { parentSession: SessionType })[];
  } {
    const hierarchy = this.getSessionHierarchy();
    let sessions = [...hierarchy.sessions];
    let macros = Object.values(hierarchy.macrosByType);

    // Filter by active status
    if (options.activeOnly) {
      sessions = sessions.filter((s) => s.isActive);
    }

    // Filter by session types
    if (options.sessionTypes?.length) {
      sessions = sessions.filter((s) => options.sessionTypes!.includes(s.type));
    }

    // Filter by macro types
    if (options.macroTypes?.length) {
      macros = macros.filter((m) => options.macroTypes!.includes(m.type));
    }

    // Filter by high probability
    if (options.highProbabilityOnly) {
      macros = macros.filter((m) => m.isHighProbability);
    }

    // Filter by volatility
    if (options.minVolatility !== undefined) {
      macros = macros.filter((m) => m.volatilityLevel >= options.minVolatility!);
    }
    if (options.maxVolatility !== undefined) {
      macros = macros.filter((m) => m.volatilityLevel <= options.maxVolatility!);
    }

    return { sessions, macros };
  }

  /**
   * Get current active session based on current time
   */
  static getCurrentSession(): SessionSelection | null {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now
      .getMinutes()
      .toString()
      .padStart(2, '0')}:00`;

    const validation = this.validateTime(currentTime);

    if (validation.suggestedMacro) {
      return this.createSessionSelection(validation.suggestedSession, validation.suggestedMacro);
    }

    if (validation.suggestedSession) {
      return this.createSessionSelection(validation.suggestedSession);
    }

    return null;
  }

  /**
   * Check if two time ranges overlap
   */
  static timeRangesOverlap(range1: TimeRange, range2: TimeRange): boolean {
    const start1 = this.timeToMinutes(range1.start);
    const end1 = this.timeToMinutes(range1.end);
    const start2 = this.timeToMinutes(range2.start);
    const end2 = this.timeToMinutes(range2.end);

    return Math.max(start1, start2) < Math.min(end1, end2);
  }

  /**
   * Get display options for UI dropdowns
   */
  static getDisplayOptions(): {
    sessionOptions: Array<{ value: SessionType; label: string; group: string }>;
    macroOptions: Array<{
      value: MacroPeriodType;
      label: string;
      group: string;
      parentSession: SessionType;
    }>;
  } {
    const hierarchy = this.getSessionHierarchy();

    const sessionOptions = hierarchy.sessions.map((session) => ({
      value: session.type,
      label: session.name,
      group: 'Sessions',
    }));

    const macroOptions = Object.values(hierarchy.macrosByType).map((macro) => ({
      value: macro.type,
      label: macro.name,
      group: hierarchy.sessionsByType[macro.parentSession]?.name || 'Other',
      parentSession: macro.parentSession,
    }));

    return { sessionOptions, macroOptions };
  }

  /**
   * Convert legacy session string to new session selection
   */
  static convertLegacySession(legacySession: string): SessionSelection | null {
    // Map legacy session names to new system
    const legacyMapping: Record<string, { session?: SessionType; macro?: MacroPeriodType }> = {
      'NY Open': { session: SessionType.NEW_YORK_AM },
      'London Open': { session: SessionType.LONDON },
      'Lunch Macro': { macro: MacroPeriodType.LUNCH_MACRO },
      MOC: { macro: MacroPeriodType.MOC },
      Overnight: { session: SessionType.OVERNIGHT },
      'Pre-Market': { session: SessionType.PRE_MARKET },
      'After Hours': { session: SessionType.AFTER_HOURS },
      'Power Hour': { macro: MacroPeriodType.POWER_HOUR },
      '10:50-11:10': { macro: MacroPeriodType.MID_MORNING_REVERSION },
      '15:15-15:45': { macro: MacroPeriodType.POWER_HOUR },
    };

    const mapping = legacyMapping[legacySession];
    if (!mapping) return null;

    return this.createSessionSelection(mapping.session, mapping.macro);
  }
}
