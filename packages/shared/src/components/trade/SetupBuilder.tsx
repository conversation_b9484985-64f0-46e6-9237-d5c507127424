/**
 * Setup Builder Component
 *
 * Modular setup construction matrix that replaces complex dropdown-based
 * setup classification with atomic elements that can be combined infinitely.
 *
 * MOVED TO SHARED: This component was causing circular dependencies by being
 * imported across multiple features. Now it's a shared component that can be
 * used by any feature without creating coupling.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { SetupComponents } from '../../types';
import { SETUP_ELEMENTS } from '../../constants';

// F1 Racing Theme Styled Components
const BuilderContainer = styled.div`
  background: #1a1a1a;
  border: 1px solid #4b5563;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
`;

const SectionTitle = styled.h3`
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
  border-bottom: 2px solid #dc2626;
  padding-bottom: 8px;
`;

const MatrixGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const ElementSection = styled.div`
  background: #262626;
  border: 1px solid #4b5563;
  border-radius: 6px;
  padding: 16px;
`;

const ElementTitle = styled.h4`
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  background: #0f0f0f;
  border: 1px solid #4b5563;
  border-radius: 4px;
  color: #ffffff;
  font-size: 0.9rem;

  &:focus {
    outline: none;
    border-color: #dc2626;
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
  }

  option {
    background: #0f0f0f;
    color: #ffffff;
  }
`;

const PreviewContainer = styled.div`
  background: #0f0f0f;
  border: 1px solid #4b5563;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
`;

const PreviewText = styled.div`
  color: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  min-height: 20px;
`;

const RequiredIndicator = styled.span`
  color: #dc2626;
  margin-left: 4px;
`;

const OptionalIndicator = styled.span`
  color: #9ca3af;
  font-size: 0.8rem;
  margin-left: 4px;
`;

interface SetupBuilderProps {
  onSetupChange: (components: SetupComponents) => void;
  initialComponents?: SetupComponents;
}

const SetupBuilder: React.FC<SetupBuilderProps> = ({ onSetupChange, initialComponents }) => {
  const [components, setComponents] = useState<SetupComponents>({
    constant: initialComponents?.constant || '',
    action: initialComponents?.action || 'None',
    variable: initialComponents?.variable || 'None',
    entry: initialComponents?.entry || '',
  });

  // Update parent component when components change
  useEffect(() => {
    if (components.constant && components.entry) {
      onSetupChange(components);
    }
  }, [components, onSetupChange]);

  const handleComponentChange = (elementType: keyof SetupComponents, value: string) => {
    setComponents((prev) => ({
      ...prev,
      [elementType]: value,
    }));
  };

  const generatePreview = (): string => {
    const { constant, action, variable, entry } = components;

    if (!constant || !entry) {
      return 'Select required elements to see setup preview...';
    }

    let preview = constant;

    if (action && action !== 'None') {
      preview += ` → ${action}`;
    }

    if (variable && variable !== 'None') {
      preview += ` → ${variable}`;
    }

    preview += ` [${entry}]`;

    return preview;
  };

  return (
    <BuilderContainer>
      <SectionTitle>Setup Construction Matrix</SectionTitle>

      <MatrixGrid>
        {/* Constant Element */}
        <ElementSection>
          <ElementTitle>
            Constant Element
            <RequiredIndicator>*</RequiredIndicator>
          </ElementTitle>
          <Select
            value={components.constant}
            onChange={(e) => handleComponentChange('constant', e.target.value)}
          >
            <option value="">Select Constant</option>
            {SETUP_ELEMENTS.constant.parentArrays.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
            {SETUP_ELEMENTS.constant.fvgTypes.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </Select>
        </ElementSection>

        {/* Action Element */}
        <ElementSection>
          <ElementTitle>
            Action Element
            <OptionalIndicator>(optional)</OptionalIndicator>
          </ElementTitle>
          <Select
            value={components.action}
            onChange={(e) => handleComponentChange('action', e.target.value)}
          >
            <option value="None">None</option>
            {SETUP_ELEMENTS.action.liquidityEvents.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </Select>
        </ElementSection>

        {/* Variable Element */}
        <ElementSection>
          <ElementTitle>
            Variable Element
            <OptionalIndicator>(optional)</OptionalIndicator>
          </ElementTitle>
          <Select
            value={components.variable}
            onChange={(e) => handleComponentChange('variable', e.target.value)}
          >
            <option value="None">None</option>
            {SETUP_ELEMENTS.variable.rdTypes.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </Select>
        </ElementSection>

        {/* Entry Element */}
        <ElementSection>
          <ElementTitle>
            Entry Method
            <RequiredIndicator>*</RequiredIndicator>
          </ElementTitle>
          <Select
            value={components.entry}
            onChange={(e) => handleComponentChange('entry', e.target.value)}
          >
            <option value="">Select Entry Method</option>
            {SETUP_ELEMENTS.entry.methods.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </Select>
        </ElementSection>
      </MatrixGrid>

      {/* Setup Preview */}
      <PreviewContainer>
        <ElementTitle>Setup Preview</ElementTitle>
        <PreviewText>{generatePreview()}</PreviewText>
      </PreviewContainer>
    </BuilderContainer>
  );
};

export default SetupBuilder;
