/**
 * Hierarchical Session Selector Component
 * 
 * A sophisticated session selector that supports both broad sessions
 * and specific macro periods with intelligent grouping and validation.
 */

import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { SessionType, MacroPeriodType, SessionSelection } from '../../types/tradingSessions';
import { useSessionSelection } from '../../hooks/useSessionSelection';

export interface HierarchicalSessionSelectorProps {
  /** Current selection */
  value?: SessionSelection;
  /** Change handler */
  onChange: (selection: SessionSelection) => void;
  /** Whether to show macro periods */
  showMacroPeriods?: boolean;
  /** Whether to show current session indicator */
  showCurrentSession?: boolean;
  /** Whether to allow custom time ranges */
  allowCustomRange?: boolean;
  /** Placeholder text */
  placeholder?: string;
  /** Whether the selector is disabled */
  disabled?: boolean;
  /** Error message */
  error?: string;
  /** CSS class name */
  className?: string;
}

const Container = styled.div<{ hasError?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const SelectorContainer = styled.div<{ hasError?: boolean; disabled?: boolean }>`
  position: relative;
  border: 1px solid ${({ theme, hasError }) => 
    hasError ? theme.colors?.error || '#ef4444' : theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  transition: all 0.2s ease;
  opacity: ${({ disabled }) => disabled ? 0.6 : 1};
  pointer-events: ${({ disabled }) => disabled ? 'none' : 'auto'};

  &:hover {
    border-color: ${({ theme, hasError }) => 
      hasError ? theme.colors?.error || '#ef4444' : theme.colors?.primary || '#dc2626'}40;
  }

  &:focus-within {
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  }
`;

const SelectedValue = styled.div`
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const DropdownIcon = styled.div<{ isOpen: boolean }>`
  transition: transform 0.2s ease;
  transform: ${({ isOpen }) => isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;

const DropdownMenu = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
  max-height: 300px;
  overflow-y: auto;
  display: ${({ isOpen }) => isOpen ? 'block' : 'none'};
`;

const SessionGroup = styled.div`
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  
  &:last-child {
    border-bottom: none;
  }
`;

const SessionHeader = styled.div<{ isSelected?: boolean }>`
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme, isSelected }) => 
    isSelected ? theme.colors?.primary || '#dc2626' : 'transparent'};
  color: ${({ theme, isSelected }) => 
    isSelected ? '#ffffff' : theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${({ theme, isSelected }) => 
      isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.border || '#4b5563'}40;
  }
`;

const MacroList = styled.div`
  background: ${({ theme }) => theme.colors?.background || '#111827'};
`;

const MacroItem = styled.div<{ isSelected?: boolean }>`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.lg || '24px'};
  color: ${({ theme, isSelected }) => 
    isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.textSecondary || '#9ca3af'};
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;
  border-left: 3px solid ${({ theme, isSelected }) => 
    isSelected ? theme.colors?.primary || '#dc2626' : 'transparent'};

  &:hover {
    background: ${({ theme }) => theme.colors?.border || '#4b5563'}20;
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;

const CurrentSessionIndicator = styled.div`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.success || '#10b981'};
  font-weight: 500;
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

/**
 * Hierarchical Session Selector Component
 */
export const HierarchicalSessionSelector: React.FC<HierarchicalSessionSelectorProps> = ({
  value,
  onChange,
  showMacroPeriods = true,
  showCurrentSession = true,
  allowCustomRange = false,
  placeholder = 'Select session or macro period',
  disabled = false,
  error,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const {
    hierarchicalOptions,
    currentSession,
    isCurrentSessionActive,
    selectSession,
    selectMacro,
  } = useSessionSelection({
    onSelectionChange: onChange,
  });

  // Display value
  const displayValue = useMemo(() => {
    if (value?.displayLabel) {
      return value.displayLabel;
    }
    return placeholder;
  }, [value, placeholder]);

  // Handle session selection
  const handleSessionSelect = (sessionType: SessionType) => {
    selectSession(sessionType);
    setIsOpen(false);
  };

  // Handle macro selection
  const handleMacroSelect = (macroType: MacroPeriodType) => {
    selectMacro(macroType);
    setIsOpen(false);
  };

  // Check if session is selected
  const isSessionSelected = (sessionType: SessionType) => {
    return value?.session === sessionType && value?.selectionType === 'session';
  };

  // Check if macro is selected
  const isMacroSelected = (macroType: MacroPeriodType) => {
    return value?.macroPeriod === macroType && value?.selectionType === 'macro';
  };

  // Check if session is current
  const isCurrentSession = (sessionType: SessionType) => {
    return currentSession?.session === sessionType;
  };

  return (
    <Container className={className} hasError={!!error}>
      <SelectorContainer 
        hasError={!!error} 
        disabled={disabled}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <SelectedValue>
          <span>{displayValue}</span>
          <DropdownIcon isOpen={isOpen}>▼</DropdownIcon>
        </SelectedValue>
        
        <DropdownMenu isOpen={isOpen}>
          {hierarchicalOptions.map(({ session, sessionLabel, macros }) => (
            <SessionGroup key={session}>
              <SessionHeader 
                isSelected={isSessionSelected(session)}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSessionSelect(session);
                }}
              >
                <span>{sessionLabel}</span>
                {showCurrentSession && isCurrentSession(session) && (
                  <CurrentSessionIndicator>
                    🔴 LIVE
                  </CurrentSessionIndicator>
                )}
              </SessionHeader>
              
              {showMacroPeriods && macros.length > 0 && (
                <MacroList>
                  {macros.map(({ value: macroValue, label }) => (
                    <MacroItem
                      key={macroValue}
                      isSelected={isMacroSelected(macroValue)}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMacroSelect(macroValue);
                      }}
                    >
                      {label}
                    </MacroItem>
                  ))}
                </MacroList>
              )}
            </SessionGroup>
          ))}
        </DropdownMenu>
      </SelectorContainer>
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </Container>
  );
};
