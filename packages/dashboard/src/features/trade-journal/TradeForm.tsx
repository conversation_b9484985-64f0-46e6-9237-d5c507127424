/**
 * Trade Form Page
 *
 * This page displays a form for adding or editing a trade entry.
 * Uses a tabbed interface for progressive disclosure of form fields.
 */

import React from 'react';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { useTradeForm } from './hooks/useTradeForm';
import PatternQualityAssessment from './components/trade-pattern-quality/PatternQualityAssessment';
import DOLAnalysis from './components/trade-dol-analysis/TradeDOLAnalysis';
import {
  TradeFormHeader,
  TradeFormBasicFields,
  TradeFormTimingFields,
  TradeFormRiskFields,
  TradeFormStrategyFields,
  TradeFormActions,
  TradeFormMessages,
  TradeFormLoading,
} from './components/trade-form';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;

const ContentSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  position: relative; /* Required for absolute positioning of loading overlay */
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xl};
`;

const SectionContainer = styled.div`
  background: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
  padding-bottom: ${({ theme }) => theme.spacing.sm};
  border-bottom: 2px solid ${({ theme }) => theme.colors.primary};
`;

const SectionDivider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.xl} 0;
`;

/**
 * TradeForm Component
 *
 * Displays a form for adding or editing trade entries in a single scrollable page
 * with all sections displayed vertically for better workflow.
 */
const TradeForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // Enhanced debugging for ID parameter
  console.log(`TradeForm component mounted with ID parameter: "${id}"`);
  console.log(`Current URL: ${window.location.href}`);
  console.log(`Is edit mode: ${id && id !== 'new'}`);
  console.log(`Is new trade: ${id === 'new'}`);

  const {
    formValues,
    setFormValues,
    handleChange,
    handleSubmit,
    isSubmitting,
    isLoading,
    error,
    success,
    validationErrors,
    isNewTrade,
    calculateProfitLoss,
  } = useTradeForm(id);

  return (
    <PageContainer>
      <TradeFormHeader isNewTrade={isNewTrade} formValues={formValues} />

      <ContentSection>
        <TradeFormLoading isLoading={isLoading} />

        <Form onSubmit={handleSubmit}>
          <TradeFormMessages error={error} success={success} />

          {/* Basic Information Section */}
          <TradeFormBasicFields
            formValues={formValues}
            handleChange={handleChange}
            validationErrors={validationErrors}
            calculateProfitLoss={calculateProfitLoss}
            setFormValues={setFormValues}
          />

          {/* Timing Section */}
          <TradeFormTimingFields
            formValues={formValues}
            handleChange={handleChange}
            validationErrors={validationErrors}
          />

          {/* Strategy & Setup Section */}
          <SectionContainer>
            <SectionTitle>📊 Strategy & Setup</SectionTitle>
            <TradeFormStrategyFields
              formValues={formValues}
              handleChange={handleChange}
              validationErrors={validationErrors}
              setFormValues={setFormValues}
            />
          </SectionContainer>

          <SectionDivider />

          {/* Analysis & DOL Section */}
          <SectionContainer>
            <SectionTitle>🔍 Analysis & DOL</SectionTitle>
            <PatternQualityAssessment
              formValues={formValues}
              onChange={(field, value) => {
                setFormValues((prev) => ({
                  ...prev,
                  [field]: value,
                }));
              }}
            />

            <SectionDivider />

            <DOLAnalysis
              formValues={formValues}
              onChange={(field, value) => {
                setFormValues((prev) => ({
                  ...prev,
                  [field]: value,
                }));
              }}
              validationErrors={validationErrors}
            />
          </SectionContainer>

          {/* Pricing & P&L Section */}
          <SectionContainer>
            <SectionTitle>💰 Pricing & P&L</SectionTitle>
            <TradeFormRiskFields
              formValues={formValues}
              handleChange={handleChange}
              validationErrors={validationErrors}
            />
          </SectionContainer>

          <SectionDivider />

          {/* Form Actions */}
          <TradeFormActions
            isSubmitting={isSubmitting}
            isLoading={isLoading}
            isNewTrade={isNewTrade}
          />
        </Form>
      </ContentSection>
    </PageContainer>
  );
};

export default TradeForm;
