/**
 * useTradeFormFields Hook
 * 
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Enhanced hook for managing trade form fields with validation and calculations.
 * 
 * BENEFITS:
 * - Focused responsibility (field management only)
 * - Built-in validation with field-specific rules
 * - Automatic profit/loss calculations
 * - Type-safe field handling
 * - Reusable across different trading forms
 */

import { useCallback, useMemo } from 'react';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { FIELD_VALIDATION_RULES, getFieldConfig } from './tradeFormFieldConfig';

export interface UseTradeFormFieldsProps {
  /** Current form values */
  formValues: TradeFormValues;
  /** Form values setter */
  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;
  /** Validation errors */
  validationErrors: ValidationErrors;
  /** Validation errors setter */
  setValidationErrors: React.Dispatch<React.SetStateAction<ValidationErrors>>;
  /** Optional profit calculation callback */
  calculateProfitLoss?: () => void;
}

export interface UseTradeFormFieldsReturn {
  /** Standard change handler */
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  /** Price change handler (triggers calculations) */
  handlePriceChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  /** Validate specific field */
  validateField: (name: string, value: any) => string | null;
  /** Validate all fields */
  validateAllFields: () => boolean;
  /** Calculate profit/loss from current values */
  calculateProfitFromValues: () => number;
  /** Check if field affects calculations */
  isCalculationField: (fieldName: string) => boolean;
}

/**
 * Fields that trigger profit/loss calculations
 */
const CALCULATION_FIELDS = ['entryPrice', 'exitPrice', 'quantity', 'direction'];

/**
 * useTradeFormFields Hook
 * 
 * Enhanced form field management with validation and calculations.
 */
export const useTradeFormFields = ({
  formValues,
  setFormValues,
  validationErrors,
  setValidationErrors,
  calculateProfitLoss,
}: UseTradeFormFieldsProps): UseTradeFormFieldsReturn => {
  
  /**
   * Validate individual field
   */
  const validateField = useCallback((name: string, value: any): string | null => {
    const fieldConfig = getFieldConfig(name);
    const validationRule = FIELD_VALIDATION_RULES[name as keyof typeof FIELD_VALIDATION_RULES];
    
    // Check required fields
    if (fieldConfig?.required && (!value || value === '')) {
      return `${fieldConfig.label} is required`;
    }
    
    // Skip validation for empty optional fields
    if (!value || value === '') {
      return null;
    }
    
    // Apply field-specific validation rules
    if (validationRule) {
      if ('pattern' in validationRule) {
        if (!validationRule.pattern.test(value)) {
          return validationRule.message;
        }
      }
      
      if ('min' in validationRule) {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue < validationRule.min) {
          return validationRule.message;
        }
      }
      
      if ('max' in validationRule) {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue > validationRule.max) {
          return validationRule.message;
        }
      }
    }
    
    // Additional validation for specific field types
    switch (name) {
      case 'date':
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return 'Please enter a valid date';
        }
        if (date > new Date()) {
          return 'Date cannot be in the future';
        }
        break;
        
      case 'entryPrice':
      case 'exitPrice':
      case 'quantity':
      case 'profit':
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          return 'Please enter a valid number';
        }
        break;
    }
    
    return null;
  }, []);
  
  /**
   * Standard change handler
   */
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Update form values
    setFormValues(prev => ({ ...prev, [name]: value }));
    
    // Validate field and update errors
    const fieldError = validateField(name, value);
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      if (fieldError) {
        newErrors[name] = fieldError;
      } else {
        delete newErrors[name];
      }
      return newErrors;
    });
  }, [validateField, setFormValues, setValidationErrors]);
  
  /**
   * Calculate profit/loss from current form values
   */
  const calculateProfitFromValues = useCallback((): number => {
    const entryPrice = parseFloat(formValues.entryPrice) || 0;
    const exitPrice = parseFloat(formValues.exitPrice) || 0;
    const quantity = parseFloat(formValues.quantity) || 0;
    const direction = formValues.direction;
    
    if (entryPrice === 0 || exitPrice === 0 || quantity === 0) {
      return 0;
    }
    
    const priceDiff = direction === 'long' 
      ? exitPrice - entryPrice 
      : entryPrice - exitPrice;
    
    return priceDiff * quantity;
  }, [formValues.entryPrice, formValues.exitPrice, formValues.quantity, formValues.direction]);
  
  /**
   * Price change handler (triggers calculations)
   */
  const handlePriceChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // Update form values
    setFormValues(prev => {
      const newValues = { ...prev, [name]: value };
      
      // Auto-calculate profit if all required fields are present
      if (CALCULATION_FIELDS.every(field => 
        field === name ? value : newValues[field as keyof TradeFormValues]
      )) {
        const entryPrice = parseFloat(field === 'entryPrice' ? value : newValues.entryPrice) || 0;
        const exitPrice = parseFloat(field === 'exitPrice' ? value : newValues.exitPrice) || 0;
        const quantity = parseFloat(field === 'quantity' ? value : newValues.quantity) || 0;
        const direction = field === 'direction' ? value : newValues.direction;
        
        if (entryPrice > 0 && exitPrice > 0 && quantity > 0) {
          const priceDiff = direction === 'long' 
            ? exitPrice - entryPrice 
            : entryPrice - exitPrice;
          
          newValues.profit = (priceDiff * quantity).toFixed(2);
        }
      }
      
      return newValues;
    });
    
    // Validate field
    const fieldError = validateField(name, value);
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      if (fieldError) {
        newErrors[name] = fieldError;
      } else {
        delete newErrors[name];
      }
      return newErrors;
    });
    
    // Trigger external calculation if provided
    if (calculateProfitLoss) {
      setTimeout(calculateProfitLoss, 0);
    }
  }, [validateField, setFormValues, setValidationErrors, calculateProfitLoss]);
  
  /**
   * Validate all fields
   */
  const validateAllFields = useCallback((): boolean => {
    const errors: ValidationErrors = {};
    let isValid = true;
    
    // Validate all form values
    Object.entries(formValues).forEach(([name, value]) => {
      const error = validateField(name, value);
      if (error) {
        errors[name] = error;
        isValid = false;
      }
    });
    
    setValidationErrors(errors);
    return isValid;
  }, [formValues, validateField, setValidationErrors]);
  
  /**
   * Check if field affects calculations
   */
  const isCalculationField = useCallback((fieldName: string): boolean => {
    return CALCULATION_FIELDS.includes(fieldName);
  }, []);
  
  return {
    handleChange,
    handlePriceChange,
    validateField,
    validateAllFields,
    calculateProfitFromValues,
    isCalculationField,
  };
};

export default useTradeFormFields;
