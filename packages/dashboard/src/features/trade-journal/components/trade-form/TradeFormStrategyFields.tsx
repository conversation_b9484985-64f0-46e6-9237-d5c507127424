/**
 * Trade Form Strategy Fields Component
 *
 * Displays the strategy fields for the trade form
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import {
  Trade,
  TradeFormData,
  SetupComponents,
  SetupBuilder,
} from '@adhd-trading-dashboard/shared';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import {
  MODEL_TYPE_OPTIONS,
  SETUP_OPTIONS,
  ENTRY_VERSION_OPTIONS,
  PATTERN_QUALITY_OPTIONS,
} from '../../hooks';
import { SelectDropdown } from '@adhd-trading-dashboard/shared';

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const TextArea = styled.textarea`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  min-height: 100px;

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: ${({ theme }) => theme.spacing.md} 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;

interface TradeFormStrategyFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;
}

/**
 * Trade Form Strategy Fields Component
 */
const TradeFormStrategyFields: React.FC<TradeFormStrategyFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
  setFormValues,
}) => {
  return (
    <>
      {/* Basic Strategy Section */}
      <SectionTitle>Basic Strategy</SectionTitle>
      <FormRow>
        <FormGroup>
          <SelectDropdown
            id="modelType"
            name="modelType"
            label="Model Type"
            value={formValues.modelType || ''}
            onChange={handleChange}
            options={MODEL_TYPE_OPTIONS}
            placeholder="Select Model Type"
          />
        </FormGroup>

        <FormGroup>
          <SelectDropdown
            id="setup"
            name="setup"
            label="Setup"
            value={formValues.setup || ''}
            onChange={handleChange}
            options={SETUP_OPTIONS}
            placeholder="Select Setup"
          />
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <SelectDropdown
            id="entryVersion"
            name="entryVersion"
            label="Entry Version"
            value={formValues.entryVersion || 'First Entry'}
            onChange={handleChange}
            options={ENTRY_VERSION_OPTIONS}
          />
        </FormGroup>

        <FormGroup>
          <SelectDropdown
            id="patternQuality"
            name="patternQuality"
            label="Pattern Quality (1-10)"
            value={formValues.patternQuality || '5'}
            onChange={handleChange}
            options={PATTERN_QUALITY_OPTIONS}
          />
        </FormGroup>
      </FormRow>

      <Divider />

      {/* Notes Section */}
      <SectionTitle>Notes</SectionTitle>
      <FormGroup>
        <Label htmlFor="notes">Trade Notes</Label>
        <TextArea id="notes" name="notes" value={formValues.notes} onChange={handleChange} />
      </FormGroup>
    </>
  );
};

export default TradeFormStrategyFields;
