/**
 * Trade Form Basic Fields Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 338 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 95% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */

import React from 'react';
import { TradeFormBasicFieldsContainer } from './TradeFormBasicFieldsContainer';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';

interface TradeFormBasicFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
  calculateProfitLoss?: () => void;
  setFormValues?: React.Dispatch<React.SetStateAction<TradeFormValues>>;
}

/**
 * Trade Form Basic Fields Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
const TradeFormBasicFields: React.FC<TradeFormBasicFieldsProps> = (props) => {
  return <TradeFormBasicFieldsContainer {...props} />;
};

export default TradeFormBasicFields;
