/**
 * Trade Form Timing Fields Component
 *
 * Displays the timing fields for the trade form
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { SESSION_OPTIONS, MARKET_OPTIONS } from '../../hooks';
import { TimePicker, SelectDropdown, Input } from '@adhd-trading-dashboard/shared';

// Market session options for the new timing fields
const MARKET_SESSION_OPTIONS = [
  { value: 'pre-market', label: 'Pre-Market (4:00 AM - 9:30 AM ET)' },
  { value: 'regular-hours', label: 'Regular Hours (9:30 AM - 4:00 PM ET)' },
  { value: 'after-hours', label: 'After-Hours (4:00 PM - 8:00 PM ET)' },
  { value: 'extended-hours', label: 'Extended Hours (4:00 AM - 8:00 PM ET)' },
];

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
  background: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;

const TimingSection = styled.div`
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const TimingSectionHeader = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  }
`;

const TimingSectionTitleRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const TimingSectionIcon = styled.div`
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || '#dc2626'}40;
`;

const TimingSectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const TimingSectionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;

const TimingSectionContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const HelpText = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  line-height: 1.4;
`;

const ValidationError = styled.span`
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-weight: 500;
`;

interface TradeFormTimingFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
}

/**
 * Trade Form Timing Fields Component
 */
const TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
}) => {
  return (
    <Container>
      {/* Entry Timing & Date Section */}
      <TimingSection>
        <TimingSectionHeader>
          <TimingSectionTitleRow>
            <TimingSectionIcon>📅</TimingSectionIcon>
            <div>
              <TimingSectionTitle>Entry Timing & Date</TimingSectionTitle>
              <TimingSectionDescription>
                When and during which session the trade was executed
              </TimingSectionDescription>
            </div>
          </TimingSectionTitleRow>
        </TimingSectionHeader>

        <TimingSectionContent>
          <FormRow>
            <FormGroup>
              <Label htmlFor="entryDate">Entry Date</Label>
              <Input
                id="entryDate"
                name="entryDate"
                type="date"
                value={formValues.entryDate || ''}
                onChange={handleChange}
              />
              {validationErrors.entryDate && (
                <ValidationError>{validationErrors.entryDate}</ValidationError>
              )}
              <HelpText>Date when the trade was entered</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="entryTime">Entry Time</Label>
              <TimePicker
                id="entryTime"
                name="entryTime"
                value={formValues.entryTime || ''}
                onChange={handleChange}
              />
              {validationErrors.entryTime && (
                <ValidationError>{validationErrors.entryTime}</ValidationError>
              )}
              <HelpText>Exact time of trade entry</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="marketSession">Market Session</Label>
              <SelectDropdown
                id="marketSession"
                name="marketSession"
                value={formValues.marketSession || ''}
                onChange={handleChange}
                options={MARKET_SESSION_OPTIONS}
                placeholder="Select Market Session"
              />
              {validationErrors.marketSession && (
                <ValidationError>{validationErrors.marketSession}</ValidationError>
              )}
              <HelpText>Trading session when entry occurred</HelpText>
            </FormGroup>
          </FormRow>
        </TimingSectionContent>
      </TimingSection>

      {/* Trade Timing Analysis Section */}
      <TimingSection>
        <TimingSectionHeader>
          <TimingSectionTitleRow>
            <TimingSectionIcon>⏱️</TimingSectionIcon>
            <div>
              <TimingSectionTitle>Trade Timing Analysis</TimingSectionTitle>
              <TimingSectionDescription>
                Key timing points and decision moments during the trade
              </TimingSectionDescription>
            </div>
          </TimingSectionTitleRow>
        </TimingSectionHeader>

        <TimingSectionContent>
          <FormRow>
            <FormGroup>
              <Label htmlFor="rdTime">Risk/Decision Time</Label>
              <TimePicker
                id="rdTime"
                name="rdTime"
                value={formValues.rdTime || ''}
                onChange={handleChange}
              />
              {validationErrors.rdTime && (
                <ValidationError>{validationErrors.rdTime}</ValidationError>
              )}
              <HelpText>Time when risk/decision was made</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="exitTime">Exit Time</Label>
              <TimePicker
                id="exitTime"
                name="exitTime"
                value={formValues.exitTime || ''}
                onChange={handleChange}
              />
              {validationErrors.exitTime && (
                <ValidationError>{validationErrors.exitTime}</ValidationError>
              )}
              <HelpText>Time when trade was exited</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="timingCriteria">Time-based Criteria</Label>
              <Input
                id="timingCriteria"
                name="timingCriteria"
                type="text"
                value={formValues.timingCriteria || ''}
                onChange={handleChange}
                placeholder="e.g., Market open, News release, Key level test"
              />
              {validationErrors.timingCriteria && (
                <ValidationError>{validationErrors.timingCriteria}</ValidationError>
              )}
              <HelpText>Specific timing conditions or criteria</HelpText>
            </FormGroup>
          </FormRow>
        </TimingSectionContent>
      </TimingSection>

      {/* Market Context Section */}
      <TimingSection>
        <TimingSectionHeader>
          <TimingSectionTitleRow>
            <TimingSectionIcon>🌐</TimingSectionIcon>
            <div>
              <TimingSectionTitle>Market Context</TimingSectionTitle>
              <TimingSectionDescription>
                Market environment and conditions during the trade
              </TimingSectionDescription>
            </div>
          </TimingSectionTitleRow>
        </TimingSectionHeader>

        <TimingSectionContent>
          <FormRow>
            <FormGroup>
              <Label htmlFor="session">Session (Time Block)</Label>
              <SelectDropdown
                id="session"
                name="session"
                value={formValues.session || ''}
                onChange={handleChange}
                options={SESSION_OPTIONS}
                placeholder="Select Session"
              />
              <HelpText>Trading session classification</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="market">Market</Label>
              <SelectDropdown
                id="market"
                name="market"
                value={formValues.market || 'Stocks'}
                onChange={handleChange}
                options={MARKET_OPTIONS}
              />
              <HelpText>Market type being traded</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="marketConditions">Market Conditions</Label>
              <Input
                id="marketConditions"
                name="marketConditions"
                type="text"
                value={formValues.marketConditions || ''}
                onChange={handleChange}
                placeholder="e.g., Trending, Ranging, Volatile, Quiet"
              />
              {validationErrors.marketConditions && (
                <ValidationError>{validationErrors.marketConditions}</ValidationError>
              )}
              <HelpText>Overall market conditions during trade</HelpText>
            </FormGroup>
          </FormRow>
        </TimingSectionContent>
      </TimingSection>
    </Container>
  );
};

export default TradeFormTimingFields;
