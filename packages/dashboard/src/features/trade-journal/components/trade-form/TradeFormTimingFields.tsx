/**
 * Trade Form Timing Fields Component
 *
 * Displays the timing fields for the trade form
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { SESSION_OPTIONS, MARKET_OPTIONS } from '../../hooks';
import { TimePicker, SelectDropdown, Input } from '@adhd-trading-dashboard/shared';

// Market session options for the new timing fields
const MARKET_SESSION_OPTIONS = [
  { value: 'pre-market', label: 'Pre-Market (4:00 AM - 9:30 AM ET)' },
  { value: 'regular-hours', label: 'Regular Hours (9:30 AM - 4:00 PM ET)' },
  { value: 'after-hours', label: 'After-Hours (4:00 PM - 8:00 PM ET)' },
  { value: 'extended-hours', label: 'Extended Hours (4:00 AM - 8:00 PM ET)' },
];

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const ValidationError = styled.span`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: 2px;
`;

const SectionSubtitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: ${({ theme }) => theme.spacing.lg} 0 ${({ theme }) => theme.spacing.sm} 0;
  padding-bottom: ${({ theme }) => theme.spacing.xs};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const HelpText = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: 2px;
`;

interface TradeFormTimingFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
}

/**
 * Trade Form Timing Fields Component
 */
const TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
}) => {
  return (
    <>
      {/* Entry Date and Time Section */}
      <SectionSubtitle>Entry Timing & Date</SectionSubtitle>
      <FormRow>
        <FormGroup>
          <Label htmlFor="entryDate">Entry Date</Label>
          <Input
            id="entryDate"
            name="entryDate"
            type="date"
            value={formValues.entryDate || ''}
            onChange={handleChange}
          />
          {validationErrors.entryDate && (
            <ValidationError>{validationErrors.entryDate}</ValidationError>
          )}
          <HelpText>Date when the trade was entered</HelpText>
        </FormGroup>

        <FormGroup>
          <TimePicker
            id="entryTime"
            name="entryTime"
            label="Entry Time"
            value={formValues.entryTime || ''}
            onChange={handleChange}
          />
          {validationErrors.entryTime && (
            <ValidationError>{validationErrors.entryTime}</ValidationError>
          )}
          <HelpText>Exact time of trade entry</HelpText>
        </FormGroup>

        <FormGroup>
          <SelectDropdown
            id="marketSession"
            name="marketSession"
            label="Market Session"
            value={formValues.marketSession || ''}
            onChange={handleChange}
            options={MARKET_SESSION_OPTIONS}
            placeholder="Select Market Session"
          />
          {validationErrors.marketSession && (
            <ValidationError>{validationErrors.marketSession}</ValidationError>
          )}
          <HelpText>Trading session when entry occurred</HelpText>
        </FormGroup>
      </FormRow>

      {/* Trade Timing Analysis Section */}
      <SectionSubtitle>Trade Timing Analysis</SectionSubtitle>
      <FormRow>
        <FormGroup>
          <TimePicker
            id="rdTime"
            name="rdTime"
            label="Risk/Decision Time"
            value={formValues.rdTime || ''}
            onChange={handleChange}
          />
          {validationErrors.rdTime && <ValidationError>{validationErrors.rdTime}</ValidationError>}
          <HelpText>Time when risk/decision was made</HelpText>
        </FormGroup>

        <FormGroup>
          <TimePicker
            id="exitTime"
            name="exitTime"
            label="Exit Time"
            value={formValues.exitTime || ''}
            onChange={handleChange}
          />
          {validationErrors.exitTime && (
            <ValidationError>{validationErrors.exitTime}</ValidationError>
          )}
          <HelpText>Time when trade was exited</HelpText>
        </FormGroup>

        <FormGroup>
          <Label htmlFor="timingCriteria">Time-based Criteria</Label>
          <Input
            id="timingCriteria"
            name="timingCriteria"
            type="text"
            value={formValues.timingCriteria || ''}
            onChange={handleChange}
            placeholder="e.g., Market open, News release, Key level test"
          />
          {validationErrors.timingCriteria && (
            <ValidationError>{validationErrors.timingCriteria}</ValidationError>
          )}
          <HelpText>Specific timing conditions or criteria</HelpText>
        </FormGroup>
      </FormRow>

      {/* Market Context Section */}
      <SectionSubtitle>Market Context</SectionSubtitle>
      <FormRow>
        <FormGroup>
          <SelectDropdown
            id="session"
            name="session"
            label="Session (Time Block)"
            value={formValues.session || ''}
            onChange={handleChange}
            options={SESSION_OPTIONS}
            placeholder="Select Session"
          />
          <HelpText>Trading session classification</HelpText>
        </FormGroup>

        <FormGroup>
          <SelectDropdown
            id="market"
            name="market"
            label="Market"
            value={formValues.market || 'Stocks'}
            onChange={handleChange}
            options={MARKET_OPTIONS}
          />
          <HelpText>Market type being traded</HelpText>
        </FormGroup>

        <FormGroup>
          <Label htmlFor="marketConditions">Market Conditions</Label>
          <Input
            id="marketConditions"
            name="marketConditions"
            type="text"
            value={formValues.marketConditions || ''}
            onChange={handleChange}
            placeholder="e.g., Trending, Ranging, Volatile, Quiet"
          />
          {validationErrors.marketConditions && (
            <ValidationError>{validationErrors.marketConditions}</ValidationError>
          )}
          <HelpText>Overall market conditions during trade</HelpText>
        </FormGroup>
      </FormRow>
    </>
  );
};

export default TradeFormTimingFields;
