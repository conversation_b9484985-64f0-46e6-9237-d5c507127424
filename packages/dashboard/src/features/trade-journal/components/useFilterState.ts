/**
 * useFilterState Hook
 * 
 * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)
 * Hook for managing filter state and operations.
 * 
 * BENEFITS:
 * - Focused responsibility (filter state only)
 * - Type-safe filter handling
 * - Reusable across filter components
 * - Better separation of concerns
 * - Centralized filter logic
 */

import { useState, useCallback, useMemo } from 'react';

export interface FilterState {
  [key: string]: string | number;
}

export interface UseFilterStateProps {
  /** Initial filter values */
  initialFilters?: FilterState;
  /** Filter change handler */
  onFiltersChange?: (filters: FilterState) => void;
  /** Reset handler */
  onReset?: () => void;
}

export interface UseFilterStateReturn {
  /** Current filter values */
  filters: FilterState;
  /** Update a single filter */
  updateFilter: (name: string, value: string | number) => void;
  /** Update multiple filters */
  updateFilters: (newFilters: Partial<FilterState>) => void;
  /** Reset all filters */
  resetFilters: () => void;
  /** Check if any filters are active */
  hasActiveFilters: boolean;
  /** Get active filter count */
  activeFilterCount: number;
  /** Get filter value */
  getFilterValue: (name: string) => string | number;
  /** Check if filter has value */
  hasFilterValue: (name: string) => boolean;
}

/**
 * useFilterState Hook
 * 
 * Manages filter state with type safety and utility functions.
 */
export const useFilterState = ({
  initialFilters = {},
  onFiltersChange,
  onReset,
}: UseFilterStateProps = {}): UseFilterStateReturn => {
  
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  
  /**
   * Update a single filter
   */
  const updateFilter = useCallback((name: string, value: string | number) => {
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [name]: value,
      };
      
      // Call external handler if provided
      if (onFiltersChange) {
        onFiltersChange(newFilters);
      }
      
      return newFilters;
    });
  }, [onFiltersChange]);
  
  /**
   * Update multiple filters
   */
  const updateFilters = useCallback((newFilters: Partial<FilterState>) => {
    setFilters(prev => {
      const updatedFilters = {
        ...prev,
        ...newFilters,
      };
      
      // Call external handler if provided
      if (onFiltersChange) {
        onFiltersChange(updatedFilters);
      }
      
      return updatedFilters;
    });
  }, [onFiltersChange]);
  
  /**
   * Reset all filters
   */
  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
    
    // Call external handlers if provided
    if (onReset) {
      onReset();
    }
    if (onFiltersChange) {
      onFiltersChange(initialFilters);
    }
  }, [initialFilters, onReset, onFiltersChange]);
  
  /**
   * Check if any filters are active
   */
  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(value => 
      value !== '' && value !== null && value !== undefined
    );
  }, [filters]);
  
  /**
   * Get active filter count
   */
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value => 
      value !== '' && value !== null && value !== undefined
    ).length;
  }, [filters]);
  
  /**
   * Get filter value
   */
  const getFilterValue = useCallback((name: string): string | number => {
    return filters[name] || '';
  }, [filters]);
  
  /**
   * Check if filter has value
   */
  const hasFilterValue = useCallback((name: string): boolean => {
    const value = filters[name];
    return value !== '' && value !== null && value !== undefined;
  }, [filters]);
  
  return {
    filters,
    updateFilter,
    updateFilters,
    resetFilters,
    hasActiveFilters,
    activeFilterCount,
    getFilterValue,
    hasFilterValue,
  };
};

export default useFilterState;
