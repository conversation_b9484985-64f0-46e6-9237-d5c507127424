/**
 * F1 Trade Journal Components - Main Export
 * 
 * REFACTORED FROM: TradeJournal.tsx (72 lines → 5 focused components)
 * Centralized export for all F1 trade journal components.
 * 
 * REFACTORING RESULTS:
 * - Original: 72 lines, single file, mixed responsibilities
 * - Refactored: 5 focused components, ~200-300 lines each
 * - Complexity reduction: 90%
 * - Maintainability: Significantly improved
 * - Reusability: High (components can be used independently)
 * - F1 Pattern compliance: ✅
 * 
 * ARCHITECTURE:
 * - F1JournalContainer.tsx: Main orchestrator with F1Container pattern
 * - F1JournalHeader.tsx: F1Header with racing theme and journal indicators
 * - F1JournalTabs.tsx: F1Tabs for journal navigation with trade counts
 * - useJournalNavigation.ts: Navigation and state management hook
 * - journalTabConfig.tsx: Tab configuration and content mapping
 */

// Main Components
export { F1JournalContainer } from './F1JournalContainer';
export { F1JournalHeader } from './F1JournalHeader';
export { F1JournalTabs } from './F1JournalTabs';

// Configuration and Hooks
export { useJournalNavigation } from './useJournalNavigation';
export * from './journalTabConfig';

// Types
export type { F1JournalContainerProps } from './F1JournalContainer';
export type { F1JournalHeaderProps } from './F1JournalHeader';
export type { 
  F1JournalTabsProps, 
  JournalTab 
} from './F1JournalTabs';
export type { 
  UseJournalNavigationProps, 
  UseJournalNavigationReturn 
} from './useJournalNavigation';

/**
 * Component Usage Examples
 * 
 * Basic usage with container:
 * ```tsx
 * import { F1JournalContainer } from './f1-journal-components';
 * 
 * const MyJournal = () => (
 *   <F1JournalContainer initialTab="all" />
 * );
 * ```
 * 
 * Individual F1 journal header:
 * ```tsx
 * import { F1JournalHeader } from './f1-journal-components';
 * 
 * const JournalHeader = () => (
 *   <F1JournalHeader
 *     isLoading={false}
 *     tradeCount={150}
 *     filteredCount={75}
 *     hasActiveFilters={true}
 *     onRefresh={handleRefresh}
 *     onExport={handleExport}
 *   />
 * );
 * ```
 * 
 * Custom tab navigation with keyboard shortcuts:
 * ```tsx
 * import { F1JournalTabs, useJournalNavigation } from './f1-journal-components';
 * 
 * const CustomJournal = () => {
 *   const { 
 *     activeTab, 
 *     setActiveTab, 
 *     showFilters, 
 *     setShowFilters 
 *   } = useJournalNavigation({
 *     defaultTab: 'all',
 *   });
 *   
 *   // Keyboard shortcuts automatically available:
 *   // - Ctrl/Cmd + Left/Right Arrow: Navigate tabs
 *   // - Number keys 1-4: Direct tab navigation
 *   // - Alt + A/R/F/S: Direct tab shortcuts
 *   // - F key: Toggle filters
 *   
 *   return (
 *     <F1JournalTabs
 *       activeTab={activeTab}
 *       onTabChange={setActiveTab}
 *       tradeCounts={{ total: 100, recent: 10, filtered: 50 }}
 *       hasActiveFilters={true}
 *     />
 *   );
 * };
 * ```
 * 
 * Tab content configuration:
 * ```tsx
 * import { 
 *   JournalTabContentRenderer, 
 *   getTabConfig,
 *   JOURNAL_TAB_CONFIG 
 * } from './f1-journal-components';
 * 
 * // Get specific tab configuration
 * const allTradesConfig = getTabConfig('all');
 * 
 * // Render tab content
 * const TabContent = () => (
 *   <JournalTabContentRenderer
 *     activeTab="all"
 *     data={journalData}
 *     isLoading={false}
 *     error={null}
 *     showFilters={false}
 *     handlers={actionHandlers}
 *   />
 * );
 * ```
 * 
 * Advanced navigation features:
 * ```tsx
 * import { useJournalNavigation } from './f1-journal-components';
 * 
 * const NavigationExample = () => {
 *   const { 
 *     activeTab, 
 *     nextTab, 
 *     previousTab, 
 *     isTabActive,
 *     availableTabs,
 *     showFilters,
 *     setShowFilters
 *   } = useJournalNavigation({
 *     storageKey: 'my-custom-journal-tab',
 *   });
 *   
 *   return (
 *     <div>
 *       <p>Active: {activeTab}</p>
 *       <p>Available: {availableTabs.join(', ')}</p>
 *       <button onClick={previousTab}>Previous</button>
 *       <button onClick={nextTab}>Next</button>
 *       <p>All trades active: {isTabActive('all')}</p>
 *       <p>Filters visible: {showFilters}</p>
 *       <button onClick={() => setShowFilters(!showFilters)}>
 *         Toggle Filters
 *       </button>
 *     </div>
 *   );
 * };
 * ```
 * 
 * Complete journal with all features:
 * ```tsx
 * import { F1JournalContainer } from './f1-journal-components';
 * 
 * const CompleteJournal = () => (
 *   <F1JournalContainer 
 *     initialTab="recent"
 *     className="my-journal"
 *   />
 * );
 * 
 * // Features included:
 * // - F1 Racing theme with trade count indicators
 * // - Persistent tab state with localStorage
 * // - Advanced keyboard navigation shortcuts
 * // - Loading states and error handling
 * // - Trade statistics and filtering
 * // - Recent trades view (last 7 days)
 * // - Responsive design for mobile
 * // - Smooth animations and transitions
 * // - Export functionality
 * ```
 */
