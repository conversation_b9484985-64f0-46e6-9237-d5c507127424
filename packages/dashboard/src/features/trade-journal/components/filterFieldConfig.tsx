/**
 * Filter Field Configuration
 * 
 * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)
 * Centralized configuration for all filter fields.
 * 
 * BENEFITS:
 * - Single source of truth for filter definitions
 * - Easy to maintain and extend
 * - Type-safe filter configurations
 * - Eliminates repetitive filter field code
 * - Clear field organization
 */

import { FilterFieldType, FilterOption } from './F1FilterField';

export interface FilterFieldConfig {
  name: string;
  label: string;
  type: FilterFieldType;
  placeholder?: string;
  options?: FilterOption[];
  group: 'basic' | 'trading' | 'analysis' | 'dates';
  order: number;
}

/**
 * Generate numeric options for rating fields
 */
const generateRatingOptions = (min: number = 1, max: number = 10): FilterOption[] => {
  return Array.from({ length: max - min + 1 }, (_, i) => ({
    value: min + i,
    label: (min + i).toString(),
  }));
};

/**
 * Complete filter field configuration
 */
export const FILTER_FIELD_CONFIG: FilterFieldConfig[] = [
  // Basic Filters
  {
    name: 'symbol',
    label: 'Symbol',
    type: 'text',
    placeholder: 'AAPL, MSFT, etc.',
    group: 'basic',
    order: 1,
  },
  {
    name: 'direction',
    label: 'Direction',
    type: 'select',
    options: [
      { value: 'Long', label: 'Long' },
      { value: 'Short', label: 'Short' },
    ],
    group: 'basic',
    order: 2,
  },
  {
    name: 'result',
    label: 'Result',
    type: 'select',
    options: [
      { value: 'win', label: 'Wins' },
      { value: 'loss', label: 'Losses' },
    ],
    group: 'basic',
    order: 3,
  },
  
  // Date Filters
  {
    name: 'dateFrom',
    label: 'From Date',
    type: 'date',
    group: 'dates',
    order: 4,
  },
  {
    name: 'dateTo',
    label: 'To Date',
    type: 'date',
    group: 'dates',
    order: 5,
  },
  
  // Trading Setup Filters
  {
    name: 'setup',
    label: 'Setup',
    type: 'select',
    options: [], // Will be populated dynamically
    group: 'trading',
    order: 6,
  },
  {
    name: 'modelType',
    label: 'Model Type',
    type: 'select',
    options: [], // Will be populated dynamically
    group: 'trading',
    order: 7,
  },
  {
    name: 'primarySetupType',
    label: 'Primary Setup',
    type: 'select',
    options: [], // Will be populated dynamically
    group: 'trading',
    order: 8,
  },
  {
    name: 'secondarySetupType',
    label: 'Secondary Setup',
    type: 'select',
    options: [], // Will be populated dynamically
    group: 'trading',
    order: 9,
  },
  {
    name: 'liquidityTaken',
    label: 'Liquidity Taken',
    type: 'select',
    options: [], // Will be populated dynamically
    group: 'trading',
    order: 10,
  },
  
  // Analysis Filters
  {
    name: 'patternQualityMin',
    label: 'Pattern Quality Min',
    type: 'select',
    options: generateRatingOptions(1, 10),
    group: 'analysis',
    order: 11,
  },
  {
    name: 'patternQualityMax',
    label: 'Pattern Quality Max',
    type: 'select',
    options: generateRatingOptions(1, 10),
    group: 'analysis',
    order: 12,
  },
  {
    name: 'dolType',
    label: 'DOL Type',
    type: 'select',
    options: [], // Will be populated dynamically
    group: 'analysis',
    order: 13,
  },
  {
    name: 'dolEffectivenessMin',
    label: 'DOL Effectiveness Min',
    type: 'select',
    options: generateRatingOptions(1, 10),
    group: 'analysis',
    order: 14,
  },
  {
    name: 'dolEffectivenessMax',
    label: 'DOL Effectiveness Max',
    type: 'select',
    options: generateRatingOptions(1, 10),
    group: 'analysis',
    order: 15,
  },
];

/**
 * Get filter fields by group
 */
export const getFilterFieldsByGroup = (group: FilterFieldConfig['group']): FilterFieldConfig[] => {
  return FILTER_FIELD_CONFIG
    .filter(field => field.group === group)
    .sort((a, b) => a.order - b.order);
};

/**
 * Get all filter groups
 */
export const getFilterGroups = (): FilterFieldConfig['group'][] => {
  return ['basic', 'dates', 'trading', 'analysis'];
};

/**
 * Get filter field by name
 */
export const getFilterField = (name: string): FilterFieldConfig | undefined => {
  return FILTER_FIELD_CONFIG.find(field => field.name === name);
};

/**
 * Update dynamic options for a field
 */
export const updateFieldOptions = (
  fieldName: string, 
  options: FilterOption[]
): FilterFieldConfig[] => {
  return FILTER_FIELD_CONFIG.map(field => 
    field.name === fieldName 
      ? { ...field, options }
      : field
  );
};

/**
 * Group labels for display
 */
export const FILTER_GROUP_LABELS: Record<FilterFieldConfig['group'], string> = {
  basic: 'Basic Filters',
  dates: 'Date Range',
  trading: 'Trading Setup',
  analysis: 'Analysis & Quality',
};

/**
 * Group descriptions
 */
export const FILTER_GROUP_DESCRIPTIONS: Record<FilterFieldConfig['group'], string> = {
  basic: 'Filter by symbol, direction, and trade results',
  dates: 'Filter trades by date range',
  trading: 'Filter by trading setups and strategies',
  analysis: 'Filter by pattern quality and DOL analysis',
};

/**
 * Get field configuration with dynamic options
 */
export const getFieldConfigWithOptions = (
  uniqueData: {
    uniqueSetups: string[];
    uniqueModelTypes: string[];
    uniquePrimarySetupTypes: string[];
    uniqueSecondarySetupTypes: string[];
    uniqueLiquidityTypes: string[];
    uniqueDOLTypes: string[];
  }
): FilterFieldConfig[] => {
  return FILTER_FIELD_CONFIG.map(field => {
    switch (field.name) {
      case 'setup':
        return {
          ...field,
          options: uniqueData.uniqueSetups.map(setup => ({
            value: setup,
            label: setup,
          })),
        };
      case 'modelType':
        return {
          ...field,
          options: uniqueData.uniqueModelTypes.map(type => ({
            value: type,
            label: type,
          })),
        };
      case 'primarySetupType':
        return {
          ...field,
          options: uniqueData.uniquePrimarySetupTypes.map(type => ({
            value: type,
            label: type,
          })),
        };
      case 'secondarySetupType':
        return {
          ...field,
          options: uniqueData.uniqueSecondarySetupTypes.map(type => ({
            value: type,
            label: type,
          })),
        };
      case 'liquidityTaken':
        return {
          ...field,
          options: uniqueData.uniqueLiquidityTypes.map(type => ({
            value: type,
            label: type,
          })),
        };
      case 'dolType':
        return {
          ...field,
          options: uniqueData.uniqueDOLTypes.map(type => ({
            value: type,
            label: type,
          })),
        };
      default:
        return field;
    }
  });
};

export default FILTER_FIELD_CONFIG;
