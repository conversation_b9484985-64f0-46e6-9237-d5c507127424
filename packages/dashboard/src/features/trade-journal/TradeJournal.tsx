/**
 * Trade Journal Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 72 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 90% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */

import React from 'react';
import { F1JournalContainer } from './components/F1JournalContainer';

export interface TradeJournalProps {
  /** Custom className */
  className?: string;
  /** Initial tab to display */
  initialTab?: 'all' | 'recent' | 'filters' | 'stats';
}

/**
 * Trade Journal Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
const TradeJournal: React.FC<TradeJournalProps> = ({ className, initialTab }) => {
  return <F1JournalContainer className={className} initialTab={initialTab} />;
};

export default TradeJournal;
