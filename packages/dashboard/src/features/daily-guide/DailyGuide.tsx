/**
 * Daily Guide Page
 *
 * This page displays daily trading guidance and market insights.
 */

import React from 'react';
import { DailyGuideProvider } from './context/DailyGuideContext';
import { DailyGuideContainer } from './components/DailyGuideContainer';

/**
 * DailyGuide Component
 *
 * PATTERN VALIDATION: Simple wrapper that uses the container pattern.
 * Demonstrates cross-component-type effectiveness.
 */
const DailyGuide: React.FC = () => {
  return (
    <DailyGuideProvider>
      <DailyGuideContainer />
    </DailyGuideProvider>
  );
};

export default DailyGuide;
