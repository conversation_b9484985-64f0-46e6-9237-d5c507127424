/**
 * Daily Guide Page
 *
 * This page displays daily trading guidance and market insights.
 */

import React from 'react';
import styled from 'styled-components';
import { MarketOverview } from './components/MarketOverview';
import { TradingPlan } from './components/TradingPlan';
import { KeyLevels } from './components/KeyLevels';
import { MarketNews } from './components/MarketNews';
import { SectionCard } from './components/ui';
import { DailyGuideHeader } from './components/DailyGuideHeader';
import { DailyGuideProvider, useDailyGuide } from './context/DailyGuideContext';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
  max-width: 1400px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  min-height: 100vh;
`;

const DailyGuideContent: React.FC = () => {
  const { isLoading, error, currentDate, refreshData } = useDailyGuide();

  return (
    <PageContainer>
      {/* F1 Racing Header */}
      <DailyGuideHeader
        currentDate={currentDate}
        onRefresh={refreshData}
        isRefreshing={isLoading}
      />

      <SectionCard
        title="Market Overview"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <MarketOverview />
      </SectionCard>

      <SectionCard
        title="Trading Plan"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <TradingPlan />
      </SectionCard>

      <SectionCard
        title="Key Levels"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <KeyLevels />
      </SectionCard>

      <SectionCard
        title="Market News"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <MarketNews />
      </SectionCard>
    </PageContainer>
  );
};

const DailyGuide: React.FC = () => {
  return (
    <DailyGuideProvider>
      <DailyGuideContent />
    </DailyGuideProvider>
  );
};

export default DailyGuide;
