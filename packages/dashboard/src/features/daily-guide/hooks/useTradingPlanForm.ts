/**
 * useTradingPlanForm Hook
 * 
 * Custom hook for managing trading plan form state and logic.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */

import { useState, useCallback } from 'react';
import { TradingPlanItem, TradingPlanPriority } from '../types';

export interface UseTradingPlanFormReturn {
  /** Whether the add form is currently shown */
  showAddForm: boolean;
  /** Function to show/hide the add form */
  setShowAddForm: (show: boolean) => void;
  /** The new item being created */
  newItem: Omit<TradingPlanItem, 'id'>;
  /** Function to update the new item */
  setNewItem: (item: Omit<TradingPlanItem, 'id'>) => void;
  /** Function to handle form submission */
  handleAddItem: (e: React.FormEvent) => void;
  /** Function to reset the form to initial state */
  resetForm: () => void;
  /** Whether the form is valid */
  isValid: boolean;
}

/**
 * Initial state for a new trading plan item
 */
const getInitialItem = (): Omit<TradingPlanItem, 'id'> => ({
  description: '',
  priority: 'medium',
  completed: false,
});

/**
 * useTradingPlanForm Hook
 * 
 * Manages form state and validation for adding new trading plan items.
 * Provides a clean API for form interactions and state management.
 */
export const useTradingPlanForm = (
  onItemAdd?: (item: TradingPlanItem) => void
): UseTradingPlanFormReturn => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newItem, setNewItem] = useState<Omit<TradingPlanItem, 'id'>>(getInitialItem());

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    setNewItem(getInitialItem());
  }, []);

  /**
   * Handle form submission
   */
  const handleAddItem = useCallback((e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!newItem.description.trim() || !onItemAdd) {
      return;
    }

    // Create the new item with a unique ID
    const itemToAdd: TradingPlanItem = {
      ...newItem,
      id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      description: newItem.description.trim(),
    };

    // Call the callback
    onItemAdd(itemToAdd);

    // Reset form state
    resetForm();
    setShowAddForm(false);
  }, [newItem, onItemAdd, resetForm]);

  /**
   * Check if form is valid
   */
  const isValid = newItem.description.trim().length > 0;

  return {
    showAddForm,
    setShowAddForm,
    newItem,
    setNewItem,
    handleAddItem,
    resetForm,
    isValid,
  };
};

export default useTradingPlanForm;
