/**
 * Trading Types
 * 
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * Trading plan and risk management type definitions.
 * 
 * BENEFITS:
 * - Focused responsibility (trading logic only)
 * - Reusable across trading features
 * - Clear separation of concerns
 * - F1 architectural consistency
 */

/**
 * Trading Plan Priority
 * 
 * Priority levels for trading plan items.
 */
export type TradingPlanPriority = 'high' | 'medium' | 'low';

/**
 * Trading Plan Item
 * 
 * Individual item in the daily trading plan.
 */
export interface TradingPlanItem {
  /** The item ID */
  id: string;
  /** The item description */
  description: string;
  /** The item priority */
  priority: TradingPlanPriority;
  /** Whether the item is completed */
  completed?: boolean;
}

/**
 * Risk Management
 * 
 * Risk management parameters for trading.
 */
export interface RiskManagement {
  /** The maximum risk per trade (percentage) */
  maxRiskPerTrade: number;
  /** The maximum daily loss (dollar amount) */
  maxDailyLoss: number;
  /** The maximum number of trades per day */
  maxTrades: number;
  /** The position sizing strategy */
  positionSizing: string;
}

/**
 * Trading Plan
 * 
 * Complete trading plan for the day.
 */
export interface TradingPlan {
  /** The trading plan items */
  items: TradingPlanItem[];
  /** The overall strategy for the day */
  strategy: string;
  /** The risk management plan */
  riskManagement: RiskManagement;
  /** Notes for the day */
  notes: string;
}

/**
 * Default Risk Management Settings
 * 
 * Provides sensible defaults for risk management.
 */
export const DEFAULT_RISK_MANAGEMENT: RiskManagement = {
  maxRiskPerTrade: 1, // 1% per trade
  maxDailyLoss: 500, // $500 daily loss limit
  maxTrades: 5, // Maximum 5 trades per day
  positionSizing: 'fixed-dollar', // Fixed dollar amount per trade
};

/**
 * Trading Plan Validation
 * 
 * Validates a trading plan for completeness.
 */
export function validateTradingPlan(plan: Partial<TradingPlan>): string[] {
  const errors: string[] = [];
  
  if (!plan.strategy?.trim()) {
    errors.push('Trading strategy is required');
  }
  
  if (!plan.items || plan.items.length === 0) {
    errors.push('At least one trading plan item is required');
  }
  
  if (plan.riskManagement) {
    if (plan.riskManagement.maxRiskPerTrade <= 0 || plan.riskManagement.maxRiskPerTrade > 10) {
      errors.push('Max risk per trade must be between 0.1% and 10%');
    }
    
    if (plan.riskManagement.maxDailyLoss <= 0) {
      errors.push('Max daily loss must be greater than 0');
    }
    
    if (plan.riskManagement.maxTrades <= 0 || plan.riskManagement.maxTrades > 20) {
      errors.push('Max trades must be between 1 and 20');
    }
  }
  
  return errors;
}

/**
 * Trading Plan Item Factory
 * 
 * Creates a new trading plan item with defaults.
 */
export function createTradingPlanItem(
  description: string,
  priority: TradingPlanPriority = 'medium'
): TradingPlanItem {
  return {
    id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    description,
    priority,
    completed: false,
  };
}

/**
 * Risk Management Calculator
 * 
 * Calculates position size based on risk management rules.
 */
export function calculatePositionSize(
  accountBalance: number,
  entryPrice: number,
  stopLoss: number,
  riskManagement: RiskManagement
): number {
  const riskAmount = accountBalance * (riskManagement.maxRiskPerTrade / 100);
  const riskPerShare = Math.abs(entryPrice - stopLoss);
  
  if (riskPerShare === 0) return 0;
  
  return Math.floor(riskAmount / riskPerShare);
}
