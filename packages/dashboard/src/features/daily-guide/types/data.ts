/**
 * Data Types
 * 
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * Main data structures and state management types.
 * 
 * BENEFITS:
 * - Clear data flow architecture
 * - Centralized state management types
 * - Better TypeScript inference
 * - F1 container pattern compliance
 */

import type { MarketOverview, KeyPriceLevel, WatchlistItem, MarketNewsItem } from './market';
import type { TradingPlan } from './trading';

/**
 * Daily Guide Data
 * 
 * Complete data structure for the daily guide feature.
 */
export interface DailyGuideData {
  /** The market overview */
  marketOverview: MarketOverview | null;
  /** The trading plan */
  tradingPlan: TradingPlan | null;
  /** The key price levels */
  keyPriceLevels: KeyPriceLevel[];
  /** The watchlist */
  watchlist: WatchlistItem[];
  /** Market news items */
  marketNews: MarketNewsItem[];
}

/**
 * Daily Guide State
 * 
 * State management for the daily guide feature.
 * Follows F1 container pattern for state management.
 */
export interface DailyGuideState {
  /** The daily guide data */
  data: DailyGuideData;
  /** Whether the data is loading */
  isLoading: boolean;
  /** Any error that occurred */
  error: string | null;
  /** The selected date (ISO string) */
  selectedDate: string;
}

/**
 * Daily Guide Actions
 * 
 * Available actions for daily guide state management.
 */
export interface DailyGuideActions {
  /** Load data for a specific date */
  loadData: (date: string) => Promise<void>;
  /** Refresh current data */
  refreshData: () => Promise<void>;
  /** Update trading plan */
  updateTradingPlan: (plan: TradingPlan) => Promise<void>;
  /** Add watchlist item */
  addWatchlistItem: (item: WatchlistItem) => Promise<void>;
  /** Remove watchlist item */
  removeWatchlistItem: (symbol: string) => Promise<void>;
  /** Clear error state */
  clearError: () => void;
  /** Set selected date */
  setSelectedDate: (date: string) => void;
}

/**
 * Daily Guide Context
 * 
 * Context type for the daily guide provider.
 * Follows F1 context pattern.
 */
export interface DailyGuideContext {
  /** Current state */
  state: DailyGuideState;
  /** Available actions */
  actions: DailyGuideActions;
}

/**
 * Default Daily Guide Data
 * 
 * Provides empty/default data structure.
 */
export const DEFAULT_DAILY_GUIDE_DATA: DailyGuideData = {
  marketOverview: null,
  tradingPlan: null,
  keyPriceLevels: [],
  watchlist: [],
  marketNews: [],
};

/**
 * Default Daily Guide State
 * 
 * Provides initial state for the daily guide.
 */
export const DEFAULT_DAILY_GUIDE_STATE: DailyGuideState = {
  data: DEFAULT_DAILY_GUIDE_DATA,
  isLoading: false,
  error: null,
  selectedDate: new Date().toISOString().split('T')[0], // Today's date
};

/**
 * Data Loading Status
 * 
 * Represents the loading status of different data sections.
 */
export interface DataLoadingStatus {
  /** Market overview loading status */
  marketOverview: boolean;
  /** Trading plan loading status */
  tradingPlan: boolean;
  /** Key levels loading status */
  keyLevels: boolean;
  /** Watchlist loading status */
  watchlist: boolean;
  /** Market news loading status */
  marketNews: boolean;
}

/**
 * Data Validation Result
 * 
 * Result of validating daily guide data.
 */
export interface DataValidationResult {
  /** Whether the data is valid */
  isValid: boolean;
  /** Validation errors */
  errors: string[];
  /** Validation warnings */
  warnings: string[];
}

/**
 * Validate Daily Guide Data
 * 
 * Validates the completeness and consistency of daily guide data.
 */
export function validateDailyGuideData(data: DailyGuideData): DataValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check market overview
  if (!data.marketOverview) {
    warnings.push('Market overview is not available');
  } else {
    if (!data.marketOverview.summary?.trim()) {
      warnings.push('Market summary is empty');
    }
    if (data.marketOverview.indices.length === 0) {
      warnings.push('No market indices data available');
    }
  }
  
  // Check trading plan
  if (!data.tradingPlan) {
    warnings.push('Trading plan is not set');
  } else {
    if (!data.tradingPlan.strategy?.trim()) {
      errors.push('Trading strategy is required');
    }
    if (data.tradingPlan.items.length === 0) {
      warnings.push('No trading plan items defined');
    }
  }
  
  // Check watchlist
  if (data.watchlist.length === 0) {
    warnings.push('Watchlist is empty');
  }
  
  // Check for duplicate symbols in watchlist
  const symbols = data.watchlist.map(item => item.symbol);
  const duplicates = symbols.filter((symbol, index) => symbols.indexOf(symbol) !== index);
  if (duplicates.length > 0) {
    errors.push(`Duplicate symbols in watchlist: ${duplicates.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
