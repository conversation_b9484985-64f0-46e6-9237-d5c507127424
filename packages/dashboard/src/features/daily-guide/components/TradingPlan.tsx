/**
 * Trading Plan Component
 *
 * REFACTORED: Now uses focused components following the proven
 * TradeAnalysis/TradingDashboard architecture pattern.
 */
import React from 'react';
import { TradingPlan as TradingPlanType, TradingPlanItem } from '../types';
import { TradingPlanContainer } from './TradingPlanContainer';

export interface TradingPlanProps {
  /** The trading plan data */
  tradingPlan: TradingPlanType | null;
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when a trading plan item is toggled */
  onItemToggle?: (id: string, completed: boolean) => void;
  /** Function called when a trading plan item is added */
  onItemAdd?: (item: TradingPlanItem) => void;
  /** Function called when a trading plan item is removed */
  onItemRemove?: (id: string) => void;
  /** Additional class name */
  className?: string;
}

/**
 * Main Trading Plan Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
export const TradingPlan: React.FC<TradingPlanProps> = (props) => {
  return <TradingPlanContainer {...props} />;
};
