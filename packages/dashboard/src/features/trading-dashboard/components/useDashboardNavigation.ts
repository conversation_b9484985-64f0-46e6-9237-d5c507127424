/**
 * useDashboardNavigation Hook
 * 
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * Hook for managing dashboard navigation and tab state.
 * 
 * BENEFITS:
 * - Focused responsibility (navigation only)
 * - Persistent tab state with localStorage
 * - Type-safe navigation handling
 * - Reusable across dashboard components
 * - Better separation of concerns
 */

import { useState, useCallback, useEffect } from 'react';
import { DashboardTab } from './F1DashboardTabs';

export interface UseDashboardNavigationProps {
  /** Default tab to show */
  defaultTab?: DashboardTab;
  /** Storage key for persistence */
  storageKey?: string;
}

export interface UseDashboardNavigationReturn {
  /** Current active tab */
  activeTab: DashboardTab;
  /** Change active tab */
  setActiveTab: (tab: DashboardTab) => void;
  /** Navigate to next tab */
  nextTab: () => void;
  /** Navigate to previous tab */
  previousTab: () => void;
  /** Check if tab is active */
  isTabActive: (tab: DashboardTab) => boolean;
  /** Get tab index */
  getTabIndex: (tab: DashboardTab) => number;
  /** Get all available tabs */
  availableTabs: DashboardTab[];
}

/**
 * Available tabs in order
 */
const AVAILABLE_TABS: DashboardTab[] = ['summary', 'trades', 'setups', 'analytics'];

/**
 * Default storage key
 */
const DEFAULT_STORAGE_KEY = 'adhd-trading-dashboard:dashboard:active-tab';

/**
 * Load tab from localStorage
 */
const loadTabFromStorage = (storageKey: string, defaultTab: DashboardTab): DashboardTab => {
  try {
    const stored = localStorage.getItem(storageKey);
    if (stored && AVAILABLE_TABS.includes(stored as DashboardTab)) {
      return stored as DashboardTab;
    }
  } catch (error) {
    console.warn('Failed to load dashboard tab from localStorage:', error);
  }
  return defaultTab;
};

/**
 * Save tab to localStorage
 */
const saveTabToStorage = (storageKey: string, tab: DashboardTab): void => {
  try {
    localStorage.setItem(storageKey, tab);
  } catch (error) {
    console.warn('Failed to save dashboard tab to localStorage:', error);
  }
};

/**
 * useDashboardNavigation Hook
 * 
 * Manages tab navigation state with persistence and keyboard navigation.
 */
export const useDashboardNavigation = ({
  defaultTab = 'summary',
  storageKey = DEFAULT_STORAGE_KEY,
}: UseDashboardNavigationProps = {}): UseDashboardNavigationReturn => {
  
  // Initialize active tab from storage or default
  const [activeTab, setActiveTabState] = useState<DashboardTab>(() =>
    loadTabFromStorage(storageKey, defaultTab)
  );
  
  /**
   * Set active tab with persistence
   */
  const setActiveTab = useCallback((tab: DashboardTab) => {
    if (AVAILABLE_TABS.includes(tab)) {
      setActiveTabState(tab);
      saveTabToStorage(storageKey, tab);
    }
  }, [storageKey]);
  
  /**
   * Navigate to next tab
   */
  const nextTab = useCallback(() => {
    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);
    const nextIndex = (currentIndex + 1) % AVAILABLE_TABS.length;
    setActiveTab(AVAILABLE_TABS[nextIndex]);
  }, [activeTab, setActiveTab]);
  
  /**
   * Navigate to previous tab
   */
  const previousTab = useCallback(() => {
    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);
    const previousIndex = currentIndex === 0 ? AVAILABLE_TABS.length - 1 : currentIndex - 1;
    setActiveTab(AVAILABLE_TABS[previousIndex]);
  }, [activeTab, setActiveTab]);
  
  /**
   * Check if tab is active
   */
  const isTabActive = useCallback((tab: DashboardTab): boolean => {
    return activeTab === tab;
  }, [activeTab]);
  
  /**
   * Get tab index
   */
  const getTabIndex = useCallback((tab: DashboardTab): number => {
    return AVAILABLE_TABS.indexOf(tab);
  }, []);
  
  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle if no input is focused
      if (document.activeElement?.tagName === 'INPUT' || 
          document.activeElement?.tagName === 'TEXTAREA' ||
          document.activeElement?.tagName === 'SELECT') {
        return;
      }
      
      // Handle Ctrl/Cmd + Arrow keys for tab navigation
      if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault();
            previousTab();
            break;
          case 'ArrowRight':
            event.preventDefault();
            nextTab();
            break;
        }
      }
      
      // Handle number keys for direct tab navigation
      if (event.key >= '1' && event.key <= '4' && !event.ctrlKey && !event.metaKey) {
        const tabIndex = parseInt(event.key) - 1;
        if (tabIndex < AVAILABLE_TABS.length) {
          event.preventDefault();
          setActiveTab(AVAILABLE_TABS[tabIndex]);
        }
      }
      
      // Handle Alt + Tab keys for dashboard-specific navigation
      if (event.altKey && !event.ctrlKey && !event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 's':
            event.preventDefault();
            setActiveTab('summary');
            break;
          case 't':
            event.preventDefault();
            setActiveTab('trades');
            break;
          case 'u':
            event.preventDefault();
            setActiveTab('setups');
            break;
          case 'a':
            event.preventDefault();
            setActiveTab('analytics');
            break;
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [nextTab, previousTab, setActiveTab]);
  
  return {
    activeTab,
    setActiveTab,
    nextTab,
    previousTab,
    isTabActive,
    getTabIndex,
    availableTabs: AVAILABLE_TABS,
  };
};

export default useDashboardNavigation;
