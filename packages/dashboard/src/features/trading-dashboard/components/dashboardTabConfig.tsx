/**
 * Dashboard Tab Configuration
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * Centralized configuration for dashboard tabs and their content.
 *
 * BENEFITS:
 * - Single source of truth for tab definitions
 * - Easy to maintain and extend
 * - Type-safe tab configurations
 * - Reusable across different dashboard views
 * - Clear content mapping
 */

import React from 'react';
import { DashboardTab } from './F1DashboardTabs';
import MetricsPanel from '../components/MetricsPanel';
import PerformanceChart from '../components/PerformanceChart';
import RecentTradesTable from '../components/RecentTradesTable';
import SetupAnalysis from '../components/SetupAnalysis';
import TradeFormBasicFields from '../../trade-journal/components/trade-form/TradeFormBasicFields';
import styled from 'styled-components';

export interface DashboardTabConfig {
  id: DashboardTab;
  title: string;
  description: string;
  icon: string;
  component: React.ComponentType<any>;
  showInMobile: boolean;
  requiresData: boolean;
}

export interface DashboardTabContentProps {
  /** Current active tab */
  activeTab: DashboardTab;
  /** Dashboard data */
  data: {
    trades: any[];
    performanceMetrics: any[];
    chartData: any[];
    setupPerformance: any[];
    sessionPerformance: any[];
  };
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Trade form values for analytics tab */
  tradeFormValues?: any;
  /** Trade form change handler */
  handleTradeFormChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
}

// Styled components for analytics layout
const AnalyticsContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};

  @media (max-width: ${({ theme }) => theme.breakpoints?.lg || '1024px'}) {
    grid-template-columns: 1fr;
  }
`;

const ChartsSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const TradeFormSection = styled.div`
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  height: fit-content;
`;

const FormTitle = styled.h3`
  margin: 0 0 ${({ theme }) => theme.spacing?.lg || '24px'} 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
`;

/**
 * Summary Tab Content
 */
const SummaryTabContent: React.FC<DashboardTabContentProps> = ({ data, isLoading }) => {
  return (
    <>
      <MetricsPanel metrics={data.performanceMetrics} isLoading={isLoading} />
      <PerformanceChart data={data.chartData} isLoading={isLoading} />
      <RecentTradesTable trades={data.trades.slice(0, 5)} isLoading={isLoading} />
    </>
  );
};

/**
 * Trades Tab Content
 */
const TradesTabContent: React.FC<DashboardTabContentProps> = ({ data, isLoading }) => {
  return <RecentTradesTable trades={data.trades} isLoading={isLoading} />;
};

/**
 * Setups Tab Content
 */
const SetupsTabContent: React.FC<DashboardTabContentProps> = ({ data, isLoading }) => {
  return (
    <SetupAnalysis
      setupPerformance={data.setupPerformance}
      sessionPerformance={data.sessionPerformance}
      isLoading={isLoading}
    />
  );
};

/**
 * Analytics Tab Content
 */
const AnalyticsTabContent: React.FC<DashboardTabContentProps> = ({
  data,
  isLoading,
  tradeFormValues,
  handleTradeFormChange,
}) => {
  return (
    <AnalyticsContainer>
      <ChartsSection>
        <MetricsPanel metrics={data.performanceMetrics} isLoading={isLoading} />
        <PerformanceChart data={data.chartData} isLoading={isLoading} />
        <SetupAnalysis
          setupPerformance={data.setupPerformance}
          sessionPerformance={data.sessionPerformance}
          isLoading={isLoading}
        />
      </ChartsSection>

      <TradeFormSection>
        <FormTitle>🏎️ Quick Trade Entry</FormTitle>
        {tradeFormValues && handleTradeFormChange && (
          <TradeFormBasicFields
            formValues={tradeFormValues}
            handleChange={handleTradeFormChange}
            validationErrors={{}}
          />
        )}
      </TradeFormSection>
    </AnalyticsContainer>
  );
};

/**
 * Tab configuration with components and metadata
 */
export const DASHBOARD_TAB_CONFIG: Record<DashboardTab, DashboardTabConfig> = {
  summary: {
    id: 'summary',
    title: 'Performance Summary',
    description: 'Overview of trading performance and key metrics',
    icon: '📊',
    component: SummaryTabContent,
    showInMobile: true,
    requiresData: true,
  },
  trades: {
    id: 'trades',
    title: 'Recent Trades',
    description: 'Complete list of recent trading activity',
    icon: '📋',
    component: TradesTabContent,
    showInMobile: true,
    requiresData: true,
  },
  setups: {
    id: 'setups',
    title: 'Setup Analysis',
    description: 'Performance breakdown by trading setups and sessions',
    icon: '🎯',
    component: SetupsTabContent,
    showInMobile: true,
    requiresData: true,
  },
  analytics: {
    id: 'analytics',
    title: 'Advanced Analytics',
    description: 'Comprehensive analytics with quick trade entry',
    icon: '📈',
    component: AnalyticsTabContent,
    showInMobile: false,
    requiresData: true,
  },
};

/**
 * Get tab configuration by ID
 */
export const getTabConfig = (tabId: DashboardTab): DashboardTabConfig => {
  return DASHBOARD_TAB_CONFIG[tabId];
};

/**
 * Get all tab configurations
 */
export const getAllTabConfigs = (): DashboardTabConfig[] => {
  return Object.values(DASHBOARD_TAB_CONFIG);
};

/**
 * Get mobile-friendly tabs
 */
export const getMobileTabConfigs = (): DashboardTabConfig[] => {
  return getAllTabConfigs().filter((config) => config.showInMobile);
};

/**
 * Get tabs that require data
 */
export const getDataRequiredTabConfigs = (): DashboardTabConfig[] => {
  return getAllTabConfigs().filter((config) => config.requiresData);
};

/**
 * Tab Content Renderer Component
 */
export const DashboardTabContentRenderer: React.FC<DashboardTabContentProps> = (props) => {
  const { activeTab } = props;
  const config = getTabConfig(activeTab);

  if (!config) {
    return (
      <div
        style={{
          padding: '48px',
          textAlign: 'center',
          color: '#ef4444',
        }}
      >
        ❌ Unknown tab: {activeTab}
      </div>
    );
  }

  const TabComponent = config.component;

  return (
    <div
      id={`dashboard-panel-${activeTab}`}
      role="tabpanel"
      aria-labelledby={`dashboard-tab-${activeTab}`}
    >
      <TabComponent {...props} />
    </div>
  );
};

export default DashboardTabContentRenderer;
