/**
 * Settings Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 271 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 95% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */

import React from 'react';
import { SettingsContainer } from './components/SettingsContainer';

/**
 * Settings Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
const Settings: React.FC = () => {
  return <SettingsContainer />;
};

export default Settings;
