/**
 * useSettingsForm Hook
 * 
 * REFACTORED FROM: useSettings.ts (enhanced with form management)
 * Enhanced hook for managing settings form state, validation, and persistence.
 * 
 * BENEFITS:
 * - Focused responsibility (form management only)
 * - Built-in validation and error handling
 * - Optimistic updates with rollback capability
 * - Local storage persistence
 * - Change tracking for unsaved state
 */

import { useState, useCallback, useEffect, useMemo } from 'react';
import { useTheme } from '@adhd-trading-dashboard/shared';

export interface SettingsFormData {
  theme: string;
  refreshInterval: number;
  showNotifications: boolean;
  enableAdvancedMetrics: boolean;
  autoSaveJournal: boolean;
}

export interface ValidationErrors {
  [key: string]: string;
}

export interface UseSettingsFormReturn {
  /** Current form data */
  data: SettingsFormData;
  /** Original saved data */
  savedData: SettingsFormData;
  /** Whether form has unsaved changes */
  hasUnsavedChanges: boolean;
  /** Validation errors */
  errors: ValidationErrors;
  /** Whether form is valid */
  isValid: boolean;
  /** Whether save operation is in progress */
  isSaving: boolean;
  /** Change handler */
  handleChange: (name: string, value: any) => void;
  /** Save handler */
  handleSave: () => Promise<void>;
  /** Reset handler */
  handleReset: () => void;
  /** Validate specific field */
  validateField: (name: string, value: any) => string | null;
  /** Validate entire form */
  validateForm: (formData: SettingsFormData) => ValidationErrors;
}

const STORAGE_KEY = 'adhd-trading-dashboard:settings';

/**
 * Default settings values
 */
const DEFAULT_SETTINGS: SettingsFormData = {
  theme: 'f1',
  refreshInterval: 5,
  showNotifications: true,
  enableAdvancedMetrics: false,
  autoSaveJournal: true,
};

/**
 * Load settings from localStorage
 */
const loadSettings = (): SettingsFormData => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      return { ...DEFAULT_SETTINGS, ...parsed };
    }
  } catch (error) {
    console.warn('Failed to load settings from localStorage:', error);
  }
  return DEFAULT_SETTINGS;
};

/**
 * Save settings to localStorage
 */
const saveSettings = (settings: SettingsFormData): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Failed to save settings to localStorage:', error);
    throw new Error('Failed to save settings');
  }
};

/**
 * Validation rules
 */
const validateField = (name: string, value: any): string | null => {
  switch (name) {
    case 'theme':
      if (!value || typeof value !== 'string') {
        return 'Theme is required';
      }
      if (!['f1', 'light', 'dark'].includes(value)) {
        return 'Invalid theme selection';
      }
      return null;
      
    case 'refreshInterval':
      if (typeof value !== 'number' || isNaN(value)) {
        return 'Refresh interval must be a number';
      }
      if (value < 1 || value > 60) {
        return 'Refresh interval must be between 1 and 60 minutes';
      }
      return null;
      
    case 'showNotifications':
    case 'enableAdvancedMetrics':
    case 'autoSaveJournal':
      if (typeof value !== 'boolean') {
        return 'Invalid boolean value';
      }
      return null;
      
    default:
      return null;
  }
};

/**
 * Validate entire form
 */
const validateForm = (formData: SettingsFormData): ValidationErrors => {
  const errors: ValidationErrors = {};
  
  Object.entries(formData).forEach(([name, value]) => {
    const error = validateField(name, value);
    if (error) {
      errors[name] = error;
    }
  });
  
  return errors;
};

/**
 * useSettingsForm Hook
 * 
 * Enhanced settings management with form validation and persistence.
 */
export const useSettingsForm = (): UseSettingsFormReturn => {
  const { setTheme } = useTheme();
  
  // Load initial settings
  const [savedData, setSavedData] = useState<SettingsFormData>(() => loadSettings());
  const [data, setData] = useState<SettingsFormData>(savedData);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSaving, setIsSaving] = useState(false);
  
  // Check for unsaved changes
  const hasUnsavedChanges = useMemo(() => {
    return JSON.stringify(data) !== JSON.stringify(savedData);
  }, [data, savedData]);
  
  // Check if form is valid
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);
  
  /**
   * Handle field changes with validation
   */
  const handleChange = useCallback((name: string, value: any) => {
    setData(prev => ({ ...prev, [name]: value }));
    
    // Validate field and update errors
    const fieldError = validateField(name, value);
    setErrors(prev => {
      const newErrors = { ...prev };
      if (fieldError) {
        newErrors[name] = fieldError;
      } else {
        delete newErrors[name];
      }
      return newErrors;
    });
    
    // Apply theme change immediately for preview
    if (name === 'theme') {
      setTheme(value);
    }
  }, [setTheme]);
  
  /**
   * Save settings
   */
  const handleSave = useCallback(async () => {
    // Validate entire form
    const formErrors = validateForm(data);
    setErrors(formErrors);
    
    if (Object.keys(formErrors).length > 0) {
      throw new Error('Please fix validation errors before saving');
    }
    
    setIsSaving(true);
    
    try {
      // Simulate async save operation
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Save to localStorage
      saveSettings(data);
      
      // Update saved data state
      setSavedData(data);
      
      // Apply theme change
      setTheme(data.theme);
      
      console.log('Settings saved successfully:', data);
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [data, setTheme]);
  
  /**
   * Reset to saved state
   */
  const handleReset = useCallback(() => {
    setData(savedData);
    setErrors({});
    
    // Reset theme to saved value
    setTheme(savedData.theme);
  }, [savedData, setTheme]);
  
  // Validate form on data changes
  useEffect(() => {
    const formErrors = validateForm(data);
    setErrors(formErrors);
  }, [data]);
  
  return {
    data,
    savedData,
    hasUnsavedChanges,
    errors,
    isValid,
    isSaving,
    handleChange,
    handleSave,
    handleReset,
    validateField,
    validateForm,
  };
};

export default useSettingsForm;
