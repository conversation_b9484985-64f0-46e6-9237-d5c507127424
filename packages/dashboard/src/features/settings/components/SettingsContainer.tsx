/**
 * SettingsContainer Component
 * 
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * Main orchestrator for the settings page with F1 container pattern.
 * 
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */

import React, { Suspense, useState } from 'react';
import styled from 'styled-components';
import { SettingsHeader } from './SettingsHeader';
import { SettingsForm } from './SettingsForm';
import { useSettingsForm } from '../hooks/useSettingsForm';

export interface SettingsContainerProps {
  /** Custom className */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;

const ContentWrapper = styled.div`
  flex: 1;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.xl || '32px'} ${({ theme }) => theme.spacing?.lg || '24px'};
  
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing?.lg || '24px'} ${({ theme }) => theme.spacing?.md || '12px'};
  }
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '32px'};
  text-align: center;
  min-height: 200px;
`;

const LoadingIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.3; }
  }
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '32px'};
  text-align: center;
  min-height: 200px;
  background: ${({ theme }) => theme.colors?.error || '#ef4444'}10;
  border: 1px solid ${({ theme }) => theme.colors?.error || '#ef4444'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
`;

const ErrorIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
`;

const ErrorTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const ErrorMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
  max-width: 400px;
`;

const RetryButton = styled.button`
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#b91c1c'};
    transform: translateY(-1px);
  }
`;

const SuccessNotification = styled.div<{ $visible: boolean }>`
  position: fixed;
  top: 20px;
  right: 20px;
  background: ${({ theme }) => theme.colors?.success || '#22c55e'};
  color: white;
  padding: ${({ theme }) => theme.spacing?.md || '12px'} ${({ theme }) => theme.spacing?.lg || '24px'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateX(${({ $visible }) => $visible ? '0' : '100%'});
  opacity: ${({ $visible }) => $visible ? '1' : '0'};
  transition: all 0.3s ease;
  z-index: 1000;
`;

/**
 * LoadingFallback Component
 */
const LoadingFallback: React.FC = () => (
  <LoadingState>
    <LoadingIcon>⚙️</LoadingIcon>
    <LoadingText>Loading Settings...</LoadingText>
  </LoadingState>
);

/**
 * ErrorFallback Component
 */
const ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
  <ErrorState>
    <ErrorIcon>⚠️</ErrorIcon>
    <ErrorTitle>Settings Error</ErrorTitle>
    <ErrorMessage>{error}</ErrorMessage>
    <RetryButton onClick={onRetry}>
      Try Again
    </RetryButton>
  </ErrorState>
);

/**
 * SettingsContent Component
 */
const SettingsContent: React.FC = () => {
  const {
    data,
    hasUnsavedChanges,
    errors,
    isSaving,
    handleChange,
    handleSave,
    handleReset,
  } = useSettingsForm();
  
  const [saveError, setSaveError] = useState<string | null>(null);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  
  const handleSaveWithNotification = async () => {
    try {
      setSaveError(null);
      await handleSave();
      
      // Show success notification
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 3000);
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save settings');
    }
  };
  
  const handleRetry = () => {
    setSaveError(null);
  };
  
  if (saveError) {
    return <ErrorFallback error={saveError} onRetry={handleRetry} />;
  }
  
  return (
    <>
      <SettingsHeader
        hasUnsavedChanges={hasUnsavedChanges}
        onSave={handleSaveWithNotification}
        onReset={handleReset}
      />
      
      <SettingsForm
        data={data}
        onChange={handleChange}
        errors={errors}
        disabled={isSaving}
      />
      
      <SuccessNotification $visible={showSuccessNotification}>
        ✅ Settings saved successfully!
      </SuccessNotification>
    </>
  );
};

/**
 * SettingsContainer Component
 * 
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const SettingsContainer: React.FC<SettingsContainerProps> = ({
  className,
}) => {
  return (
    <Container className={className}>
      <ContentWrapper>
        <Suspense fallback={<LoadingFallback />}>
          <SettingsContent />
        </Suspense>
      </ContentWrapper>
    </Container>
  );
};

export default SettingsContainer;
