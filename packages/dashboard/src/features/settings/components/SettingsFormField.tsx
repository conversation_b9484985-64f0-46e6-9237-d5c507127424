/**
 * SettingsFormField Component
 * 
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * Reusable form field component with F1 racing theme.
 * 
 * BENEFITS:
 * - Focused responsibility (single form field)
 * - Reusable across different settings
 * - F1 racing theme with consistent styling
 * - Built-in validation and error handling
 * - Accessible form controls
 */

import React from 'react';
import styled from 'styled-components';

export type FieldType = 'text' | 'number' | 'select' | 'toggle' | 'textarea';

export interface FieldOption {
  value: string | number;
  label: string;
}

export interface SettingsFormFieldProps {
  /** Field identifier */
  name: string;
  /** Field label */
  label: string;
  /** Field description */
  description?: string;
  /** Field type */
  type: FieldType;
  /** Current value */
  value: any;
  /** Change handler */
  onChange: (name: string, value: any) => void;
  /** Options for select fields */
  options?: FieldOption[];
  /** Input props for text/number fields */
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  /** Validation error */
  error?: string;
  /** Whether field is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const FieldContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.md || '12px'} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  
  &:last-child {
    border-bottom: none;
  }
`;

const FieldRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${({ theme }) => theme.spacing?.md || '12px'};
  }
`;

const LabelSection = styled.div`
  flex: 1;
  min-width: 0;
`;

const FieldLabel = styled.label`
  display: block;
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
  cursor: pointer;
`;

const FieldDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
  line-height: 1.4;
`;

const ControlSection = styled.div`
  flex-shrink: 0;
  min-width: 120px;
  
  @media (max-width: 768px) {
    min-width: 0;
  }
`;

const Input = styled.input<{ $hasError?: boolean }>`
  width: 100%;
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};
  border: 1px solid ${({ theme, $hasError }) => 
    $hasError 
      ? theme.colors?.error || '#ef4444'
      : theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Select = styled.select<{ $hasError?: boolean }>`
  width: 100%;
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};
  border: 1px solid ${({ theme, $hasError }) => 
    $hasError 
      ? theme.colors?.error || '#ef4444'
      : theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ToggleContainer = styled.label`
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  cursor: pointer;
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;
  
  &:checked + span {
    background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  }
  
  &:checked + span:before {
    transform: translateX(24px);
  }
  
  &:focus + span {
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  }
`;

const ToggleSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ theme }) => theme.colors?.border || '#4b5563'};
  transition: all 0.3s ease;
  border-radius: 28px;
  
  &:before {
    position: absolute;
    content: '';
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background: white;
    transition: all 0.3s ease;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;

const ErrorMessage = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-weight: 500;
`;

/**
 * SettingsFormField Component
 * 
 * PATTERN: F1 Form Field Pattern
 * - Racing-inspired styling with red accents
 * - Consistent form controls across field types
 * - Built-in validation and error states
 * - Accessible with proper labels and focus
 * - Responsive design for mobile
 */
export const SettingsFormField: React.FC<SettingsFormFieldProps> = ({
  name,
  label,
  description,
  type,
  value,
  onChange,
  options = [],
  inputProps = {},
  error,
  disabled = false,
  className,
}) => {
  const fieldId = `settings-field-${name}`;
  
  const handleChange = (newValue: any) => {
    if (!disabled) {
      onChange(name, newValue);
    }
  };
  
  const renderControl = () => {
    switch (type) {
      case 'text':
      case 'number':
        return (
          <Input
            id={fieldId}
            type={type}
            value={value || ''}
            onChange={(e) => handleChange(
              type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value
            )}
            disabled={disabled}
            $hasError={!!error}
            {...inputProps}
          />
        );
        
      case 'select':
        return (
          <Select
            id={fieldId}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            $hasError={!!error}
          >
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        );
        
      case 'toggle':
        return (
          <ToggleContainer>
            <ToggleInput
              id={fieldId}
              type="checkbox"
              checked={!!value}
              onChange={(e) => handleChange(e.target.checked)}
              disabled={disabled}
            />
            <ToggleSlider />
          </ToggleContainer>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <FieldContainer className={className}>
      <FieldRow>
        <LabelSection>
          <FieldLabel htmlFor={fieldId}>
            {label}
          </FieldLabel>
          {description && (
            <FieldDescription>
              {description}
            </FieldDescription>
          )}
        </LabelSection>
        
        <ControlSection>
          {renderControl()}
        </ControlSection>
      </FieldRow>
      
      {error && (
        <ErrorMessage role="alert">
          {error}
        </ErrorMessage>
      )}
    </FieldContainer>
  );
};

export default SettingsFormField;
