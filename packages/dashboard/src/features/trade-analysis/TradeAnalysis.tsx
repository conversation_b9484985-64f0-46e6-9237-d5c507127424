/**
 * Trade Analysis Page
 *
 * REFACTORED: Now uses the proven TradingDashboard container pattern
 * for better performance, maintainability, and consistency.
 */

import React from 'react';
import { TradeAnalysisProvider } from './hooks/TradeAnalysisContext';
import { TradeAnalysisContainer } from './components/TradeAnalysisContainer';

/**
 * Main Trade Analysis Component
 *
 * Simple wrapper that provides context and renders the container.
 * Follows the proven TradingDashboard architecture pattern.
 */
const TradeAnalysis: React.FC = () => {
  return (
    <TradeAnalysisProvider>
      <TradeAnalysisContainer />
    </TradeAnalysisProvider>
  );
};

export default TradeAnalysis;
