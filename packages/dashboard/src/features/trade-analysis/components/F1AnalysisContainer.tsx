/**
 * F1AnalysisContainer Component
 * 
 * REFACTORED FROM: TradeAnalysis.tsx (144 lines → focused components)
 * Main orchestrator for trade analysis with F1 container pattern.
 * 
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */

import React, { Suspense, useEffect } from 'react';
import styled from 'styled-components';
import { useTradeAnalysis } from '../hooks/useTradeAnalysis';
import { F1AnalysisHeader } from './F1AnalysisHeader';
import { F1AnalysisTabs } from './F1AnalysisTabs';
import { useAnalysisNavigation } from './useAnalysisNavigation';
import { AnalysisTabContentRenderer } from './analysisTabConfig';

export interface F1AnalysisContainerProps {
  /** Custom className */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  min-height: 100vh;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  max-width: 1400px;
  margin: 0 auto;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  flex: 1;
`;

const TabContentContainer = styled.div`
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: 0 0 ${({ theme }) => theme.borderRadius?.lg || '8px'} ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  animation: fadeIn 0.3s ease-in-out;
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
`;

const LoadingIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.3; }
  }
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
  background: ${({ theme }) => theme.colors?.error || '#ef4444'}10;
  border: 1px solid ${({ theme }) => theme.colors?.error || '#ef4444'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
`;

const ErrorIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
`;

const ErrorTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const ErrorMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
  max-width: 400px;
`;

const RetryButton = styled.button`
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#b91c1c'};
    transform: translateY(-1px);
  }
`;

/**
 * LoadingFallback Component
 */
const LoadingFallback: React.FC = () => (
  <LoadingState>
    <LoadingIcon>📊</LoadingIcon>
    <LoadingText>Loading Trade Analysis...</LoadingText>
  </LoadingState>
);

/**
 * ErrorFallback Component
 */
const ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
  <ErrorState>
    <ErrorIcon>⚠️</ErrorIcon>
    <ErrorTitle>Analysis Error</ErrorTitle>
    <ErrorMessage>{error}</ErrorMessage>
    <RetryButton onClick={onRetry}>
      Try Again
    </RetryButton>
  </ErrorState>
);

/**
 * AnalysisContent Component
 */
const AnalysisContent: React.FC = () => {
  const {
    trades,
    filter,
    sort,
    page,
    pageSize,
    totalPages,
    tradeSummary,
    equityCurveData,
    distributionData,
    isLoading,
    error,
    setFilter,
    clearFilters,
    setSort,
    setPage,
    setPageSize,
    fetchTrades,
    exportTrades,
  } = useTradeAnalysis();
  
  const { activeTab, setActiveTab } = useAnalysisNavigation({
    defaultTab: 'summary',
  });
  
  // Fetch trades on mount
  useEffect(() => {
    fetchTrades();
  }, [fetchTrades]);
  
  // Prepare data and handlers for tab content
  const tabContentProps = {
    activeTab,
    data: {
      trades,
      filter,
      sort,
      page,
      pageSize,
      totalPages,
      tradeSummary,
      equityCurveData,
      distributionData,
    },
    isLoading,
    error,
    handlers: {
      setFilter,
      clearFilters,
      setSort,
      setPage,
      setPageSize,
      fetchTrades,
      exportTrades,
    },
  };
  
  if (error) {
    return <ErrorFallback error={error} onRetry={fetchTrades} />;
  }
  
  return (
    <Container>
      {/* F1 Racing Header */}
      <F1AnalysisHeader
        isLoading={isLoading}
        tradeCount={trades.length}
        onRefresh={fetchTrades}
        onExport={exportTrades}
      />
      
      {/* F1 Racing Tabs */}
      <F1AnalysisTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        disabled={isLoading}
      />
      
      {/* Tab Content */}
      <ContentArea>
        <TabContentContainer>
          <Suspense fallback={<LoadingFallback />}>
            <AnalysisTabContentRenderer {...tabContentProps} />
          </Suspense>
        </TabContentContainer>
      </ContentArea>
    </Container>
  );
};

/**
 * F1AnalysisContainer Component
 * 
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const F1AnalysisContainer: React.FC<F1AnalysisContainerProps> = ({
  className,
}) => {
  return (
    <div className={className}>
      <Suspense fallback={<LoadingFallback />}>
        <AnalysisContent />
      </Suspense>
    </div>
  );
};

export default F1AnalysisContainer;
