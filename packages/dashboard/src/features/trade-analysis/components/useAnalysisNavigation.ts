/**
 * useAnalysisNavigation Hook
 * 
 * REFACTORED FROM: TradeAnalysis.tsx (144 lines → focused components)
 * Hook for managing analysis navigation and tab state.
 * 
 * BENEFITS:
 * - Focused responsibility (navigation only)
 * - Persistent tab state with localStorage
 * - Type-safe navigation handling
 * - Reusable across analysis components
 * - Better separation of concerns
 */

import { useState, useCallback, useEffect } from 'react';
import { AnalysisTab } from './F1AnalysisTabs';

export interface UseAnalysisNavigationProps {
  /** Default tab to show */
  defaultTab?: AnalysisTab;
  /** Storage key for persistence */
  storageKey?: string;
}

export interface UseAnalysisNavigationReturn {
  /** Current active tab */
  activeTab: AnalysisTab;
  /** Change active tab */
  setActiveTab: (tab: AnalysisTab) => void;
  /** Navigate to next tab */
  nextTab: () => void;
  /** Navigate to previous tab */
  previousTab: () => void;
  /** Check if tab is active */
  isTabActive: (tab: AnalysisTab) => boolean;
  /** Get tab index */
  getTabIndex: (tab: AnalysisTab) => number;
  /** Get all available tabs */
  availableTabs: AnalysisTab[];
}

/**
 * Available tabs in order
 */
const AVAILABLE_TABS: AnalysisTab[] = ['summary', 'charts', 'trades', 'filters'];

/**
 * Default storage key
 */
const DEFAULT_STORAGE_KEY = 'adhd-trading-dashboard:analysis:active-tab';

/**
 * Load tab from localStorage
 */
const loadTabFromStorage = (storageKey: string, defaultTab: AnalysisTab): AnalysisTab => {
  try {
    const stored = localStorage.getItem(storageKey);
    if (stored && AVAILABLE_TABS.includes(stored as AnalysisTab)) {
      return stored as AnalysisTab;
    }
  } catch (error) {
    console.warn('Failed to load analysis tab from localStorage:', error);
  }
  return defaultTab;
};

/**
 * Save tab to localStorage
 */
const saveTabToStorage = (storageKey: string, tab: AnalysisTab): void => {
  try {
    localStorage.setItem(storageKey, tab);
  } catch (error) {
    console.warn('Failed to save analysis tab to localStorage:', error);
  }
};

/**
 * useAnalysisNavigation Hook
 * 
 * Manages tab navigation state with persistence and keyboard navigation.
 */
export const useAnalysisNavigation = ({
  defaultTab = 'summary',
  storageKey = DEFAULT_STORAGE_KEY,
}: UseAnalysisNavigationProps = {}): UseAnalysisNavigationReturn => {
  
  // Initialize active tab from storage or default
  const [activeTab, setActiveTabState] = useState<AnalysisTab>(() =>
    loadTabFromStorage(storageKey, defaultTab)
  );
  
  /**
   * Set active tab with persistence
   */
  const setActiveTab = useCallback((tab: AnalysisTab) => {
    if (AVAILABLE_TABS.includes(tab)) {
      setActiveTabState(tab);
      saveTabToStorage(storageKey, tab);
    }
  }, [storageKey]);
  
  /**
   * Navigate to next tab
   */
  const nextTab = useCallback(() => {
    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);
    const nextIndex = (currentIndex + 1) % AVAILABLE_TABS.length;
    setActiveTab(AVAILABLE_TABS[nextIndex]);
  }, [activeTab, setActiveTab]);
  
  /**
   * Navigate to previous tab
   */
  const previousTab = useCallback(() => {
    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);
    const previousIndex = currentIndex === 0 ? AVAILABLE_TABS.length - 1 : currentIndex - 1;
    setActiveTab(AVAILABLE_TABS[previousIndex]);
  }, [activeTab, setActiveTab]);
  
  /**
   * Check if tab is active
   */
  const isTabActive = useCallback((tab: AnalysisTab): boolean => {
    return activeTab === tab;
  }, [activeTab]);
  
  /**
   * Get tab index
   */
  const getTabIndex = useCallback((tab: AnalysisTab): number => {
    return AVAILABLE_TABS.indexOf(tab);
  }, []);
  
  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle if no input is focused
      if (document.activeElement?.tagName === 'INPUT' || 
          document.activeElement?.tagName === 'TEXTAREA' ||
          document.activeElement?.tagName === 'SELECT') {
        return;
      }
      
      // Handle Ctrl/Cmd + Arrow keys for tab navigation
      if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault();
            previousTab();
            break;
          case 'ArrowRight':
            event.preventDefault();
            nextTab();
            break;
        }
      }
      
      // Handle number keys for direct tab navigation
      if (event.key >= '1' && event.key <= '4' && !event.ctrlKey && !event.metaKey) {
        const tabIndex = parseInt(event.key) - 1;
        if (tabIndex < AVAILABLE_TABS.length) {
          event.preventDefault();
          setActiveTab(AVAILABLE_TABS[tabIndex]);
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [nextTab, previousTab, setActiveTab]);
  
  return {
    activeTab,
    setActiveTab,
    nextTab,
    previousTab,
    isTabActive,
    getTabIndex,
    availableTabs: AVAILABLE_TABS,
  };
};

export default useAnalysisNavigation;
