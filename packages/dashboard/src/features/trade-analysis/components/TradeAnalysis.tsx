/**
 * Trade Analysis Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 144 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 95% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */

import React from 'react';
import { TradeAnalysisProvider } from '../hooks/tradeAnalysisState';
import { F1AnalysisContainer } from './F1AnalysisContainer';

export interface TradeAnalysisProps {
  /** The title of the component */
  title?: string;
}

/**
 * Trade Analysis Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
export const TradeAnalysis: React.FC<TradeAnalysisProps> = ({ title }) => {
  return (
    <TradeAnalysisProvider>
      <F1AnalysisContainer />
    </TradeAnalysisProvider>
  );
};

export default TradeAnalysis;
