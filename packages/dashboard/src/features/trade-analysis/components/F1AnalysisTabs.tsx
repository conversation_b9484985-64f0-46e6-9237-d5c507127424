/**
 * F1AnalysisTabs Component
 * 
 * REFACTORED FROM: TradeAnalysis.tsx (144 lines → focused components)
 * F1 racing-themed tabs for different analysis views.
 * 
 * BENEFITS:
 * - Focused responsibility (tab navigation only)
 * - F1 racing theme with red accents
 * - Consistent with other F1Tab components
 * - Better separation of concerns
 * - Reusable tab navigation pattern
 */

import React from 'react';
import styled from 'styled-components';

export type AnalysisTab = 'summary' | 'charts' | 'trades' | 'filters';

export interface F1AnalysisTabsProps {
  /** Currently active tab */
  activeTab: AnalysisTab;
  /** Tab change handler */
  onTabChange: (tab: AnalysisTab) => void;
  /** Whether tabs are disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'} ${({ theme }) => theme.borderRadius?.lg || '8px'} 0 0;
  overflow: hidden;
  position: relative;
`;

const Tab = styled.button<{ $isActive: boolean; $disabled?: boolean }>`
  flex: 1;
  padding: ${({ theme }) => theme.spacing?.md || '12px'} ${({ theme }) => theme.spacing?.lg || '24px'};
  background: transparent;
  border: none;
  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease;
  position: relative;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  
  /* Base styling */
  color: ${({ theme, $isActive }) =>
    $isActive 
      ? theme.colors?.primary || '#dc2626'
      : theme.colors?.textSecondary || '#9ca3af'};
  
  /* F1 Racing hover effect */
  &:hover:not(:disabled) {
    color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    background: ${({ theme }) => theme.colors?.primary || '#dc2626'}08;
    transform: translateY(-1px);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  /* Active tab styling */
  ${({ $isActive, theme }) =>
    $isActive &&
    `
    background: ${theme.colors?.primary || '#dc2626'}10;
    
    /* F1 Racing active indicator */
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(
        90deg,
        ${theme.colors?.primary || '#dc2626'} 0%,
        ${theme.colors?.primary || '#dc2626'}80 50%,
        ${theme.colors?.primary || '#dc2626'} 100%
      );
    }
  `}
  
  /* Disabled styling */
  ${({ $disabled }) =>
    $disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
  `}
  
  /* Mobile responsive */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
    font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  }
`;

const TabIcon = styled.span`
  margin-right: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-size: 16px;
  
  @media (max-width: 768px) {
    margin-right: 0;
    font-size: 14px;
  }
`;

const TabLabel = styled.span`
  @media (max-width: 768px) {
    display: none;
  }
`;

/**
 * Tab configuration with icons and labels
 */
const TAB_CONFIG: Record<AnalysisTab, { icon: string; label: string; description: string }> = {
  summary: {
    icon: '📊',
    label: 'Summary',
    description: 'Performance overview and key metrics',
  },
  charts: {
    icon: '📈',
    label: 'Charts',
    description: 'Visual analysis and trend charts',
  },
  trades: {
    icon: '📋',
    label: 'Trades',
    description: 'Detailed trade list and analysis',
  },
  filters: {
    icon: '🔍',
    label: 'Filters',
    description: 'Filter and search options',
  },
};

/**
 * F1AnalysisTabs Component
 * 
 * PATTERN: F1 Tabs Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for active state
 * - Accessible keyboard navigation
 * - Responsive design for mobile
 */
export const F1AnalysisTabs: React.FC<F1AnalysisTabsProps> = ({
  activeTab,
  onTabChange,
  disabled = false,
  className,
}) => {
  const handleTabClick = (tab: AnalysisTab) => {
    if (!disabled) {
      onTabChange(tab);
    }
  };
  
  const handleKeyDown = (event: React.KeyboardEvent, tab: AnalysisTab) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
      event.preventDefault();
      onTabChange(tab);
    }
  };
  
  return (
    <TabsContainer className={className} role="tablist">
      {(Object.keys(TAB_CONFIG) as AnalysisTab[]).map((tab) => {
        const config = TAB_CONFIG[tab];
        const isActive = activeTab === tab;
        
        return (
          <Tab
            key={tab}
            $isActive={isActive}
            $disabled={disabled}
            onClick={() => handleTabClick(tab)}
            onKeyDown={(e) => handleKeyDown(e, tab)}
            disabled={disabled}
            role="tab"
            aria-selected={isActive}
            aria-controls={`analysis-panel-${tab}`}
            tabIndex={disabled ? -1 : 0}
            title={config.description}
          >
            <TabIcon>{config.icon}</TabIcon>
            <TabLabel>{config.label}</TabLabel>
          </Tab>
        );
      })}
    </TabsContainer>
  );
};

export default F1AnalysisTabs;
