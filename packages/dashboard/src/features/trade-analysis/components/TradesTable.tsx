/**
 * Trades Table Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 330 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 95% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */

import React from 'react';
import { TradesTableContainer } from './TradesTableContainer';

interface TradesTableProps {
  className?: string;
}

/**
 * TradesTable Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
export const TradesTable: React.FC<TradesTableProps> = (props) => {
  return <TradesTableContainer {...props} />;
};

export default TradesTable;
