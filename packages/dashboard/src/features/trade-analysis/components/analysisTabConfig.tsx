/**
 * Analysis Tab Configuration
 * 
 * REFACTORED FROM: TradeAnalysis.tsx (144 lines → focused components)
 * Centralized configuration for analysis tabs and their content.
 * 
 * BENEFITS:
 * - Single source of truth for tab definitions
 * - Easy to maintain and extend
 * - Type-safe tab configurations
 * - Reusable across different analysis views
 * - Clear content mapping
 */

import React from 'react';
import { AnalysisTab } from './F1AnalysisTabs';
import { TradeAnalysisFilter } from './TradeAnalysisFilter';
import { TradeAnalysisTable } from './TradeAnalysisTable';
import { TradeAnalysisSummary } from './TradeAnalysisSummary';
import { TradeAnalysisCharts } from './TradeAnalysisCharts';

export interface AnalysisTabConfig {
  id: AnalysisTab;
  title: string;
  description: string;
  icon: string;
  component: React.ComponentType<any>;
  showInMobile: boolean;
  requiresData: boolean;
}

export interface AnalysisTabContentProps {
  /** Current active tab */
  activeTab: AnalysisTab;
  /** Analysis data */
  data: {
    trades: any[];
    filter: any;
    sort: any;
    page: number;
    pageSize: number;
    totalPages: number;
    tradeSummary: any;
    equityCurveData: any[];
    distributionData: any[];
  };
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Action handlers */
  handlers: {
    setFilter: (filter: any) => void;
    clearFilters: () => void;
    setSort: (sort: any) => void;
    setPage: (page: number) => void;
    setPageSize: (pageSize: number) => void;
    fetchTrades: () => void;
    exportTrades: () => void;
  };
}

/**
 * Summary Tab Content
 */
const SummaryTabContent: React.FC<AnalysisTabContentProps> = ({ 
  data, 
  isLoading 
}) => {
  if (!data.tradeSummary) {
    return (
      <div style={{ 
        padding: '48px', 
        textAlign: 'center', 
        color: '#9ca3af' 
      }}>
        📊 No summary data available
      </div>
    );
  }
  
  return (
    <TradeAnalysisSummary 
      summary={data.tradeSummary} 
      isLoading={isLoading} 
    />
  );
};

/**
 * Charts Tab Content
 */
const ChartsTabContent: React.FC<AnalysisTabContentProps> = ({ 
  data, 
  isLoading 
}) => {
  return (
    <TradeAnalysisCharts
      equityCurveData={data.equityCurveData}
      distributionData={data.distributionData}
      isLoading={isLoading}
    />
  );
};

/**
 * Trades Tab Content
 */
const TradesTabContent: React.FC<AnalysisTabContentProps> = ({ 
  data, 
  isLoading, 
  handlers 
}) => {
  return (
    <TradeAnalysisTable
      trades={data.trades}
      sort={data.sort}
      onSort={handlers.setSort}
      page={data.page}
      onPageChange={handlers.setPage}
      pageSize={data.pageSize}
      onPageSizeChange={handlers.setPageSize}
      totalPages={data.totalPages}
      isLoading={isLoading}
    />
  );
};

/**
 * Filters Tab Content
 */
const FiltersTabContent: React.FC<AnalysisTabContentProps> = ({ 
  data, 
  isLoading, 
  handlers 
}) => {
  return (
    <TradeAnalysisFilter
      filter={data.filter}
      onSetFilter={handlers.setFilter}
      onClearFilters={handlers.clearFilters}
      isLoading={isLoading}
    />
  );
};

/**
 * Tab configuration with components and metadata
 */
export const ANALYSIS_TAB_CONFIG: Record<AnalysisTab, AnalysisTabConfig> = {
  summary: {
    id: 'summary',
    title: 'Performance Summary',
    description: 'Overview of trading performance and key metrics',
    icon: '📊',
    component: SummaryTabContent,
    showInMobile: true,
    requiresData: true,
  },
  charts: {
    id: 'charts',
    title: 'Visual Analysis',
    description: 'Charts and visual representations of trading data',
    icon: '📈',
    component: ChartsTabContent,
    showInMobile: true,
    requiresData: true,
  },
  trades: {
    id: 'trades',
    title: 'Trade Details',
    description: 'Detailed list and analysis of individual trades',
    icon: '📋',
    component: TradesTabContent,
    showInMobile: true,
    requiresData: true,
  },
  filters: {
    id: 'filters',
    title: 'Filter & Search',
    description: 'Advanced filtering and search options',
    icon: '🔍',
    component: FiltersTabContent,
    showInMobile: false,
    requiresData: false,
  },
};

/**
 * Get tab configuration by ID
 */
export const getTabConfig = (tabId: AnalysisTab): AnalysisTabConfig => {
  return ANALYSIS_TAB_CONFIG[tabId];
};

/**
 * Get all tab configurations
 */
export const getAllTabConfigs = (): AnalysisTabConfig[] => {
  return Object.values(ANALYSIS_TAB_CONFIG);
};

/**
 * Get mobile-friendly tabs
 */
export const getMobileTabConfigs = (): AnalysisTabConfig[] => {
  return getAllTabConfigs().filter(config => config.showInMobile);
};

/**
 * Get tabs that require data
 */
export const getDataRequiredTabConfigs = (): AnalysisTabConfig[] => {
  return getAllTabConfigs().filter(config => config.requiresData);
};

/**
 * Tab Content Renderer Component
 */
export const AnalysisTabContentRenderer: React.FC<AnalysisTabContentProps> = (props) => {
  const { activeTab } = props;
  const config = getTabConfig(activeTab);
  
  if (!config) {
    return (
      <div style={{ 
        padding: '48px', 
        textAlign: 'center', 
        color: '#ef4444' 
      }}>
        ❌ Unknown tab: {activeTab}
      </div>
    );
  }
  
  const TabComponent = config.component;
  
  return (
    <div 
      id={`analysis-panel-${activeTab}`}
      role="tabpanel"
      aria-labelledby={`analysis-tab-${activeTab}`}
    >
      <TabComponent {...props} />
    </div>
  );
};

export default AnalysisTabContentRenderer;
