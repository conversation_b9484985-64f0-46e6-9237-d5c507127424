/**
 * TabContentRenderer Component
 * 
 * Renders the appropriate content for each analysis tab.
 * Extracted from the original TradeAnalysis component for better separation of concerns.
 */

import React from 'react';
import { DataCard } from '@adhd-trading-dashboard/shared';
import { PerformanceSummary } from './PerformanceSummary';
import { TradesTable } from './TradesTable';
import { CategoryPerformanceChart } from './CategoryPerformanceChart';
import { TimePerformanceChart } from './TimePerformanceChart';
import { TradeDetail } from './TradeDetail';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';

export interface TabContentRendererProps {
  /** Currently active tab */
  activeTab: string;
  /** Analysis data */
  data: any;
  /** Loading state */
  isLoading: boolean;
  /** Error message */
  error: string | null;
}

/**
 * TabContentRenderer Component
 * 
 * Handles rendering of content for each analysis tab with proper
 * error handling and loading states.
 */
export const TabContentRenderer: React.FC<TabContentRendererProps> = ({
  activeTab,
  data,
  isLoading,
  error,
}) => {
  const { selectedTradeId } = useTradeAnalysis();

  const renderContent = () => {
    switch (activeTab) {
      case 'summary':
        return (
          <>
            <DataCard
              title="Performance Summary"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.metrics}
              emptyMessage="No performance data available for the selected filters."
            >
              <PerformanceSummary />
            </DataCard>

            <DataCard
              title="Performance by Time of Day"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}
              emptyMessage="No time of day performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="timeOfDay" title="Time of Day" />
            </DataCard>

            <DataCard
              title="Performance by Day of Week"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}
              emptyMessage="No day of week performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="dayOfWeek" title="Day of Week" />
            </DataCard>
          </>
        );

      case 'trades':
        return (
          <>
            <DataCard
              title="Trades"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.trades || data.trades.length === 0}
              emptyMessage="No trades available for the selected filters."
            >
              <TradesTable />
            </DataCard>

            {selectedTradeId && <TradeDetail />}
          </>
        );

      case 'symbols':
        return (
          <DataCard
            title="Performance by Symbol"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
            isEmpty={!data?.symbolPerformance || data.symbolPerformance.length === 0}
            emptyMessage="No symbol performance data available for the selected filters."
          >
            <CategoryPerformanceChart category="symbol" title="Symbol" />
          </DataCard>
        );

      case 'strategies':
        return (
          <DataCard
            title="Performance by Strategy"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
            isEmpty={!data?.strategyPerformance || data.strategyPerformance.length === 0}
            emptyMessage="No strategy performance data available for the selected filters."
          >
            <CategoryPerformanceChart category="strategy" title="Strategy" />
          </DataCard>
        );

      case 'timeframes':
        return (
          <>
            <DataCard
              title="Performance by Timeframe"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.timeframePerformance || data.timeframePerformance.length === 0}
              emptyMessage="No timeframe performance data available for the selected filters."
            >
              <CategoryPerformanceChart category="timeframe" title="Timeframe" />
            </DataCard>

            <DataCard
              title="Performance by Session"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.sessionPerformance || data.sessionPerformance.length === 0}
              emptyMessage="No session performance data available for the selected filters."
            >
              <CategoryPerformanceChart category="session" title="Session" />
            </DataCard>
          </>
        );

      case 'time':
        return (
          <>
            <DataCard
              title="Performance by Time of Day"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}
              emptyMessage="No time of day performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="timeOfDay" title="Time of Day" />
            </DataCard>

            <DataCard
              title="Performance by Day of Week"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}
              emptyMessage="No day of week performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="dayOfWeek" title="Day of Week" />
            </DataCard>
          </>
        );

      default:
        return (
          <DataCard
            title="Unknown Tab"
            hasError={true}
            errorMessage={`Unknown tab: ${activeTab}`}
          >
            <div style={{ textAlign: 'center', padding: '40px', color: '#9ca3af' }}>
              Tab content not found: {activeTab}
            </div>
          </DataCard>
        );
    }
  };

  return <>{renderContent()}</>;
};

export default TabContentRenderer;
