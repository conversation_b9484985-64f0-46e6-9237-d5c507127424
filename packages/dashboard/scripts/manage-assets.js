/**
 * Asset Management Script for ADHD Trading Dashboard
 *
 * This script ensures that all necessary assets are available in the correct locations
 * for both development and production builds.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function manageAssets() {
  // Directories
  const rootDir = path.resolve(__dirname, '..');
  const publicDir = path.resolve(rootDir, 'public');
  const distDir = path.resolve(rootDir, 'dist');

  // Ensure directories exist
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
    console.log(`Created directory: ${publicDir}`);
  }

  // List of essential files
  const essentialFiles = ['favicon.ico', 'logo192.png', 'logo512.png', 'manifest.json'];

  // Check if essential files exist, generate proper files if not
  let missingFiles = false;
  essentialFiles.forEach((file) => {
    const publicFilePath = path.join(publicDir, file);
    const rootFilePath = path.join(rootDir, file);

    if (!fs.existsSync(publicFilePath) || !fs.existsSync(rootFilePath)) {
      console.log(`Missing essential file: ${file} in public or root directory`);
      missingFiles = true;

      if (file === 'manifest.json') {
        // Create a basic manifest.json
        const manifest = {
          short_name: 'ADHD Trading',
          name: 'ADHD Trading Dashboard',
          icons: [
            {
              src: 'favicon.ico',
              sizes: '64x64 32x32 24x24 16x16',
              type: 'image/x-icon',
            },
            {
              src: 'logo192.png',
              type: 'image/png',
              sizes: '192x192',
            },
            {
              src: 'logo512.png',
              type: 'image/png',
              sizes: '512x512',
            },
          ],
          start_url: '.',
          display: 'standalone',
          theme_color: '#e10600',
          background_color: '#1a1f2c',
        };

        // Write to public directory
        fs.writeFileSync(publicFilePath, JSON.stringify(manifest, null, 2));
        console.log(`Created manifest.json in public directory`);

        // Write to root directory
        fs.writeFileSync(rootFilePath, JSON.stringify(manifest, null, 2));
        console.log(`Created manifest.json in root directory`);
      }
    }
  });

  // Always regenerate image files to ensure they're in the correct format
  console.log('Generating logo files...');
  try {
    // Use the logo generation script that creates proper PNG files
    await import('./generate-logo-files.js');
    console.log('Generated proper logo files using canvas');
  } catch (error) {
    console.error(`Failed to generate proper logo files: ${error.message}`);
    console.log('Falling back to placeholder SVG files...');

    try {
      // Fall back to the old placeholder script if the new one fails
      await import('../public/assets/generate-placeholder-assets.js');
      console.log('Generated placeholder SVG files');
    } catch (fallbackError) {
      console.error(`Failed to generate placeholder files: ${fallbackError.message}`);
    }
  }

  // Copy files from public to root if needed for development
  essentialFiles.forEach((file) => {
    const publicFilePath = path.join(publicDir, file);
    const rootFilePath = path.join(rootDir, file);

    if (fs.existsSync(publicFilePath) && !fs.existsSync(rootFilePath)) {
      try {
        fs.copyFileSync(publicFilePath, rootFilePath);
        console.log(`Copied ${file} from public to root directory for development`);
      } catch (error) {
        console.error(`Failed to copy ${file}: ${error.message}`);
      }
    }
  });

  // Ensure index.html is consistent between root and public directories
  const rootIndexPath = path.join(rootDir, 'index.html');
  const publicIndexPath = path.join(publicDir, 'index.html');

  try {
    // Check if both files exist
    const rootExists = fs.existsSync(rootIndexPath);
    const publicExists = fs.existsSync(publicIndexPath);

    if (rootExists && publicExists) {
      // Both exist, ensure they're consistent
      const rootContent = fs.readFileSync(rootIndexPath, 'utf8');
      const publicContent = fs.readFileSync(publicIndexPath, 'utf8');

      if (rootContent !== publicContent) {
        console.log('index.html files are inconsistent, synchronizing...');

        // Standardize paths to be relative
        let standardContent = rootContent;
        standardContent = standardContent.replace(/href="\/([^/])/g, 'href="./$1');
        standardContent = standardContent.replace(/src="\/([^/])/g, 'src="./$1');

        // Write to both locations
        fs.writeFileSync(rootIndexPath, standardContent);
        fs.writeFileSync(publicIndexPath, standardContent);
        console.log('Synchronized index.html files with consistent paths');
      }
    } else if (rootExists && !publicExists) {
      // Only root exists, copy to public
      let content = fs.readFileSync(rootIndexPath, 'utf8');

      // Ensure paths are relative
      content = content.replace(/href="\/([^/])/g, 'href="./$1');
      content = content.replace(/src="\/([^/])/g, 'src="./$1');

      fs.writeFileSync(publicIndexPath, content);
      console.log('Copied index.html from root to public directory with consistent paths');
    } else if (!rootExists && publicExists) {
      // Only public exists, copy to root
      let content = fs.readFileSync(publicIndexPath, 'utf8');

      // Ensure paths are relative
      content = content.replace(/href="\/([^/])/g, 'href="./$1');
      content = content.replace(/src="\/([^/])/g, 'src="./$1');

      fs.writeFileSync(rootIndexPath, content);
      console.log('Copied index.html from public to root directory with consistent paths');
    } else {
      // Neither exists, create a basic one
      const basicHtml = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="./favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#e10600" />
    <meta name="description" content="ADHD Trading Dashboard - F1-inspired trading analytics" />
    <link rel="apple-touch-icon" href="./logo192.png" />
    <link rel="manifest" href="./manifest.json" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <title>ADHD Trading Dashboard</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="./src/index.tsx"></script>
  </body>
</html>`;

      fs.writeFileSync(rootIndexPath, basicHtml);
      fs.writeFileSync(publicIndexPath, basicHtml);
      console.log('Created index.html files in both root and public directories');
    }
  } catch (error) {
    console.error(`Failed to synchronize index.html files: ${error.message}`);
  }

  console.log('Asset management complete!');
}

// Run the asset management
manageAssets().catch(console.error);
