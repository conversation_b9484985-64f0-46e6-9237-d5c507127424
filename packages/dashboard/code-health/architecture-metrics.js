#!/usr/bin/env node

/**
 * Architecture Metrics Analyzer
 *
 * Tracks component complexity scores and architectural health indicators.
 * Provides objective "health" metrics for refactoring decisions.
 *
 * METRICS TRACKED:
 * - Lines of code (LOC)
 * - Number of imports
 * - Cyclomatic complexity (estimated)
 * - Component responsibilities
 * - Dependency graph analysis
 * - Pattern compliance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, 'architecture-metrics.json'),
  thresholds: {
    lines: { good: 100, warning: 200, critical: 300 },
    imports: { good: 10, warning: 20, critical: 30 },
    complexity: { good: 10, warning: 20, critical: 30 },
    responsibilities: { good: 3, warning: 5, critical: 8 },
  },
  patterns: {
    container: /Container\.tsx?$/,
    header: /Header\.tsx?$/,
    form: /Form\.tsx?$/,
    hook: /^use[A-Z].*\.ts$/,
    component: /\.tsx$/,
  },
};

/**
 * Calculate component metrics
 */
function analyzeComponent(filePath, content) {
  const lines = content.split('\n');
  const nonEmptyLines = lines.filter((line) => line.trim().length > 0);

  // Count imports
  const imports = lines.filter(
    (line) => line.trim().startsWith('import ') || line.trim().startsWith('export ')
  ).length;

  // Estimate cyclomatic complexity
  const complexityKeywords = [
    'if',
    'else',
    'switch',
    'case',
    'for',
    'while',
    'do',
    'catch',
    'finally',
  ];
  const complexity =
    complexityKeywords.reduce((count, keyword) => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      return count + (matches ? matches.length : 0);
    }, 1) +
    // Count logical operators separately
    (content.match(/&&/g) || []).length +
    (content.match(/\|\|/g) || []).length +
    (content.match(/\?/g) || []).length;

  // Count component responsibilities (rough estimate)
  const responsibilities = [
    /useState|useReducer/, // State management
    /useEffect/, // Side effects
    /fetch|axios|api/, // API calls
    /styled|css/, // Styling
    /onClick|onChange|onSubmit/, // Event handling
    /validate|error/, // Validation
    /loading|spinner/, // Loading states
    /modal|dialog/, // UI interactions
  ].filter((pattern) => pattern.test(content)).length;

  // Detect patterns
  const fileName = path.basename(filePath);
  const detectedPatterns = Object.entries(CONFIG.patterns)
    .filter(([, pattern]) => pattern.test(fileName))
    .map(([name]) => name);

  // Calculate health score (0-100)
  const scores = {
    lines: Math.max(0, 100 - (nonEmptyLines.length / CONFIG.thresholds.lines.critical) * 100),
    imports: Math.max(0, 100 - (imports / CONFIG.thresholds.imports.critical) * 100),
    complexity: Math.max(0, 100 - (complexity / CONFIG.thresholds.complexity.critical) * 100),
    responsibilities: Math.max(
      0,
      100 - (responsibilities / CONFIG.thresholds.responsibilities.critical) * 100
    ),
  };

  const overallHealth = Object.values(scores).reduce((sum, score) => sum + score, 0) / 4;

  return {
    filePath: path.relative(CONFIG.srcDir, filePath),
    metrics: {
      lines: nonEmptyLines.length,
      imports,
      complexity,
      responsibilities,
    },
    scores,
    overallHealth: Math.round(overallHealth),
    detectedPatterns,
    recommendations: generateRecommendations(
      nonEmptyLines.length,
      imports,
      complexity,
      responsibilities
    ),
  };
}

/**
 * Generate recommendations based on metrics
 */
function generateRecommendations(lines, imports, complexity, responsibilities) {
  const recommendations = [];

  if (lines > CONFIG.thresholds.lines.critical) {
    recommendations.push({
      type: 'critical',
      message: `Component is too large (${lines} lines). Consider applying container pattern.`,
      action: 'refactor',
      priority: 'high',
    });
  } else if (lines > CONFIG.thresholds.lines.warning) {
    recommendations.push({
      type: 'warning',
      message: `Component is getting large (${lines} lines). Monitor for refactoring opportunities.`,
      action: 'monitor',
      priority: 'medium',
    });
  }

  if (imports > CONFIG.thresholds.imports.critical) {
    recommendations.push({
      type: 'critical',
      message: `Too many imports (${imports}). Consider dependency injection or facade pattern.`,
      action: 'refactor',
      priority: 'high',
    });
  }

  if (complexity > CONFIG.thresholds.complexity.critical) {
    recommendations.push({
      type: 'critical',
      message: `High cyclomatic complexity (${complexity}). Extract functions or use strategy pattern.`,
      action: 'refactor',
      priority: 'high',
    });
  }

  if (responsibilities > CONFIG.thresholds.responsibilities.critical) {
    recommendations.push({
      type: 'critical',
      message: `Too many responsibilities (${responsibilities}). Apply single responsibility principle.`,
      action: 'refactor',
      priority: 'high',
    });
  }

  return recommendations;
}

/**
 * Scan directory for components
 */
function scanDirectory(dir) {
  const results = [];

  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scan(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          const analysis = analyzeComponent(fullPath, content);
          results.push(analysis);
        } catch (error) {
          console.warn(`Warning: Could not analyze ${fullPath}: ${error.message}`);
        }
      }
    }
  }

  scan(dir);
  return results;
}

/**
 * Generate summary report
 */
function generateSummary(analyses) {
  const total = analyses.length;
  const healthDistribution = {
    excellent: analyses.filter((a) => a.overallHealth >= 80).length,
    good: analyses.filter((a) => a.overallHealth >= 60 && a.overallHealth < 80).length,
    warning: analyses.filter((a) => a.overallHealth >= 40 && a.overallHealth < 60).length,
    critical: analyses.filter((a) => a.overallHealth < 40).length,
  };

  const patternUsage = {};
  analyses.forEach((analysis) => {
    analysis.detectedPatterns.forEach((pattern) => {
      patternUsage[pattern] = (patternUsage[pattern] || 0) + 1;
    });
  });

  const topIssues = analyses
    .filter((a) => a.recommendations.length > 0)
    .sort((a, b) => b.recommendations.length - a.recommendations.length)
    .slice(0, 10);

  const averageHealth = Math.round(analyses.reduce((sum, a) => sum + a.overallHealth, 0) / total);

  return {
    timestamp: new Date().toISOString(),
    summary: {
      totalComponents: total,
      averageHealth,
      healthDistribution,
      patternUsage,
    },
    topIssues: topIssues.map((issue) => ({
      file: issue.filePath,
      health: issue.overallHealth,
      issues: issue.recommendations.length,
      metrics: issue.metrics,
    })),
    recommendations: {
      immediate: analyses.filter((a) => a.recommendations.some((r) => r.priority === 'high'))
        .length,
      monitor: analyses.filter((a) => a.recommendations.some((r) => r.priority === 'medium'))
        .length,
    },
  };
}

/**
 * Main execution
 */
function main() {
  console.log('🔍 Analyzing architecture metrics...\n');

  const analyses = scanDirectory(CONFIG.srcDir);
  const summary = generateSummary(analyses);

  // Save detailed results
  const report = {
    summary,
    components: analyses,
    config: CONFIG,
  };

  fs.writeFileSync(CONFIG.outputFile, JSON.stringify(report, null, 2));

  // Display summary
  console.log('📊 ARCHITECTURE HEALTH REPORT');
  console.log('================================');
  console.log(`Total Components: ${summary.summary.totalComponents}`);
  console.log(`Average Health Score: ${summary.summary.averageHealth}/100`);
  console.log('');

  console.log('Health Distribution:');
  console.log(`  🟢 Excellent (80-100): ${summary.summary.healthDistribution.excellent}`);
  console.log(`  🟡 Good (60-79): ${summary.summary.healthDistribution.good}`);
  console.log(`  🟠 Warning (40-59): ${summary.summary.healthDistribution.warning}`);
  console.log(`  🔴 Critical (<40): ${summary.summary.healthDistribution.critical}`);
  console.log('');

  console.log('Pattern Usage:');
  Object.entries(summary.summary.patternUsage).forEach(([pattern, count]) => {
    console.log(`  ${pattern}: ${count} components`);
  });
  console.log('');

  console.log('Top Issues:');
  summary.topIssues.slice(0, 5).forEach((issue, index) => {
    console.log(
      `  ${index + 1}. ${issue.file} (Health: ${issue.health}/100, Issues: ${issue.issues})`
    );
  });

  console.log(`\n📄 Detailed report saved to: ${CONFIG.outputFile}`);
  console.log(`⚡ Immediate action needed: ${summary.recommendations.immediate} components`);
  console.log(`👀 Monitor: ${summary.recommendations.monitor} components`);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { analyzeComponent, scanDirectory, generateSummary };
