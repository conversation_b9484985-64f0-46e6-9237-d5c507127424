{"summary": {"timestamp": "2025-05-26T18:58:45.756Z", "summary": {"totalComponents": 201, "averageHealth": 69, "healthDistribution": {"excellent": 74, "good": 62, "warning": 50, "critical": 15}, "patternUsage": {"component": 145, "form": 7, "container": 5, "header": 8, "hook": 17}}, "topIssues": [{"file": "components/molecules/ProfitLossCell.tsx", "health": 43, "issues": 2, "metrics": {"lines": 217, "imports": 6, "complexity": 84, "responsibilities": 3}}, {"file": "features/daily-guide/components/TradingPlanContainer.tsx", "health": 34, "issues": 2, "metrics": {"lines": 222, "imports": 12, "complexity": 55, "responsibilities": 4}}, {"file": "features/trade-analysis/components/CategoryPerformanceChart.tsx", "health": 41, "issues": 2, "metrics": {"lines": 234, "imports": 6, "complexity": 37, "responsibilities": 3}}, {"file": "features/trade-analysis/components/TradesTable.tsx", "health": 30, "issues": 2, "metrics": {"lines": 287, "imports": 6, "complexity": 46, "responsibilities": 5}}, {"file": "features/trade-analysis/hooks/tradeAnalysisState.ts", "health": 22, "issues": 2, "metrics": {"lines": 392, "imports": 30, "complexity": 77, "responsibilities": 1}}, {"file": "features/trade-analysis/services/tradeAnalysisApi.ts", "health": 41, "issues": 2, "metrics": {"lines": 356, "imports": 3, "complexity": 71, "responsibilities": 2}}, {"file": "features/trade-journal/components/TradeList.tsx", "health": 39, "issues": 2, "metrics": {"lines": 217, "imports": 10, "complexity": 38, "responsibilities": 3}}, {"file": "features/trade-journal/hooks/useTradeFilters.ts", "health": 50, "issues": 2, "metrics": {"lines": 208, "imports": 5, "complexity": 58, "responsibilities": 1}}, {"file": "features/trade-journal/hooks/useTradeFormData.ts", "health": 39, "issues": 2, "metrics": {"lines": 234, "imports": 5, "complexity": 62, "responsibilities": 4}}, {"file": "features/trade-journal/hooks/useTradeSubmission.ts", "health": 45, "issues": 2, "metrics": {"lines": 228, "imports": 6, "complexity": 71, "responsibilities": 2}}], "recommendations": {"immediate": 40, "monitor": 28}}, "components": [{"filePath": "App.tsx", "metrics": {"lines": 21, "imports": 6, "complexity": 3, "responsibilities": 0}, "scores": {"lines": 93, "imports": 80, "complexity": 90, "responsibilities": 100}, "overallHealth": 91, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "MinimalApp.tsx", "metrics": {"lines": 39, "imports": 2, "complexity": 1, "responsibilities": 1}, "scores": {"lines": 87, "imports": 93.33333333333333, "complexity": 96.66666666666667, "responsibilities": 87.5}, "overallHealth": 91, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "SimpleApp.tsx", "metrics": {"lines": 23, "imports": 2, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 92.33333333333333, "imports": 93.33333333333333, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 96, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "TestApp.tsx", "metrics": {"lines": 65, "imports": 2, "complexity": 2, "responsibilities": 3}, "scores": {"lines": 78.33333333333333, "imports": 93.33333333333333, "complexity": 93.33333333333333, "responsibilities": 62.5}, "overallHealth": 82, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "components/AppErrorBoundary.tsx", "metrics": {"lines": 38, "imports": 5, "complexity": 5, "responsibilities": 1}, "scores": {"lines": 87.33333333333333, "imports": 83.33333333333334, "complexity": 83.33333333333334, "responsibilities": 87.5}, "overallHealth": 85, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "components/FeatureErrorBoundary.tsx", "metrics": {"lines": 49, "imports": 5, "complexity": 8, "responsibilities": 1}, "scores": {"lines": 83.66666666666667, "imports": 83.33333333333334, "complexity": 73.33333333333333, "responsibilities": 87.5}, "overallHealth": 82, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "components/NotFound.tsx", "metrics": {"lines": 59, "imports": 4, "complexity": 2, "responsibilities": 1}, "scores": {"lines": 80.33333333333334, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 87.5}, "overallHealth": 87, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "components/__tests__/AppErrorBoundary.test.tsx", "metrics": {"lines": 80, "imports": 5, "complexity": 8, "responsibilities": 2}, "scores": {"lines": 73.33333333333333, "imports": 83.33333333333334, "complexity": 73.33333333333333, "responsibilities": 75}, "overallHealth": 76, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "components/__tests__/FeatureErrorBoundary.test.tsx", "metrics": {"lines": 99, "imports": 5, "complexity": 13, "responsibilities": 2}, "scores": {"lines": 67, "imports": 83.33333333333334, "complexity": 56.666666666666664, "responsibilities": 75}, "overallHealth": 71, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "components/molecules/LoadingScreen.tsx", "metrics": {"lines": 45, "imports": 3, "complexity": 2, "responsibilities": 2}, "scores": {"lines": 85, "imports": 90, "complexity": 93.33333333333333, "responsibilities": 75}, "overallHealth": 86, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "components/molecules/ProfitLossCell.stories.tsx", "metrics": {"lines": 404, "imports": 17, "complexity": 20, "responsibilities": 2}, "scores": {"lines": 0, "imports": 43.333333333333336, "complexity": 33.33333333333334, "responsibilities": 75}, "overallHealth": 38, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "Component is too large (404 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "components/molecules/ProfitLossCell.tsx", "metrics": {"lines": 217, "imports": 6, "complexity": 84, "responsibilities": 3}, "scores": {"lines": 27.666666666666657, "imports": 80, "complexity": 0, "responsibilities": 62.5}, "overallHealth": 43, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (217 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (84). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "components/molecules/ProfitLossCellRefactored.tsx", "metrics": {"lines": 197, "imports": 5, "complexity": 83, "responsibilities": 3}, "scores": {"lines": 34.33333333333334, "imports": 83.33333333333334, "complexity": 0, "responsibilities": 62.5}, "overallHealth": 45, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (83). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/DailyGuide.tsx", "metrics": {"lines": 22, "imports": 4, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 92.66666666666667, "imports": 86.66666666666667, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 94, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/DailyGuideComposed.tsx", "metrics": {"lines": 137, "imports": 9, "complexity": 1, "responsibilities": 3}, "scores": {"lines": 54.333333333333336, "imports": 70, "complexity": 96.66666666666667, "responsibilities": 62.5}, "overallHealth": 71, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/__tests__/DailyGuide.test.tsx", "metrics": {"lines": 197, "imports": 5, "complexity": 6, "responsibilities": 4}, "scores": {"lines": 34.33333333333334, "imports": 83.33333333333334, "complexity": 80, "responsibilities": 50}, "overallHealth": 62, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/api/dailyGuideApi.ts", "metrics": {"lines": 173, "imports": 2, "complexity": 25, "responsibilities": 2}, "scores": {"lines": 42.333333333333336, "imports": 93.33333333333333, "complexity": 16.666666666666657, "responsibilities": 75}, "overallHealth": 57, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/daily-guide/components/AddItemForm.tsx", "metrics": {"lines": 175, "imports": 7, "complexity": 59, "responsibilities": 2}, "scores": {"lines": 41.666666666666664, "imports": 76.66666666666667, "complexity": 0, "responsibilities": 75}, "overallHealth": 48, "detectedPatterns": ["form", "component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (59). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/components/DailyGuide.tsx", "metrics": {"lines": 144, "imports": 10, "complexity": 6, "responsibilities": 3}, "scores": {"lines": 52, "imports": 66.66666666666667, "complexity": 80, "responsibilities": 62.5}, "overallHealth": 65, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/DailyGuideContainer.tsx", "metrics": {"lines": 169, "imports": 13, "complexity": 12, "responsibilities": 3}, "scores": {"lines": 43.666666666666664, "imports": 56.666666666666664, "complexity": 60, "responsibilities": 62.5}, "overallHealth": 56, "detectedPatterns": ["container", "component"], "recommendations": []}, {"filePath": "features/daily-guide/components/DailyGuideHeader.tsx", "metrics": {"lines": 155, "imports": 6, "complexity": 46, "responsibilities": 2}, "scores": {"lines": 48.33333333333333, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 51, "detectedPatterns": ["header", "component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (46). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/components/KeyLevels.tsx", "metrics": {"lines": 184, "imports": 6, "complexity": 20, "responsibilities": 4}, "scores": {"lines": 38.66666666666667, "imports": 80, "complexity": 33.33333333333334, "responsibilities": 50}, "overallHealth": 51, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/MarketIndicators.tsx", "metrics": {"lines": 90, "imports": 5, "complexity": 6, "responsibilities": 1}, "scores": {"lines": 70, "imports": 83.33333333333334, "complexity": 80, "responsibilities": 87.5}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/MarketNews.tsx", "metrics": {"lines": 153, "imports": 6, "complexity": 12, "responsibilities": 2}, "scores": {"lines": 49, "imports": 80, "complexity": 60, "responsibilities": 75}, "overallHealth": 66, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/MarketOverview.tsx", "metrics": {"lines": 119, "imports": 9, "complexity": 16, "responsibilities": 4}, "scores": {"lines": 60.333333333333336, "imports": 70, "complexity": 46.666666666666664, "responsibilities": 50}, "overallHealth": 57, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/MarketSummary.tsx", "metrics": {"lines": 93, "imports": 6, "complexity": 9, "responsibilities": 2}, "scores": {"lines": 69, "imports": 80, "complexity": 70, "responsibilities": 75}, "overallHealth": 74, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/PlanItemsList.tsx", "metrics": {"lines": 199, "imports": 7, "complexity": 62, "responsibilities": 3}, "scores": {"lines": 33.66666666666667, "imports": 76.66666666666667, "complexity": 0, "responsibilities": 62.5}, "overallHealth": 43, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (62). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/components/RiskManagementGrid.tsx", "metrics": {"lines": 134, "imports": 6, "complexity": 32, "responsibilities": 2}, "scores": {"lines": 55.333333333333336, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 53, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (32). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/components/TradingPlan.tsx", "metrics": {"lines": 34, "imports": 5, "complexity": 8, "responsibilities": 2}, "scores": {"lines": 88.66666666666667, "imports": 83.33333333333334, "complexity": 73.33333333333333, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/TradingPlanContainer.tsx", "metrics": {"lines": 222, "imports": 12, "complexity": 55, "responsibilities": 4}, "scores": {"lines": 26, "imports": 60, "complexity": 0, "responsibilities": 50}, "overallHealth": 34, "detectedPatterns": ["container", "component"], "recommendations": [{"type": "warning", "message": "Component is getting large (222 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (55). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/components/TradingPlanHeader.tsx", "metrics": {"lines": 129, "imports": 6, "complexity": 40, "responsibilities": 2}, "scores": {"lines": 57, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 53, "detectedPatterns": ["header", "component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (40). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/components/__tests__/DailyGuide.test.tsx", "metrics": {"lines": 135, "imports": 5, "complexity": 3, "responsibilities": 3}, "scores": {"lines": 55, "imports": 83.33333333333334, "complexity": 90, "responsibilities": 62.5}, "overallHealth": 73, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/index.ts", "metrics": {"lines": 18, "imports": 10, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 94, "imports": 66.66666666666667, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 89, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/daily-guide/components/ui/PriorityTag.tsx", "metrics": {"lines": 50, "imports": 4, "complexity": 7, "responsibilities": 2}, "scores": {"lines": 83.33333333333334, "imports": 86.66666666666667, "complexity": 76.66666666666667, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/ui/SectionCard.tsx", "metrics": {"lines": 85, "imports": 3, "complexity": 10, "responsibilities": 3}, "scores": {"lines": 71.66666666666667, "imports": 90, "complexity": 66.66666666666667, "responsibilities": 62.5}, "overallHealth": 73, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/ui/SentimentBadge.tsx", "metrics": {"lines": 50, "imports": 4, "complexity": 7, "responsibilities": 1}, "scores": {"lines": 83.33333333333334, "imports": 86.66666666666667, "complexity": 76.66666666666667, "responsibilities": 87.5}, "overallHealth": 84, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/components/ui/index.ts", "metrics": {"lines": 8, "imports": 3, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 97.33333333333333, "imports": 90, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 95, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/daily-guide/context/DailyGuideContext.tsx", "metrics": {"lines": 130, "imports": 5, "complexity": 13, "responsibilities": 4}, "scores": {"lines": 56.666666666666664, "imports": 83.33333333333334, "complexity": 56.666666666666664, "responsibilities": 50}, "overallHealth": 62, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/hooks/__tests__/useDailyGuide.test.tsx", "metrics": {"lines": 118, "imports": 5, "complexity": 1, "responsibilities": 2}, "scores": {"lines": 60.66666666666667, "imports": 83.33333333333334, "complexity": 96.66666666666667, "responsibilities": 75}, "overallHealth": 79, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/daily-guide/hooks/index.ts", "metrics": {"lines": 6, "imports": 1, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 98, "imports": 96.66666666666667, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 97, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/daily-guide/hooks/useDailyGuide.ts", "metrics": {"lines": 316, "imports": 4, "complexity": 13, "responsibilities": 4}, "scores": {"lines": 0, "imports": 86.66666666666667, "complexity": 56.666666666666664, "responsibilities": 50}, "overallHealth": 48, "detectedPatterns": ["hook"], "recommendations": [{"type": "critical", "message": "Component is too large (316 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/hooks/useTradingPlanForm.ts", "metrics": {"lines": 85, "imports": 5, "complexity": 10, "responsibilities": 1}, "scores": {"lines": 71.66666666666667, "imports": 83.33333333333334, "complexity": 66.66666666666667, "responsibilities": 87.5}, "overallHealth": 77, "detectedPatterns": ["form", "hook"], "recommendations": []}, {"filePath": "features/daily-guide/index.ts", "metrics": {"lines": 21, "imports": 8, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 93, "imports": 73.33333333333333, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 91, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/daily-guide/state/dailyGuideSelectors.ts", "metrics": {"lines": 160, "imports": 30, "complexity": 18, "responsibilities": 1}, "scores": {"lines": 46.666666666666664, "imports": 0, "complexity": 40, "responsibilities": 87.5}, "overallHealth": 44, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/daily-guide/state/dailyGuideState.ts", "metrics": {"lines": 303, "imports": 19, "complexity": 18, "responsibilities": 2}, "scores": {"lines": 0, "imports": 36.66666666666667, "complexity": 40, "responsibilities": 75}, "overallHealth": 38, "detectedPatterns": [], "recommendations": [{"type": "critical", "message": "Component is too large (303 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/daily-guide/state/index.ts", "metrics": {"lines": 7, "imports": 2, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 97.66666666666667, "imports": 93.33333333333333, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 96, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/daily-guide/types.ts", "metrics": {"lines": 199, "imports": 14, "complexity": 19, "responsibilities": 2}, "scores": {"lines": 33.66666666666667, "imports": 53.333333333333336, "complexity": 36.66666666666667, "responsibilities": 75}, "overallHealth": 50, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/performance-dashboard/Dashboard.tsx", "metrics": {"lines": 83, "imports": 7, "complexity": 2, "responsibilities": 3}, "scores": {"lines": 72.33333333333333, "imports": 76.66666666666667, "complexity": 93.33333333333333, "responsibilities": 62.5}, "overallHealth": 76, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/performance-dashboard/DashboardComposed.tsx", "metrics": {"lines": 103, "imports": 8, "complexity": 3, "responsibilities": 4}, "scores": {"lines": 65.66666666666666, "imports": 73.33333333333333, "complexity": 90, "responsibilities": 50}, "overallHealth": 70, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/performance-dashboard/components/MetricsPanel.tsx", "metrics": {"lines": 67, "imports": 3, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 77.66666666666667, "imports": 90, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 86, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/performance-dashboard/components/PerformanceChart.tsx", "metrics": {"lines": 47, "imports": 3, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 84.33333333333333, "imports": 90, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 88, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/performance-dashboard/components/RecentTradesPanel.tsx", "metrics": {"lines": 119, "imports": 4, "complexity": 17, "responsibilities": 1}, "scores": {"lines": 60.333333333333336, "imports": 86.66666666666667, "complexity": 43.333333333333336, "responsibilities": 87.5}, "overallHealth": 69, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/performance-dashboard/hooks/useDashboardData.ts", "metrics": {"lines": 105, "imports": 3, "complexity": 11, "responsibilities": 3}, "scores": {"lines": 65, "imports": 90, "complexity": 63.333333333333336, "responsibilities": 62.5}, "overallHealth": 70, "detectedPatterns": ["hook"], "recommendations": []}, {"filePath": "features/performance-dashboard/index.ts", "metrics": {"lines": 6, "imports": 1, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 98, "imports": 96.66666666666667, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 97, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/settings/Settings.tsx", "metrics": {"lines": 237, "imports": 4, "complexity": 2, "responsibilities": 2}, "scores": {"lines": 21, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 75}, "overallHealth": 69, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (237 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/settings/components/SettingItem.tsx", "metrics": {"lines": 61, "imports": 3, "complexity": 4, "responsibilities": 1}, "scores": {"lines": 79.66666666666667, "imports": 90, "complexity": 86.66666666666667, "responsibilities": 87.5}, "overallHealth": 86, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/settings/components/SettingsSection.tsx", "metrics": {"lines": 41, "imports": 3, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 86.33333333333333, "imports": 90, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 88, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/settings/components/ToggleSwitch.tsx", "metrics": {"lines": 74, "imports": 3, "complexity": 6, "responsibilities": 2}, "scores": {"lines": 75.33333333333333, "imports": 90, "complexity": 80, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/settings/hooks/useSettings.ts", "metrics": {"lines": 55, "imports": 3, "complexity": 4, "responsibilities": 1}, "scores": {"lines": 81.66666666666667, "imports": 90, "complexity": 86.66666666666667, "responsibilities": 87.5}, "overallHealth": 86, "detectedPatterns": ["hook"], "recommendations": []}, {"filePath": "features/settings/index.ts", "metrics": {"lines": 10, "imports": 5, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 96.66666666666667, "imports": 83.33333333333334, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 93, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-analysis/TradeAnalysis.tsx", "metrics": {"lines": 23, "imports": 4, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 92.33333333333333, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 93, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/TradeAnalysisComposed.tsx", "metrics": {"lines": 244, "imports": 11, "complexity": 13, "responsibilities": 4}, "scores": {"lines": 18.66666666666667, "imports": 63.333333333333336, "complexity": 56.666666666666664, "responsibilities": 50}, "overallHealth": 47, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (244 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-analysis/components/AnalysisHeader.tsx", "metrics": {"lines": 138, "imports": 6, "complexity": 34, "responsibilities": 2}, "scores": {"lines": 54, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 52, "detectedPatterns": ["header", "component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (34). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/components/AnalysisTabs.tsx", "metrics": {"lines": 136, "imports": 6, "complexity": 43, "responsibilities": 2}, "scores": {"lines": 54.66666666666667, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 52, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (43). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/components/CategoryPerformanceChart.tsx", "metrics": {"lines": 234, "imports": 6, "complexity": 37, "responsibilities": 3}, "scores": {"lines": 22, "imports": 80, "complexity": 0, "responsibilities": 62.5}, "overallHealth": 41, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (234 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (37). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/components/CategoryPerformanceChartRefactored.tsx", "metrics": {"lines": 175, "imports": 7, "complexity": 47, "responsibilities": 2}, "scores": {"lines": 41.666666666666664, "imports": 76.66666666666667, "complexity": 0, "responsibilities": 75}, "overallHealth": 48, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (47). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/components/DistributionChart.tsx", "metrics": {"lines": 131, "imports": 5, "complexity": 7, "responsibilities": 2}, "scores": {"lines": 56.333333333333336, "imports": 83.33333333333334, "complexity": 76.66666666666667, "responsibilities": 75}, "overallHealth": 73, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/EquityCurve.tsx", "metrics": {"lines": 164, "imports": 5, "complexity": 13, "responsibilities": 1}, "scores": {"lines": 45.333333333333336, "imports": 83.33333333333334, "complexity": 56.666666666666664, "responsibilities": 87.5}, "overallHealth": 68, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/FilterPanel.tsx", "metrics": {"lines": 276, "imports": 6, "complexity": 23, "responsibilities": 4}, "scores": {"lines": 8, "imports": 80, "complexity": 23.33333333333333, "responsibilities": 50}, "overallHealth": 40, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (276 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-analysis/components/MetricsPanel.tsx", "metrics": {"lines": 92, "imports": 4, "complexity": 4, "responsibilities": 1}, "scores": {"lines": 69.33333333333334, "imports": 86.66666666666667, "complexity": 86.66666666666667, "responsibilities": 87.5}, "overallHealth": 83, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/PerformanceSummary.tsx", "metrics": {"lines": 118, "imports": 6, "complexity": 11, "responsibilities": 1}, "scores": {"lines": 60.66666666666667, "imports": 80, "complexity": 63.333333333333336, "responsibilities": 87.5}, "overallHealth": 73, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/TabContentRenderer.tsx", "metrics": {"lines": 183, "imports": 11, "complexity": 51, "responsibilities": 2}, "scores": {"lines": 39, "imports": 63.333333333333336, "complexity": 0, "responsibilities": 75}, "overallHealth": 44, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (51). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/components/TimePerformanceChart.tsx", "metrics": {"lines": 149, "imports": 6, "complexity": 13, "responsibilities": 1}, "scores": {"lines": 50.333333333333336, "imports": 80, "complexity": 56.666666666666664, "responsibilities": 87.5}, "overallHealth": 69, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/TradeAnalysis.tsx", "metrics": {"lines": 131, "imports": 10, "complexity": 7, "responsibilities": 4}, "scores": {"lines": 56.333333333333336, "imports": 66.66666666666667, "complexity": 76.66666666666667, "responsibilities": 50}, "overallHealth": 62, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/TradeAnalysisCharts.tsx", "metrics": {"lines": 274, "imports": 5, "complexity": 9, "responsibilities": 1}, "scores": {"lines": 8.666666666666671, "imports": 83.33333333333334, "complexity": 70, "responsibilities": 87.5}, "overallHealth": 62, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (274 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-analysis/components/TradeAnalysisContainer.tsx", "metrics": {"lines": 159, "imports": 11, "complexity": 40, "responsibilities": 4}, "scores": {"lines": 47, "imports": 63.333333333333336, "complexity": 0, "responsibilities": 50}, "overallHealth": 40, "detectedPatterns": ["container", "component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (40). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/components/TradeAnalysisFilter.tsx", "metrics": {"lines": 150, "imports": 5, "complexity": 18, "responsibilities": 3}, "scores": {"lines": 50, "imports": 83.33333333333334, "complexity": 40, "responsibilities": 62.5}, "overallHealth": 59, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/TradeAnalysisSummary.tsx", "metrics": {"lines": 120, "imports": 5, "complexity": 5, "responsibilities": 1}, "scores": {"lines": 60, "imports": 83.33333333333334, "complexity": 83.33333333333334, "responsibilities": 87.5}, "overallHealth": 79, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/TradeAnalysisTable.tsx", "metrics": {"lines": 153, "imports": 5, "complexity": 16, "responsibilities": 2}, "scores": {"lines": 49, "imports": 83.33333333333334, "complexity": 46.666666666666664, "responsibilities": 75}, "overallHealth": 64, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/components/TradeDetail.tsx", "metrics": {"lines": 202, "imports": 6, "complexity": 17, "responsibilities": 2}, "scores": {"lines": 32.66666666666667, "imports": 80, "complexity": 43.333333333333336, "responsibilities": 75}, "overallHealth": 58, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (202 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-analysis/components/TradesTable.tsx", "metrics": {"lines": 287, "imports": 6, "complexity": 46, "responsibilities": 5}, "scores": {"lines": 4.333333333333329, "imports": 80, "complexity": 0, "responsibilities": 37.5}, "overallHealth": 30, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (287 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (46). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/components/index.ts", "metrics": {"lines": 29, "imports": 19, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 90.33333333333333, "imports": 36.66666666666667, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 80, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-analysis/components/trade-tests/TradeAnalysis.test.tsx", "metrics": {"lines": 84, "imports": 4, "complexity": 1, "responsibilities": 2}, "scores": {"lines": 72, "imports": 86.66666666666667, "complexity": 96.66666666666667, "responsibilities": 75}, "overallHealth": 83, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/hooks/TradeAnalysisContext.tsx", "metrics": {"lines": 197, "imports": 6, "complexity": 14, "responsibilities": 4}, "scores": {"lines": 34.33333333333334, "imports": 80, "complexity": 53.333333333333336, "responsibilities": 50}, "overallHealth": 54, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-analysis/hooks/tradeAnalysisState.ts", "metrics": {"lines": 392, "imports": 30, "complexity": 77, "responsibilities": 1}, "scores": {"lines": 0, "imports": 0, "complexity": 0, "responsibilities": 87.5}, "overallHealth": 22, "detectedPatterns": [], "recommendations": [{"type": "critical", "message": "Component is too large (392 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}, {"type": "critical", "message": "High cyclomatic complexity (77). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/hooks/useTradeAnalysis.ts", "metrics": {"lines": 227, "imports": 5, "complexity": 22, "responsibilities": 5}, "scores": {"lines": 24.33333333333333, "imports": 83.33333333333334, "complexity": 26.66666666666667, "responsibilities": 37.5}, "overallHealth": 43, "detectedPatterns": ["hook"], "recommendations": [{"type": "warning", "message": "Component is getting large (227 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-analysis/index.ts", "metrics": {"lines": 31, "imports": 17, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 89.66666666666667, "imports": 43.333333333333336, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 82, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-analysis/services/tradeAnalysisApi.ts", "metrics": {"lines": 356, "imports": 3, "complexity": 71, "responsibilities": 2}, "scores": {"lines": 0, "imports": 90, "complexity": 0, "responsibilities": 75}, "overallHealth": 41, "detectedPatterns": [], "recommendations": [{"type": "critical", "message": "Component is too large (356 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}, {"type": "critical", "message": "High cyclomatic complexity (71). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/trade-tests/TradeAnalysis.test.tsx", "metrics": {"lines": 311, "imports": 6, "complexity": 4, "responsibilities": 4}, "scores": {"lines": 0, "imports": 80, "complexity": 86.66666666666667, "responsibilities": 50}, "overallHealth": 54, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "Component is too large (311 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-analysis/types/index.ts", "metrics": {"lines": 19, "imports": 4, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 93.66666666666667, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 93, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-analysis/types.ts", "metrics": {"lines": 156, "imports": 17, "complexity": 17, "responsibilities": 1}, "scores": {"lines": 48, "imports": 43.333333333333336, "complexity": 43.333333333333336, "responsibilities": 87.5}, "overallHealth": 56, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-entry/components/SetupBuilder.tsx", "metrics": {"lines": 219, "imports": 4, "complexity": 19, "responsibilities": 4}, "scores": {"lines": 27, "imports": 86.66666666666667, "complexity": 36.66666666666667, "responsibilities": 50}, "overallHealth": 50, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (219 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-journal/TradeForm.test.tsx", "metrics": {"lines": 209, "imports": 7, "complexity": 5, "responsibilities": 2}, "scores": {"lines": 30.33333333333333, "imports": 76.66666666666667, "complexity": 83.33333333333334, "responsibilities": 75}, "overallHealth": 66, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (209 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-journal/TradeForm.tsx", "metrics": {"lines": 173, "imports": 10, "complexity": 8, "responsibilities": 4}, "scores": {"lines": 42.333333333333336, "imports": 66.66666666666667, "complexity": 73.33333333333333, "responsibilities": 50}, "overallHealth": 58, "detectedPatterns": ["form", "component"], "recommendations": []}, {"filePath": "features/trade-journal/TradeJournal.tsx", "metrics": {"lines": 64, "imports": 7, "complexity": 2, "responsibilities": 3}, "scores": {"lines": 78.66666666666666, "imports": 76.66666666666667, "complexity": 93.33333333333333, "responsibilities": 62.5}, "overallHealth": 78, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/SelectDropdown.tsx", "metrics": {"lines": 98, "imports": 3, "complexity": 11, "responsibilities": 2}, "scores": {"lines": 67.33333333333334, "imports": 90, "complexity": 63.333333333333336, "responsibilities": 75}, "overallHealth": 74, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/TabPanel.test.tsx", "metrics": {"lines": 101, "imports": 6, "complexity": 7, "responsibilities": 2}, "scores": {"lines": 66.33333333333334, "imports": 80, "complexity": 76.66666666666667, "responsibilities": 75}, "overallHealth": 75, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/TabPanel.tsx", "metrics": {"lines": 103, "imports": 3, "complexity": 17, "responsibilities": 3}, "scores": {"lines": 65.66666666666666, "imports": 90, "complexity": 43.333333333333336, "responsibilities": 62.5}, "overallHealth": 65, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/TimePicker.tsx", "metrics": {"lines": 89, "imports": 3, "complexity": 12, "responsibilities": 2}, "scores": {"lines": 70.33333333333333, "imports": 90, "complexity": 60, "responsibilities": 75}, "overallHealth": 74, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/TradeList.tsx", "metrics": {"lines": 217, "imports": 10, "complexity": 38, "responsibilities": 3}, "scores": {"lines": 27.666666666666657, "imports": 66.66666666666667, "complexity": 0, "responsibilities": 62.5}, "overallHealth": 39, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (217 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (38). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/components/index.ts", "metrics": {"lines": 23, "imports": 10, "complexity": 3, "responsibilities": 0}, "scores": {"lines": 92.33333333333333, "imports": 66.66666666666667, "complexity": 90, "responsibilities": 100}, "overallHealth": 87, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/DOLAnalysisComposed.tsx", "metrics": {"lines": 158, "imports": 5, "complexity": 20, "responsibilities": 3}, "scores": {"lines": 47.333333333333336, "imports": 83.33333333333334, "complexity": 33.33333333333334, "responsibilities": 62.5}, "overallHealth": 57, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/DOLContextSelector.tsx", "metrics": {"lines": 94, "imports": 4, "complexity": 6, "responsibilities": 2}, "scores": {"lines": 68.66666666666666, "imports": 86.66666666666667, "complexity": 80, "responsibilities": 75}, "overallHealth": 78, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/DOLDetailedAnalysis.tsx", "metrics": {"lines": 140, "imports": 4, "complexity": 16, "responsibilities": 2}, "scores": {"lines": 53.333333333333336, "imports": 86.66666666666667, "complexity": 46.666666666666664, "responsibilities": 75}, "overallHealth": 65, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/DOLEffectivenessRating.tsx", "metrics": {"lines": 171, "imports": 4, "complexity": 4, "responsibilities": 2}, "scores": {"lines": 43.00000000000001, "imports": 86.66666666666667, "complexity": 86.66666666666667, "responsibilities": 75}, "overallHealth": 73, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/DOLReactionSelector.tsx", "metrics": {"lines": 103, "imports": 4, "complexity": 2, "responsibilities": 2}, "scores": {"lines": 65.66666666666666, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/DOLStrengthSelector.tsx", "metrics": {"lines": 103, "imports": 4, "complexity": 2, "responsibilities": 2}, "scores": {"lines": 65.66666666666666, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/DOLTypeSelector.tsx", "metrics": {"lines": 103, "imports": 4, "complexity": 2, "responsibilities": 2}, "scores": {"lines": 65.66666666666666, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/TradeDOLAnalysis.tsx", "metrics": {"lines": 107, "imports": 4, "complexity": 20, "responsibilities": 3}, "scores": {"lines": 64.33333333333333, "imports": 86.66666666666667, "complexity": 33.33333333333334, "responsibilities": 62.5}, "overallHealth": 62, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-dol-analysis/index.ts", "metrics": {"lines": 17, "imports": 8, "complexity": 3, "responsibilities": 0}, "scores": {"lines": 94.33333333333333, "imports": 73.33333333333333, "complexity": 90, "responsibilities": 100}, "overallHealth": 89, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/TradeFormActions.tsx", "metrics": {"lines": 74, "imports": 5, "complexity": 6, "responsibilities": 2}, "scores": {"lines": 75.33333333333333, "imports": 83.33333333333334, "complexity": 80, "responsibilities": 75}, "overallHealth": 78, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/TradeFormBasicFields.tsx", "metrics": {"lines": 309, "imports": 6, "complexity": 18, "responsibilities": 3}, "scores": {"lines": 0, "imports": 80, "complexity": 40, "responsibilities": 62.5}, "overallHealth": 46, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "Component is too large (309 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/components/trade-form/TradeFormHeader.tsx", "metrics": {"lines": 38, "imports": 5, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 87.33333333333333, "imports": 83.33333333333334, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 87, "detectedPatterns": ["header", "component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/TradeFormLoading.tsx", "metrics": {"lines": 60, "imports": 4, "complexity": 3, "responsibilities": 2}, "scores": {"lines": 80, "imports": 86.66666666666667, "complexity": 90, "responsibilities": 75}, "overallHealth": 83, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/TradeFormMessages.tsx", "metrics": {"lines": 53, "imports": 4, "complexity": 9, "responsibilities": 2}, "scores": {"lines": 82.33333333333333, "imports": 86.66666666666667, "complexity": 70, "responsibilities": 75}, "overallHealth": 79, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/TradeFormRiskFields.tsx", "metrics": {"lines": 111, "imports": 6, "complexity": 6, "responsibilities": 2}, "scores": {"lines": 63, "imports": 80, "complexity": 80, "responsibilities": 75}, "overallHealth": 75, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx", "metrics": {"lines": 150, "imports": 8, "complexity": 6, "responsibilities": 3}, "scores": {"lines": 50, "imports": 73.33333333333333, "complexity": 80, "responsibilities": 62.5}, "overallHealth": 66, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/TradeFormTimingFields.tsx", "metrics": {"lines": 107, "imports": 8, "complexity": 10, "responsibilities": 3}, "scores": {"lines": 64.33333333333333, "imports": 73.33333333333333, "complexity": 66.66666666666667, "responsibilities": 62.5}, "overallHealth": 67, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-form/index.ts", "metrics": {"lines": 14, "imports": 9, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 95.33333333333333, "imports": 70, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 91, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-journal/TradeJournalContent.tsx", "metrics": {"lines": 86, "imports": 6, "complexity": 3, "responsibilities": 2}, "scores": {"lines": 71.33333333333333, "imports": 80, "complexity": 90, "responsibilities": 75}, "overallHealth": 79, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-journal/TradeJournalFilters.tsx", "metrics": {"lines": 295, "imports": 5, "complexity": 2, "responsibilities": 2}, "scores": {"lines": 1.6666666666666714, "imports": 83.33333333333334, "complexity": 93.33333333333333, "responsibilities": 75}, "overallHealth": 63, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (295 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-journal/components/trade-journal/TradeJournalHeader.tsx", "metrics": {"lines": 85, "imports": 5, "complexity": 5, "responsibilities": 2}, "scores": {"lines": 71.66666666666667, "imports": 83.33333333333334, "complexity": 83.33333333333334, "responsibilities": 75}, "overallHealth": 78, "detectedPatterns": ["header", "component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-journal/index.ts", "metrics": {"lines": 9, "imports": 4, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 97, "imports": 86.66666666666667, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 95, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-list/TradeListEmpty.tsx", "metrics": {"lines": 66, "imports": 5, "complexity": 5, "responsibilities": 1}, "scores": {"lines": 78, "imports": 83.33333333333334, "complexity": 83.33333333333334, "responsibilities": 87.5}, "overallHealth": 83, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-list/TradeListExpandedRow.tsx", "metrics": {"lines": 186, "imports": 5, "complexity": 26, "responsibilities": 1}, "scores": {"lines": 38, "imports": 83.33333333333334, "complexity": 13.333333333333329, "responsibilities": 87.5}, "overallHealth": 56, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-list/TradeListHeader.tsx", "metrics": {"lines": 44, "imports": 5, "complexity": 2, "responsibilities": 1}, "scores": {"lines": 85.33333333333333, "imports": 83.33333333333334, "complexity": 93.33333333333333, "responsibilities": 87.5}, "overallHealth": 87, "detectedPatterns": ["header", "component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-list/TradeListLoading.tsx", "metrics": {"lines": 50, "imports": 4, "complexity": 3, "responsibilities": 2}, "scores": {"lines": 83.33333333333334, "imports": 86.66666666666667, "complexity": 90, "responsibilities": 75}, "overallHealth": 84, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-list/TradeListRow.tsx", "metrics": {"lines": 47, "imports": 5, "complexity": 4, "responsibilities": 2}, "scores": {"lines": 84.33333333333333, "imports": 83.33333333333334, "complexity": 86.66666666666667, "responsibilities": 75}, "overallHealth": 82, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-list/index.ts", "metrics": {"lines": 11, "imports": 6, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 96.33333333333333, "imports": 80, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 93, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-pattern-quality/CriterionSelector.tsx", "metrics": {"lines": 107, "imports": 5, "complexity": 3, "responsibilities": 2}, "scores": {"lines": 64.33333333333333, "imports": 83.33333333333334, "complexity": 90, "responsibilities": 75}, "overallHealth": 78, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-pattern-quality/PatternQualityAssessment.tsx", "metrics": {"lines": 251, "imports": 6, "complexity": 29, "responsibilities": 4}, "scores": {"lines": 16.33333333333333, "imports": 80, "complexity": 3.3333333333333286, "responsibilities": 50}, "overallHealth": 37, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (251 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trade-journal/components/trade-pattern-quality/index.ts", "metrics": {"lines": 8, "imports": 2, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 97.33333333333333, "imports": 93.33333333333333, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 96, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/DOLTargetSelector.tsx", "metrics": {"lines": 158, "imports": 4, "complexity": 14, "responsibilities": 4}, "scores": {"lines": 47.333333333333336, "imports": 86.66666666666667, "complexity": 53.333333333333336, "responsibilities": 50}, "overallHealth": 59, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/FVGSelector.tsx", "metrics": {"lines": 193, "imports": 4, "complexity": 6, "responsibilities": 2}, "scores": {"lines": 35.66666666666667, "imports": 86.66666666666667, "complexity": 80, "responsibilities": 75}, "overallHealth": 69, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/LiquiditySelector.tsx", "metrics": {"lines": 83, "imports": 4, "complexity": 3, "responsibilities": 2}, "scores": {"lines": 72.33333333333333, "imports": 86.66666666666667, "complexity": 90, "responsibilities": 75}, "overallHealth": 81, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/ParentPDArraySelector.tsx", "metrics": {"lines": 84, "imports": 4, "complexity": 4, "responsibilities": 2}, "scores": {"lines": 72, "imports": 86.66666666666667, "complexity": 86.66666666666667, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/PrimarySetupSelector.tsx", "metrics": {"lines": 151, "imports": 4, "complexity": 9, "responsibilities": 4}, "scores": {"lines": 49.66666666666667, "imports": 86.66666666666667, "complexity": 70, "responsibilities": 50}, "overallHealth": 64, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/SecondarySetupSelector.tsx", "metrics": {"lines": 167, "imports": 4, "complexity": 13, "responsibilities": 5}, "scores": {"lines": 44.333333333333336, "imports": 86.66666666666667, "complexity": 56.666666666666664, "responsibilities": 37.5}, "overallHealth": 56, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/SetupClassificationComposed.tsx", "metrics": {"lines": 169, "imports": 5, "complexity": 16, "responsibilities": 3}, "scores": {"lines": 43.666666666666664, "imports": 83.33333333333334, "complexity": 46.666666666666664, "responsibilities": 62.5}, "overallHealth": 59, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/SetupClassificationSection.tsx", "metrics": {"lines": 109, "imports": 4, "complexity": 16, "responsibilities": 4}, "scores": {"lines": 63.666666666666664, "imports": 86.66666666666667, "complexity": 46.666666666666664, "responsibilities": 50}, "overallHealth": 62, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trade-journal/components/trade-setup-classification/index.ts", "metrics": {"lines": 17, "imports": 8, "complexity": 3, "responsibilities": 0}, "scores": {"lines": 94.33333333333333, "imports": 73.33333333333333, "complexity": 90, "responsibilities": 100}, "overallHealth": 89, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/constants/dolAnalysis.ts", "metrics": {"lines": 129, "imports": 15, "complexity": 34, "responsibilities": 0}, "scores": {"lines": 57, "imports": 50, "complexity": 0, "responsibilities": 100}, "overallHealth": 52, "detectedPatterns": [], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (34). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/constants/patternQuality.ts", "metrics": {"lines": 137, "imports": 8, "complexity": 25, "responsibilities": 0}, "scores": {"lines": 54.333333333333336, "imports": 73.33333333333333, "complexity": 16.666666666666657, "responsibilities": 100}, "overallHealth": 61, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/constants/setupClassification.ts", "metrics": {"lines": 184, "imports": 16, "complexity": 9, "responsibilities": 0}, "scores": {"lines": 38.66666666666667, "imports": 46.666666666666664, "complexity": 70, "responsibilities": 100}, "overallHealth": 64, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/hooks/index.ts", "metrics": {"lines": 27, "imports": 11, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 91, "imports": 63.333333333333336, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 88, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/hooks/useTradeCalculations.ts", "metrics": {"lines": 66, "imports": 5, "complexity": 17, "responsibilities": 2}, "scores": {"lines": 78, "imports": 83.33333333333334, "complexity": 43.333333333333336, "responsibilities": 75}, "overallHealth": 70, "detectedPatterns": ["hook"], "recommendations": []}, {"filePath": "features/trade-journal/hooks/useTradeFilters.ts", "metrics": {"lines": 208, "imports": 5, "complexity": 58, "responsibilities": 1}, "scores": {"lines": 30.666666666666657, "imports": 83.33333333333334, "complexity": 0, "responsibilities": 87.5}, "overallHealth": 50, "detectedPatterns": ["hook"], "recommendations": [{"type": "warning", "message": "Component is getting large (208 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (58). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/hooks/useTradeForm.ts", "metrics": {"lines": 163, "imports": 14, "complexity": 22, "responsibilities": 3}, "scores": {"lines": 45.666666666666664, "imports": 53.333333333333336, "complexity": 26.66666666666667, "responsibilities": 62.5}, "overallHealth": 47, "detectedPatterns": ["form", "hook"], "recommendations": []}, {"filePath": "features/trade-journal/hooks/useTradeFormData.ts", "metrics": {"lines": 234, "imports": 5, "complexity": 62, "responsibilities": 4}, "scores": {"lines": 22, "imports": 83.33333333333334, "complexity": 0, "responsibilities": 50}, "overallHealth": 39, "detectedPatterns": ["hook"], "recommendations": [{"type": "warning", "message": "Component is getting large (234 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (62). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/hooks/useTradeJournal.ts", "metrics": {"lines": 83, "imports": 5, "complexity": 5, "responsibilities": 5}, "scores": {"lines": 72.33333333333333, "imports": 83.33333333333334, "complexity": 83.33333333333334, "responsibilities": 37.5}, "overallHealth": 69, "detectedPatterns": ["hook"], "recommendations": []}, {"filePath": "features/trade-journal/hooks/useTradeList.ts", "metrics": {"lines": 45, "imports": 5, "complexity": 7, "responsibilities": 1}, "scores": {"lines": 85, "imports": 83.33333333333334, "complexity": 76.66666666666667, "responsibilities": 87.5}, "overallHealth": 83, "detectedPatterns": ["hook"], "recommendations": []}, {"filePath": "features/trade-journal/hooks/useTradeSubmission.ts", "metrics": {"lines": 228, "imports": 6, "complexity": 71, "responsibilities": 2}, "scores": {"lines": 24, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 45, "detectedPatterns": ["hook"], "recommendations": [{"type": "warning", "message": "Component is getting large (228 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (71). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/hooks/useTradeValidation.ts", "metrics": {"lines": 149, "imports": 6, "complexity": 49, "responsibilities": 2}, "scores": {"lines": 50.333333333333336, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 51, "detectedPatterns": ["hook"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (49). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/index.ts", "metrics": {"lines": 8, "imports": 3, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 97.33333333333333, "imports": 90, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 95, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trade-journal/tests/useTradeSubmission.test.ts", "metrics": {"lines": 385, "imports": 5, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 0, "imports": 83.33333333333334, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 65, "detectedPatterns": ["hook"], "recommendations": [{"type": "critical", "message": "Component is too large (385 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trade-journal/types/index.ts", "metrics": {"lines": 260, "imports": 25, "complexity": 7, "responsibilities": 1}, "scores": {"lines": 13.333333333333329, "imports": 16.666666666666657, "complexity": 76.66666666666667, "responsibilities": 87.5}, "overallHealth": 49, "detectedPatterns": [], "recommendations": [{"type": "warning", "message": "Component is getting large (260 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trading-dashboard/TradingDashboard.tsx", "metrics": {"lines": 343, "imports": 11, "complexity": 18, "responsibilities": 6}, "scores": {"lines": 0, "imports": 63.333333333333336, "complexity": 40, "responsibilities": 25}, "overallHealth": 32, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "Component is too large (343 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/TradingDashboardComposed.tsx", "metrics": {"lines": 260, "imports": 6, "complexity": 14, "responsibilities": 5}, "scores": {"lines": 13.333333333333329, "imports": 80, "complexity": 53.333333333333336, "responsibilities": 37.5}, "overallHealth": 46, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (260 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trading-dashboard/TradingDashboardRefactored.tsx", "metrics": {"lines": 35, "imports": 5, "complexity": 3, "responsibilities": 0}, "scores": {"lines": 88.33333333333333, "imports": 83.33333333333334, "complexity": 90, "responsibilities": 100}, "overallHealth": 90, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trading-dashboard/TradingDashboardWithFeatureFlag.tsx", "metrics": {"lines": 249, "imports": 8, "complexity": 87, "responsibilities": 5}, "scores": {"lines": 17, "imports": 73.33333333333333, "complexity": 0, "responsibilities": 37.5}, "overallHealth": 32, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (249 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (87). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/components/DashboardTabs.tsx", "metrics": {"lines": 252, "imports": 7, "complexity": 84, "responsibilities": 4}, "scores": {"lines": 16, "imports": 76.66666666666667, "complexity": 0, "responsibilities": 50}, "overallHealth": 36, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (252 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (84). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/components/F1Header.tsx", "metrics": {"lines": 229, "imports": 6, "complexity": 78, "responsibilities": 2}, "scores": {"lines": 23.66666666666667, "imports": 80, "complexity": 0, "responsibilities": 75}, "overallHealth": 45, "detectedPatterns": ["header", "component"], "recommendations": [{"type": "warning", "message": "Component is getting large (229 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (78). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/components/MetricsPanel.tsx", "metrics": {"lines": 164, "imports": 5, "complexity": 8, "responsibilities": 3}, "scores": {"lines": 45.333333333333336, "imports": 83.33333333333334, "complexity": 73.33333333333333, "responsibilities": 62.5}, "overallHealth": 66, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trading-dashboard/components/PerformanceChart.tsx", "metrics": {"lines": 147, "imports": 6, "complexity": 9, "responsibilities": 1}, "scores": {"lines": 51, "imports": 80, "complexity": 70, "responsibilities": 87.5}, "overallHealth": 72, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trading-dashboard/components/QuickTradeForm.tsx", "metrics": {"lines": 37, "imports": 5, "complexity": 6, "responsibilities": 1}, "scores": {"lines": 87.66666666666667, "imports": 83.33333333333334, "complexity": 80, "responsibilities": 87.5}, "overallHealth": 85, "detectedPatterns": ["form", "component"], "recommendations": []}, {"filePath": "features/trading-dashboard/components/QuickTradeFormActions.tsx", "metrics": {"lines": 183, "imports": 5, "complexity": 63, "responsibilities": 3}, "scores": {"lines": 39, "imports": 83.33333333333334, "complexity": 0, "responsibilities": 62.5}, "overallHealth": 46, "detectedPatterns": ["component"], "recommendations": [{"type": "critical", "message": "High cyclomatic complexity (63). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/components/QuickTradeFormContainer.tsx", "metrics": {"lines": 134, "imports": 9, "complexity": 8, "responsibilities": 2}, "scores": {"lines": 55.333333333333336, "imports": 70, "complexity": 73.33333333333333, "responsibilities": 75}, "overallHealth": 68, "detectedPatterns": ["container", "component"], "recommendations": []}, {"filePath": "features/trading-dashboard/components/QuickTradeFormFields.tsx", "metrics": {"lines": 181, "imports": 6, "complexity": 17, "responsibilities": 1}, "scores": {"lines": 39.666666666666664, "imports": 80, "complexity": 43.333333333333336, "responsibilities": 87.5}, "overallHealth": 63, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trading-dashboard/components/RecentTradesTable.tsx", "metrics": {"lines": 138, "imports": 5, "complexity": 8, "responsibilities": 2}, "scores": {"lines": 54, "imports": 83.33333333333334, "complexity": 73.33333333333333, "responsibilities": 75}, "overallHealth": 71, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trading-dashboard/components/SetupAnalysis.tsx", "metrics": {"lines": 172, "imports": 6, "complexity": 10, "responsibilities": 2}, "scores": {"lines": 42.666666666666664, "imports": 80, "complexity": 66.66666666666667, "responsibilities": 75}, "overallHealth": 66, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "features/trading-dashboard/components/TradingDashboardContainer.tsx", "metrics": {"lines": 290, "imports": 13, "complexity": 57, "responsibilities": 3}, "scores": {"lines": 3.3333333333333286, "imports": 56.666666666666664, "complexity": 0, "responsibilities": 62.5}, "overallHealth": 31, "detectedPatterns": ["container", "component"], "recommendations": [{"type": "warning", "message": "Component is getting large (290 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (57). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/components/index.ts", "metrics": {"lines": 27, "imports": 10, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 91, "imports": 66.66666666666667, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 88, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trading-dashboard/context/TradingDashboardContext.tsx", "metrics": {"lines": 230, "imports": 14, "complexity": 21, "responsibilities": 3}, "scores": {"lines": 23.33333333333333, "imports": 53.333333333333336, "complexity": 30, "responsibilities": 62.5}, "overallHealth": 42, "detectedPatterns": ["component"], "recommendations": [{"type": "warning", "message": "Component is getting large (230 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "features/trading-dashboard/hooks/useQuickTradeForm.ts", "metrics": {"lines": 282, "imports": 6, "complexity": 31, "responsibilities": 4}, "scores": {"lines": 6, "imports": 80, "complexity": 0, "responsibilities": 50}, "overallHealth": 34, "detectedPatterns": ["form", "hook"], "recommendations": [{"type": "warning", "message": "Component is getting large (282 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (31). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/hooks/useTradingDashboard.ts", "metrics": {"lines": 345, "imports": 6, "complexity": 43, "responsibilities": 4}, "scores": {"lines": 0, "imports": 80, "complexity": 0, "responsibilities": 50}, "overallHealth": 33, "detectedPatterns": ["hook"], "recommendations": [{"type": "critical", "message": "Component is too large (345 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}, {"type": "critical", "message": "High cyclomatic complexity (43). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/hooks/useTradingDashboardData.ts", "metrics": {"lines": 349, "imports": 8, "complexity": 70, "responsibilities": 5}, "scores": {"lines": 0, "imports": 73.33333333333333, "complexity": 0, "responsibilities": 37.5}, "overallHealth": 28, "detectedPatterns": ["hook"], "recommendations": [{"type": "critical", "message": "Component is too large (349 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}, {"type": "critical", "message": "High cyclomatic complexity (70). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "features/trading-dashboard/index.ts", "metrics": {"lines": 17, "imports": 8, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 94.33333333333333, "imports": 73.33333333333333, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 91, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trading-dashboard/types/index.ts", "metrics": {"lines": 67, "imports": 7, "complexity": 7, "responsibilities": 1}, "scores": {"lines": 77.66666666666667, "imports": 76.66666666666667, "complexity": 76.66666666666667, "responsibilities": 87.5}, "overallHealth": 80, "detectedPatterns": [], "recommendations": []}, {"filePath": "features/trading-dashboard/utils/dataValidation.ts", "metrics": {"lines": 328, "imports": 7, "complexity": 94, "responsibilities": 1}, "scores": {"lines": 0, "imports": 76.66666666666667, "complexity": 0, "responsibilities": 87.5}, "overallHealth": 41, "detectedPatterns": [], "recommendations": [{"type": "critical", "message": "Component is too large (328 lines). Consider applying container pattern.", "action": "refactor", "priority": "high"}, {"type": "critical", "message": "High cyclomatic complexity (94). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "index.tsx", "metrics": {"lines": 41, "imports": 8, "complexity": 4, "responsibilities": 2}, "scores": {"lines": 86.33333333333333, "imports": 73.33333333333333, "complexity": 86.66666666666667, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "layouts/Header.tsx", "metrics": {"lines": 142, "imports": 3, "complexity": 2, "responsibilities": 2}, "scores": {"lines": 52.666666666666664, "imports": 90, "complexity": 93.33333333333333, "responsibilities": 75}, "overallHealth": 78, "detectedPatterns": ["header", "component"], "recommendations": []}, {"filePath": "layouts/MainLayout.tsx", "metrics": {"lines": 108, "imports": 6, "complexity": 10, "responsibilities": 4}, "scores": {"lines": 64, "imports": 80, "complexity": 66.66666666666667, "responsibilities": 50}, "overallHealth": 65, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "layouts/Sidebar.tsx", "metrics": {"lines": 130, "imports": 4, "complexity": 10, "responsibilities": 1}, "scores": {"lines": 56.666666666666664, "imports": 86.66666666666667, "complexity": 66.66666666666667, "responsibilities": 87.5}, "overallHealth": 74, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "layouts/index.ts", "metrics": {"lines": 8, "imports": 3, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 97.33333333333333, "imports": 90, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 96, "detectedPatterns": [], "recommendations": []}, {"filePath": "pages/DailyGuide.tsx", "metrics": {"lines": 34, "imports": 3, "complexity": 1, "responsibilities": 1}, "scores": {"lines": 88.66666666666667, "imports": 90, "complexity": 96.66666666666667, "responsibilities": 87.5}, "overallHealth": 91, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "pages/Dashboard.tsx", "metrics": {"lines": 16, "imports": 3, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 94.66666666666667, "imports": 90, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 95, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "pages/NotFound.tsx", "metrics": {"lines": 55, "imports": 4, "complexity": 2, "responsibilities": 1}, "scores": {"lines": 81.66666666666667, "imports": 86.66666666666667, "complexity": 93.33333333333333, "responsibilities": 87.5}, "overallHealth": 87, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "pages/Settings.tsx", "metrics": {"lines": 34, "imports": 3, "complexity": 1, "responsibilities": 1}, "scores": {"lines": 88.66666666666667, "imports": 90, "complexity": 96.66666666666667, "responsibilities": 87.5}, "overallHealth": 91, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "pages/TradeAnalysis.tsx", "metrics": {"lines": 37, "imports": 5, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 87.66666666666667, "imports": 83.33333333333334, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 87, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "pages/TradeForm.tsx", "metrics": {"lines": 44, "imports": 4, "complexity": 8, "responsibilities": 2}, "scores": {"lines": 85.33333333333333, "imports": 86.66666666666667, "complexity": 73.33333333333333, "responsibilities": 75}, "overallHealth": 80, "detectedPatterns": ["form", "component"], "recommendations": []}, {"filePath": "pages/TradeJournal.tsx", "metrics": {"lines": 34, "imports": 3, "complexity": 1, "responsibilities": 1}, "scores": {"lines": 88.66666666666667, "imports": 90, "complexity": 96.66666666666667, "responsibilities": 87.5}, "overallHealth": 91, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "reportWebVitals.ts", "metrics": {"lines": 18, "imports": 2, "complexity": 4, "responsibilities": 0}, "scores": {"lines": 94, "imports": 93.33333333333333, "complexity": 86.66666666666667, "responsibilities": 100}, "overallHealth": 94, "detectedPatterns": [], "recommendations": []}, {"filePath": "routes/components/molecules/LoadingScreen.tsx", "metrics": {"lines": 47, "imports": 3, "complexity": 3, "responsibilities": 2}, "scores": {"lines": 84.33333333333333, "imports": 90, "complexity": 90, "responsibilities": 75}, "overallHealth": 85, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "routes/index.ts", "metrics": {"lines": 4, "imports": 1, "complexity": 1, "responsibilities": 0}, "scores": {"lines": 98.66666666666667, "imports": 96.66666666666667, "complexity": 96.66666666666667, "responsibilities": 100}, "overallHealth": 98, "detectedPatterns": [], "recommendations": []}, {"filePath": "routes/index.tsx", "metrics": {"lines": 55, "imports": 5, "complexity": 3, "responsibilities": 0}, "scores": {"lines": 81.66666666666667, "imports": 83.33333333333334, "complexity": 90, "responsibilities": 100}, "overallHealth": 89, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "routes/layouts/MainLayout.tsx", "metrics": {"lines": 86, "imports": 4, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 71.33333333333333, "imports": 86.66666666666667, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 84, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "routes/routes.test.tsx", "metrics": {"lines": 122, "imports": 7, "complexity": 14, "responsibilities": 2}, "scores": {"lines": 59.333333333333336, "imports": 76.66666666666667, "complexity": 53.333333333333336, "responsibilities": 75}, "overallHealth": 66, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "routes/routes.tsx", "metrics": {"lines": 44, "imports": 5, "complexity": 2, "responsibilities": 0}, "scores": {"lines": 85.33333333333333, "imports": 83.33333333333334, "complexity": 93.33333333333333, "responsibilities": 100}, "overallHealth": 91, "detectedPatterns": ["component"], "recommendations": []}, {"filePath": "services/contracts/TradeJournalApiImpl.ts", "metrics": {"lines": 206, "imports": 3, "complexity": 24, "responsibilities": 1}, "scores": {"lines": 31.33333333333333, "imports": 90, "complexity": 20, "responsibilities": 87.5}, "overallHealth": 57, "detectedPatterns": [], "recommendations": [{"type": "warning", "message": "Component is getting large (206 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}]}, {"filePath": "services/transformers/setupTransformer.ts", "metrics": {"lines": 205, "imports": 2, "complexity": 48, "responsibilities": 1}, "scores": {"lines": 31.66666666666667, "imports": 93.33333333333333, "complexity": 0, "responsibilities": 87.5}, "overallHealth": 53, "detectedPatterns": [], "recommendations": [{"type": "warning", "message": "Component is getting large (205 lines). Monitor for refactoring opportunities.", "action": "monitor", "priority": "medium"}, {"type": "critical", "message": "High cyclomatic complexity (48). Extract functions or use strategy pattern.", "action": "refactor", "priority": "high"}]}, {"filePath": "simple-index.tsx", "metrics": {"lines": 22, "imports": 4, "complexity": 3, "responsibilities": 1}, "scores": {"lines": 92.66666666666667, "imports": 86.66666666666667, "complexity": 90, "responsibilities": 87.5}, "overallHealth": 89, "detectedPatterns": ["component"], "recommendations": []}], "config": {"srcDir": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src", "outputFile": "/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/code-health/architecture-metrics.json", "thresholds": {"lines": {"good": 100, "warning": 200, "critical": 300}, "imports": {"good": 10, "warning": 20, "critical": 30}, "complexity": {"good": 10, "warning": 20, "critical": 30}, "responsibilities": {"good": 3, "warning": 5, "critical": 8}}, "patterns": {"container": {}, "header": {}, "form": {}, "hook": {}, "component": {}}}}