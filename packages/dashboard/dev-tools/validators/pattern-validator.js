/**
 * Pattern Validator
 * 
 * Validates that components follow our established patterns and architectural principles.
 * Provides auto-fix capabilities for common pattern violations.
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import { glob } from 'glob';

const PATTERN_RULES = {
  container: {
    name: 'Container Pattern',
    files: ['*Container.tsx'],
    required: [
      'F1Container',
      'Suspense',
      'error boundaries'
    ],
    forbidden: [
      'direct styled components',
      'complex business logic'
    ],
    maxLines: 150
  },
  
  form: {
    name: 'Form Pattern',
    files: ['*Form.tsx', '*FormFields.tsx'],
    required: [
      'F1Form',
      'F1FormField',
      'useFormField',
      'validation'
    ],
    forbidden: [
      'inline styles',
      'direct DOM manipulation'
    ],
    maxLines: 200
  },
  
  header: {
    name: 'Header Pattern',
    files: ['*Header.tsx'],
    required: [
      'F1Header',
      'F1 theme colors',
      'responsive design'
    ],
    forbidden: [
      'hardcoded colors',
      'fixed dimensions'
    ],
    maxLines: 100
  },
  
  f1Theme: {
    name: 'F1 Theme Compliance',
    files: ['*.tsx'],
    required: [
      'theme.colors',
      'theme.spacing'
    ],
    forbidden: [
      '#dc2626 (use theme.colors.primary)',
      '#4b5563 (use theme.colors.border)',
      'hardcoded spacing values'
    ]
  },
  
  typescript: {
    name: 'TypeScript Standards',
    files: ['*.tsx', '*.ts'],
    required: [
      'proper interfaces',
      'exported types',
      'JSDoc comments'
    ],
    forbidden: [
      'any type',
      'non-null assertions without justification'
    ]
  }
};

/**
 * Validate patterns across codebase
 */
export async function validatePatterns(options = {}) {
  try {
    const srcPath = path.resolve('src');
    const violations = [];
    
    if (!options.silent) {
      console.log(chalk.blue('🔍 Validating pattern compliance...'));
    }
    
    // Get all relevant files
    const files = await glob('**/*.{ts,tsx}', { 
      cwd: srcPath,
      ignore: ['**/*.test.*', '**/*.stories.*', '**/node_modules/**']
    });
    
    // Validate each pattern
    for (const [patternName, rule] of Object.entries(PATTERN_RULES)) {
      const patternViolations = await validatePattern(srcPath, files, patternName, rule);
      violations.push(...patternViolations);
    }
    
    // Display results
    if (!options.silent) {
      displayValidationResults(violations);
    }
    
    // Auto-fix if requested
    if (options.fix && violations.length > 0) {
      await autoFixViolations(violations);
    }
    
    // Generate report if requested
    if (options.report) {
      await generateValidationReport(violations);
    }
    
    return {
      totalFiles: files.length,
      violations: violations.length,
      patterns: Object.keys(PATTERN_RULES).length,
      success: violations.length === 0
    };
    
  } catch (error) {
    console.log(chalk.red(`❌ Validation failed: ${error.message}`));
    return null;
  }
}

/**
 * Validate specific pattern
 */
async function validatePattern(srcPath, files, patternName, rule) {
  const violations = [];
  
  // Filter files that match pattern
  const matchingFiles = files.filter(file => {
    return rule.files.some(pattern => {
      const regex = new RegExp(pattern.replace('*', '.*'));
      return regex.test(file);
    });
  });
  
  // Validate each matching file
  for (const file of matchingFiles) {
    const filePath = path.join(srcPath, file);
    const content = fs.readFileSync(filePath, 'utf8');
    const fileViolations = validateFile(filePath, content, patternName, rule);
    violations.push(...fileViolations);
  }
  
  return violations;
}

/**
 * Validate individual file against pattern
 */
function validateFile(filePath, content, patternName, rule) {
  const violations = [];
  const fileName = path.basename(filePath);
  const lines = content.split('\n');
  
  // Check required elements
  rule.required?.forEach(requirement => {
    if (!checkRequirement(content, requirement)) {
      violations.push({
        type: 'missing_requirement',
        pattern: patternName,
        file: filePath,
        message: `Missing required element: ${requirement}`,
        severity: 'error',
        line: null,
        fixable: isFixable(requirement)
      });
    }
  });
  
  // Check forbidden elements
  rule.forbidden?.forEach(forbidden => {
    const violation = checkForbidden(content, forbidden);
    if (violation) {
      violations.push({
        type: 'forbidden_element',
        pattern: patternName,
        file: filePath,
        message: `Forbidden element found: ${forbidden}`,
        severity: 'warning',
        line: violation.line,
        fixable: isFixable(forbidden)
      });
    }
  });
  
  // Check file size
  if (rule.maxLines && lines.length > rule.maxLines) {
    violations.push({
      type: 'size_violation',
      pattern: patternName,
      file: filePath,
      message: `File too large: ${lines.length} lines (max: ${rule.maxLines})`,
      severity: 'warning',
      line: null,
      fixable: false
    });
  }
  
  return violations;
}

/**
 * Check if requirement is met
 */
function checkRequirement(content, requirement) {
  const checks = {
    'F1Container': () => content.includes('F1Container'),
    'F1Form': () => content.includes('F1Form'),
    'F1Header': () => content.includes('F1Header'),
    'F1FormField': () => content.includes('F1FormField'),
    'useFormField': () => content.includes('useFormField'),
    'Suspense': () => content.includes('Suspense'),
    'error boundaries': () => content.includes('ErrorBoundary') || content.includes('componentDidCatch'),
    'validation': () => content.includes('validation') || content.includes('validate'),
    'theme.colors': () => content.includes('theme.colors'),
    'theme.spacing': () => content.includes('theme.spacing'),
    'F1 theme colors': () => content.includes('theme.colors'),
    'responsive design': () => content.includes('@media') || content.includes('responsive'),
    'proper interfaces': () => content.includes('interface ') && content.includes('export interface'),
    'exported types': () => content.includes('export type') || content.includes('export interface'),
    'JSDoc comments': () => content.includes('/**')
  };
  
  const check = checks[requirement];
  return check ? check() : false;
}

/**
 * Check for forbidden elements
 */
function checkForbidden(content, forbidden) {
  const lines = content.split('\n');
  
  const checks = {
    'direct styled components': () => {
      const regex = /const\s+\w+\s*=\s*styled\./;
      for (let i = 0; i < lines.length; i++) {
        if (regex.test(lines[i]) && !lines[i].includes('// allowed')) {
          return { line: i + 1 };
        }
      }
      return null;
    },
    'complex business logic': () => {
      // Check for complex logic in container files
      const complexPatterns = [/useEffect.*{[\s\S]{100,}}/g, /useState.*{[\s\S]{50,}}/g];
      for (const pattern of complexPatterns) {
        if (pattern.test(content)) {
          return { line: null };
        }
      }
      return null;
    },
    'inline styles': () => {
      const regex = /style\s*=\s*\{/;
      for (let i = 0; i < lines.length; i++) {
        if (regex.test(lines[i])) {
          return { line: i + 1 };
        }
      }
      return null;
    },
    'hardcoded colors': () => {
      const colorRegex = /#[0-9a-fA-F]{3,6}/;
      for (let i = 0; i < lines.length; i++) {
        if (colorRegex.test(lines[i]) && !lines[i].includes('// allowed')) {
          return { line: i + 1 };
        }
      }
      return null;
    },
    'any type': () => {
      const regex = /:\s*any\b/;
      for (let i = 0; i < lines.length; i++) {
        if (regex.test(lines[i]) && !lines[i].includes('// eslint-disable')) {
          return { line: i + 1 };
        }
      }
      return null;
    }
  };
  
  // Handle specific forbidden patterns
  if (forbidden.includes('#dc2626')) {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('#dc2626') && !lines[i].includes('// allowed')) {
        return { line: i + 1 };
      }
    }
  }
  
  if (forbidden.includes('#4b5563')) {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('#4b5563') && !lines[i].includes('// allowed')) {
        return { line: i + 1 };
      }
    }
  }
  
  const check = checks[forbidden];
  return check ? check() : null;
}

/**
 * Check if violation is auto-fixable
 */
function isFixable(element) {
  const fixableElements = [
    '#dc2626 (use theme.colors.primary)',
    '#4b5563 (use theme.colors.border)',
    'hardcoded colors',
    'inline styles',
    'any type'
  ];
  
  return fixableElements.some(fixable => element.includes(fixable) || fixable.includes(element));
}

/**
 * Display validation results
 */
function displayValidationResults(violations) {
  console.log(chalk.blue('\n📋 PATTERN VALIDATION RESULTS'));
  console.log(chalk.blue('=============================='));
  
  if (violations.length === 0) {
    console.log(chalk.green('✅ All patterns are compliant!'));
    return;
  }
  
  // Group violations by pattern
  const violationsByPattern = violations.reduce((acc, violation) => {
    if (!acc[violation.pattern]) {
      acc[violation.pattern] = [];
    }
    acc[violation.pattern].push(violation);
    return acc;
  }, {});
  
  // Display violations by pattern
  Object.entries(violationsByPattern).forEach(([pattern, patternViolations]) => {
    console.log(chalk.yellow(`\n🔍 ${PATTERN_RULES[pattern].name}:`));
    
    patternViolations.forEach(violation => {
      const severityColor = violation.severity === 'error' ? 'red' : 'yellow';
      const fixableIcon = violation.fixable ? '🔧' : '⚠️';
      const lineInfo = violation.line ? `:${violation.line}` : '';
      
      console.log(chalk[severityColor](`   ${fixableIcon} ${path.basename(violation.file)}${lineInfo}`));
      console.log(chalk.gray(`      ${violation.message}`));
    });
  });
  
  // Summary
  const errorCount = violations.filter(v => v.severity === 'error').length;
  const warningCount = violations.filter(v => v.severity === 'warning').length;
  const fixableCount = violations.filter(v => v.fixable).length;
  
  console.log(chalk.white('\n📊 Summary:'));
  console.log(chalk.red(`   Errors: ${errorCount}`));
  console.log(chalk.yellow(`   Warnings: ${warningCount}`));
  console.log(chalk.blue(`   Auto-fixable: ${fixableCount}`));
  
  if (fixableCount > 0) {
    console.log(chalk.gray('\n💡 Run with --fix to auto-fix violations'));
  }
}

/**
 * Auto-fix violations where possible
 */
async function autoFixViolations(violations) {
  console.log(chalk.blue('\n🔧 Auto-fixing violations...'));
  
  const fixableViolations = violations.filter(v => v.fixable);
  let fixedCount = 0;
  
  // Group by file for efficient fixing
  const violationsByFile = fixableViolations.reduce((acc, violation) => {
    if (!acc[violation.file]) {
      acc[violation.file] = [];
    }
    acc[violation.file].push(violation);
    return acc;
  }, {});
  
  // Fix each file
  for (const [filePath, fileViolations] of Object.entries(violationsByFile)) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      for (const violation of fileViolations) {
        const fixResult = applyFix(content, violation);
        if (fixResult.success) {
          content = fixResult.content;
          modified = true;
          fixedCount++;
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, content);
        console.log(chalk.green(`   ✅ Fixed ${path.basename(filePath)}`));
      }
      
    } catch (error) {
      console.log(chalk.red(`   ❌ Failed to fix ${path.basename(filePath)}: ${error.message}`));
    }
  }
  
  console.log(chalk.green(`\n✅ Fixed ${fixedCount} violations`));
}

/**
 * Apply specific fix to content
 */
function applyFix(content, violation) {
  try {
    let newContent = content;
    
    // Apply fixes based on violation type
    if (violation.message.includes('#dc2626')) {
      newContent = newContent.replace(/#dc2626/g, '${({ theme }) => theme.colors?.primary || "#dc2626"}');
    }
    
    if (violation.message.includes('#4b5563')) {
      newContent = newContent.replace(/#4b5563/g, '${({ theme }) => theme.colors?.border || "#4b5563"}');
    }
    
    if (violation.message.includes('any type')) {
      // This requires more sophisticated fixing, skip for now
      return { success: false, content };
    }
    
    return { success: newContent !== content, content: newContent };
    
  } catch (error) {
    return { success: false, content };
  }
}

/**
 * Generate validation report
 */
async function generateValidationReport(violations) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalViolations: violations.length,
      errorCount: violations.filter(v => v.severity === 'error').length,
      warningCount: violations.filter(v => v.severity === 'warning').length,
      fixableCount: violations.filter(v => v.fixable).length
    },
    violations: violations.map(v => ({
      pattern: v.pattern,
      file: path.relative(process.cwd(), v.file),
      message: v.message,
      severity: v.severity,
      line: v.line,
      fixable: v.fixable
    }))
  };
  
  const reportPath = path.resolve('dev-tools/reports/pattern-validation.json');
  fs.mkdirSync(path.dirname(reportPath), { recursive: true });
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(chalk.blue(`📄 Report saved: ${reportPath}`));
}
