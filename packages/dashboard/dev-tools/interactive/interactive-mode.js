/**
 * Interactive Development Mode
 * 
 * Guided development experience with intelligent suggestions and automated workflows.
 * Helps developers apply our patterns efficiently and learn best practices.
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import { analyzeComponent } from '../analyzers/component-analyzer.js';
import { generateComponent } from '../generators/component-generator.js';
import { validatePatterns } from '../validators/pattern-validator.js';
import { refactorSuggestions } from '../analyzers/refactor-analyzer.js';

/**
 * Start interactive development mode
 */
export async function startInteractiveMode() {
  console.log(chalk.blue('🎮 Welcome to F1 Interactive Development Mode!'));
  console.log(chalk.gray('Your guided assistant for ADHD Trading Dashboard development\n'));
  
  while (true) {
    try {
      const action = await promptMainMenu();
      
      if (action === 'exit') {
        console.log(chalk.green('👋 Happy coding! See you next time.'));
        break;
      }
      
      await executeAction(action);
      
    } catch (error) {
      console.log(chalk.red(`❌ Error: ${error.message}`));
      
      const retry = await inquirer.prompt([{
        type: 'confirm',
        name: 'continue',
        message: 'Would you like to continue?',
        default: true
      }]);
      
      if (!retry.continue) break;
    }
  }
}

/**
 * Show main menu and get user choice
 */
async function promptMainMenu() {
  const choices = [
    {
      name: '🔍 Analyze Component - Get detailed analysis and refactoring suggestions',
      value: 'analyze'
    },
    {
      name: '🚀 Generate Component - Create new component with F1 patterns',
      value: 'generate'
    },
    {
      name: '✅ Validate Patterns - Check pattern compliance across codebase',
      value: 'validate'
    },
    {
      name: '🔧 Refactor Assistant - Get intelligent refactoring suggestions',
      value: 'refactor'
    },
    {
      name: '📚 Learn Patterns - Interactive pattern tutorial',
      value: 'learn'
    },
    {
      name: '🏥 Health Check - Comprehensive codebase health analysis',
      value: 'health'
    },
    {
      name: '⚙️  Settings - Configure development tools',
      value: 'settings'
    },
    new inquirer.Separator(),
    {
      name: '🚪 Exit',
      value: 'exit'
    }
  ];

  const answer = await inquirer.prompt([{
    type: 'list',
    name: 'action',
    message: 'What would you like to do?',
    choices,
    pageSize: 10
  }]);

  return answer.action;
}

/**
 * Execute selected action
 */
async function executeAction(action) {
  switch (action) {
    case 'analyze':
      await interactiveAnalyze();
      break;
    case 'generate':
      await interactiveGenerate();
      break;
    case 'validate':
      await interactiveValidate();
      break;
    case 'refactor':
      await interactiveRefactor();
      break;
    case 'learn':
      await interactiveLearn();
      break;
    case 'health':
      await interactiveHealthCheck();
      break;
    case 'settings':
      await interactiveSettings();
      break;
  }
}

/**
 * Interactive component analysis
 */
async function interactiveAnalyze() {
  console.log(chalk.blue('\n🔍 Component Analysis'));
  console.log(chalk.blue('====================='));
  
  const { componentPath } = await inquirer.prompt([{
    type: 'input',
    name: 'componentPath',
    message: 'Enter component path (relative to src/):',
    validate: input => input.trim().length > 0 || 'Please enter a valid path'
  }]);
  
  const { detailed } = await inquirer.prompt([{
    type: 'confirm',
    name: 'detailed',
    message: 'Show detailed metrics?',
    default: true
  }]);
  
  const { suggestions } = await inquirer.prompt([{
    type: 'confirm',
    name: 'suggestions',
    message: 'Show refactoring suggestions?',
    default: true
  }]);
  
  console.log(chalk.yellow('\n⏳ Analyzing component...'));
  
  const analysis = await analyzeComponent(`src/${componentPath}`, { detailed, suggestions });
  
  if (analysis && analysis.suggestions.length > 0) {
    const { applyRefactor } = await inquirer.prompt([{
      type: 'confirm',
      name: 'applyRefactor',
      message: 'Would you like to apply suggested refactoring?',
      default: false
    }]);
    
    if (applyRefactor) {
      await guidedRefactoring(analysis);
    }
  }
}

/**
 * Interactive component generation
 */
async function interactiveGenerate() {
  console.log(chalk.blue('\n🚀 Component Generation'));
  console.log(chalk.blue('======================='));
  
  const { type } = await inquirer.prompt([{
    type: 'list',
    name: 'type',
    message: 'What type of component?',
    choices: [
      { name: '📦 Container - Main orchestrator with error boundaries', value: 'container' },
      { name: '📝 Form - Form with validation and F1 styling', value: 'form' },
      { name: '🎯 Header - F1-themed header component', value: 'header' },
      { name: '📊 Table - Sortable table with F1 styling', value: 'table' },
      { name: '🧩 Component - Generic reusable component', value: 'component' }
    ]
  }]);
  
  const { name } = await inquirer.prompt([{
    type: 'input',
    name: 'name',
    message: 'Component name (PascalCase):',
    validate: input => {
      if (!input.trim()) return 'Please enter a component name';
      if (!/^[A-Z][a-zA-Z0-9]*$/.test(input)) return 'Please use PascalCase (e.g., MyComponent)';
      return true;
    }
  }]);
  
  const { location } = await inquirer.prompt([{
    type: 'list',
    name: 'location',
    message: 'Where should it be created?',
    choices: [
      { name: '🎯 Feature - In a specific feature directory', value: 'feature' },
      { name: '🔄 Shared - In shared components', value: 'shared' },
      { name: '📄 Page - As a page component', value: 'page' },
      { name: '🧩 Components - In general components', value: 'components' }
    ]
  }]);
  
  let featureName = '';
  if (location === 'feature') {
    const featureAnswer = await inquirer.prompt([{
      type: 'input',
      name: 'featureName',
      message: 'Feature name:',
      validate: input => input.trim().length > 0 || 'Please enter a feature name'
    }]);
    featureName = featureAnswer.featureName;
  }
  
  const { force } = await inquirer.prompt([{
    type: 'confirm',
    name: 'force',
    message: 'Overwrite if exists?',
    default: false
  }]);
  
  console.log(chalk.yellow('\n⏳ Generating component...'));
  
  const componentType = location === 'feature' ? 'feature' : location;
  const componentName = location === 'feature' ? `${featureName}/${name}` : name;
  
  const result = await generateComponent(componentType, componentName, { 
    pattern: type, 
    force 
  });
  
  if (result) {
    const { openInEditor } = await inquirer.prompt([{
      type: 'confirm',
      name: 'openInEditor',
      message: 'Open generated files in editor?',
      default: true
    }]);
    
    if (openInEditor) {
      console.log(chalk.blue(`📝 Open: ${result.path}`));
    }
  }
}

/**
 * Interactive pattern validation
 */
async function interactiveValidate() {
  console.log(chalk.blue('\n✅ Pattern Validation'));
  console.log(chalk.blue('====================='));
  
  const { scope } = await inquirer.prompt([{
    type: 'list',
    name: 'scope',
    message: 'Validation scope:',
    choices: [
      { name: '🌍 Full Codebase - Validate all patterns', value: 'full' },
      { name: '📁 Specific Directory - Validate specific directory', value: 'directory' },
      { name: '📄 Single File - Validate single file', value: 'file' }
    ]
  }]);
  
  const { autoFix } = await inquirer.prompt([{
    type: 'confirm',
    name: 'autoFix',
    message: 'Auto-fix violations where possible?',
    default: false
  }]);
  
  console.log(chalk.yellow('\n⏳ Validating patterns...'));
  
  const result = await validatePatterns({ fix: autoFix, report: true });
  
  if (result && result.violations > 0) {
    const { viewReport } = await inquirer.prompt([{
      type: 'confirm',
      name: 'viewReport',
      message: 'View detailed validation report?',
      default: true
    }]);
    
    if (viewReport) {
      console.log(chalk.blue('📄 Report saved to: dev-tools/reports/pattern-validation.json'));
    }
  }
}

/**
 * Interactive refactoring assistant
 */
async function interactiveRefactor() {
  console.log(chalk.blue('\n🔧 Refactoring Assistant'));
  console.log(chalk.blue('========================'));
  
  console.log(chalk.yellow('⏳ Analyzing refactoring opportunities...'));
  
  const suggestions = await refactorSuggestions({ priority: 'all' });
  
  if (!suggestions || suggestions.length === 0) {
    console.log(chalk.green('✅ No immediate refactoring opportunities found!'));
    return;
  }
  
  const { selectedSuggestion } = await inquirer.prompt([{
    type: 'list',
    name: 'selectedSuggestion',
    message: 'Select refactoring opportunity:',
    choices: suggestions.map((suggestion, index) => ({
      name: `${suggestion.priority.toUpperCase()}: ${suggestion.component} - ${suggestion.description}`,
      value: index
    }))
  }]);
  
  const suggestion = suggestions[selectedSuggestion];
  
  console.log(chalk.white(`\n📋 Refactoring Plan for ${suggestion.component}:`));
  console.log(chalk.gray(`   ${suggestion.description}`));
  console.log(chalk.white(`   Estimated effort: ${suggestion.estimatedEffort}`));
  console.log(chalk.green(`   Expected benefit: ${suggestion.expectedBenefit}`));
  
  const { proceed } = await inquirer.prompt([{
    type: 'confirm',
    name: 'proceed',
    message: 'Proceed with guided refactoring?',
    default: true
  }]);
  
  if (proceed) {
    await guidedRefactoring(suggestion);
  }
}

/**
 * Interactive pattern learning
 */
async function interactiveLearn() {
  console.log(chalk.blue('\n📚 Pattern Learning'));
  console.log(chalk.blue('==================='));
  
  const patterns = [
    { name: '📦 Container Pattern', value: 'container' },
    { name: '📝 Form Pattern', value: 'form' },
    { name: '🎯 Header Pattern', value: 'header' },
    { name: '📊 Table Pattern', value: 'table' },
    { name: '🎨 F1 Theme System', value: 'theme' }
  ];
  
  const { pattern } = await inquirer.prompt([{
    type: 'list',
    name: 'pattern',
    message: 'Which pattern would you like to learn about?',
    choices: patterns
  }]);
  
  await showPatternTutorial(pattern);
}

/**
 * Interactive health check
 */
async function interactiveHealthCheck() {
  console.log(chalk.blue('\n🏥 Codebase Health Check'));
  console.log(chalk.blue('========================'));
  
  console.log(chalk.yellow('⏳ Running comprehensive health check...'));
  
  // Run all health checks
  const results = await Promise.all([
    validatePatterns({ silent: true }),
    // Add other health checks here
  ]);
  
  console.log(chalk.green('✅ Health check complete!'));
  
  // Display summary
  const totalIssues = results.reduce((sum, result) => sum + (result?.violations || 0), 0);
  
  if (totalIssues === 0) {
    console.log(chalk.green('🎉 Codebase is healthy!'));
  } else {
    console.log(chalk.yellow(`⚠️  Found ${totalIssues} issues`));
    
    const { viewDetails } = await inquirer.prompt([{
      type: 'confirm',
      name: 'viewDetails',
      message: 'View detailed health report?',
      default: true
    }]);
    
    if (viewDetails) {
      // Show detailed results
      results.forEach((result, index) => {
        if (result?.violations > 0) {
          console.log(chalk.yellow(`\n📊 Check ${index + 1}: ${result.violations} violations`));
        }
      });
    }
  }
}

/**
 * Interactive settings
 */
async function interactiveSettings() {
  console.log(chalk.blue('\n⚙️  Development Tools Settings'));
  console.log(chalk.blue('=============================='));
  
  const settings = {
    autoFix: false,
    verboseOutput: true,
    generateTests: true,
    generateStories: true
  };
  
  const { newSettings } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'autoFix',
      message: 'Enable auto-fix by default?',
      default: settings.autoFix
    },
    {
      type: 'confirm',
      name: 'verboseOutput',
      message: 'Enable verbose output?',
      default: settings.verboseOutput
    },
    {
      type: 'confirm',
      name: 'generateTests',
      message: 'Generate tests by default?',
      default: settings.generateTests
    },
    {
      type: 'confirm',
      name: 'generateStories',
      message: 'Generate Storybook stories by default?',
      default: settings.generateStories
    }
  ]);
  
  console.log(chalk.green('✅ Settings updated!'));
}

/**
 * Show pattern tutorial
 */
async function showPatternTutorial(pattern) {
  const tutorials = {
    container: `
📦 Container Pattern
===================

The Container pattern separates orchestration from presentation:

✅ DO:
- Use F1Container wrapper
- Include error boundaries
- Handle loading states
- Manage context providers

❌ DON'T:
- Include complex styling
- Handle business logic directly
- Mix presentation concerns

Example:
\`\`\`tsx
export const MyContainer = () => (
  <F1Container variant="default" maxWidth={800}>
    <Suspense fallback={<LoadingSpinner />}>
      <MyContent />
    </Suspense>
  </F1Container>
);
\`\`\`
    `,
    
    form: `
📝 Form Pattern
===============

The Form pattern provides consistent form handling:

✅ DO:
- Use F1Form and F1FormField
- Implement proper validation
- Handle error states
- Use form hooks

❌ DON'T:
- Create custom form styling
- Skip validation
- Handle form state manually

Example:
\`\`\`tsx
const { field } = useFormField({
  initialValue: '',
  required: true,
  validationRules: [validationRules.required()]
});

<F1FormField
  label="Symbol"
  field={field}
  type="text"
  required
/>
\`\`\`
    `
  };
  
  const tutorial = tutorials[pattern] || 'Tutorial not available yet.';
  console.log(chalk.white(tutorial));
  
  await inquirer.prompt([{
    type: 'input',
    name: 'continue',
    message: 'Press Enter to continue...'
  }]);
}

/**
 * Guided refactoring process
 */
async function guidedRefactoring(analysis) {
  console.log(chalk.blue('\n🔧 Guided Refactoring'));
  console.log(chalk.blue('====================='));
  
  console.log(chalk.yellow('This feature is coming soon!'));
  console.log(chalk.gray('Will provide step-by-step refactoring guidance.'));
}
