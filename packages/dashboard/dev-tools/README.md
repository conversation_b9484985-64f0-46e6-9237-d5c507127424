# 🏎️ F1 Development Tools Suite

Comprehensive development toolkit for the ADHD Trading Dashboard, designed to accelerate development iterations and maintain architectural excellence.

## 🚀 Quick Start

```bash
# Interactive mode (recommended for beginners)
npm run dev:interactive

# Direct commands
npm run f1-dev analyze src/components/MyComponent.tsx
npm run f1-dev generate form TradeForm
npm run f1-dev validate --fix
npm run f1-dev health
```

## 🛠️ Available Tools

### 🔍 Component Analyzer
Analyzes component complexity and provides intelligent refactoring suggestions.

```bash
# Analyze specific component
npm run dev:analyze src/components/TradeForm.tsx

# With detailed metrics and suggestions
f1-dev analyze src/components/TradeForm.tsx --detailed --suggestions
```

**Features:**
- Lines of code analysis
- Cyclomatic complexity calculation
- Pattern detection (Container, Form, Header, Table)
- Responsibility counting
- Health score calculation (0-100)
- Intelligent refactoring suggestions

### 🚀 Component Generator
Generates components following F1 patterns with TypeScript, tests, and Storybook integration.

```bash
# Generate different component types
npm run dev:generate form UserForm
npm run dev:generate container DashboardContainer
npm run dev:generate header PageHeader
npm run dev:generate table DataTable

# With specific options
f1-dev generate form TradeForm --pattern form --force
```

**Generated Files:**
- Main component file (.tsx)
- TypeScript interfaces
- Unit tests (.test.tsx)
- Storybook stories (.stories.tsx)
- Custom hooks (when applicable)
- Index file for exports

### ✅ Pattern Validator
Validates pattern compliance across the codebase with auto-fix capabilities.

```bash
# Validate all patterns
npm run dev:validate

# Auto-fix violations
npm run dev:validate --fix

# Generate detailed report
npm run dev:validate --report
```

**Validation Rules:**
- **Container Pattern**: F1Container usage, error boundaries, loading states
- **Form Pattern**: F1Form components, validation, proper hooks
- **Header Pattern**: F1Header, theme compliance, responsive design
- **F1 Theme**: Proper color usage, spacing, no hardcoded values
- **TypeScript**: Interface exports, proper typing, JSDoc comments

### 🔧 Refactoring Assistant
Provides intelligent refactoring suggestions based on proven patterns.

```bash
# Get refactoring suggestions
npm run dev:refactor

# Target specific component
f1-dev refactor --target TradeForm

# Filter by priority
f1-dev refactor --priority high
```

**Suggestion Types:**
- **Container Pattern**: Extract orchestration logic
- **Form Pattern**: Apply F1 form components
- **Table Pattern**: Implement sortable tables
- **Performance**: Add memoization and virtualization
- **Custom Hooks**: Extract reusable logic

### 🏥 Health Check
Comprehensive codebase health analysis.

```bash
# Full health check
npm run dev:health

# With auto-fix
f1-dev health --fix
```

**Health Metrics:**
- Pattern compliance score
- Component complexity distribution
- Dependency graph analysis
- Performance bottlenecks
- Code quality indicators

### 🎮 Interactive Mode
Guided development experience with step-by-step assistance.

```bash
npm run dev:interactive
```

**Interactive Features:**
- Component analysis wizard
- Guided component generation
- Pattern learning tutorials
- Refactoring assistance
- Health check dashboard

## 📊 Architecture Metrics

Monitor codebase health with objective metrics:

```bash
# Generate architecture metrics
npm run metrics

# Check schema compliance
npm run schema-check
```

**Tracked Metrics:**
- **Health Score**: 0-100 component health rating
- **Complexity**: Cyclomatic complexity analysis
- **Pattern Adoption**: Usage of established patterns
- **Dependency Graph**: Import/export relationships
- **Code Duplication**: Duplicate code detection

## 🎨 F1 Theme Integration

All generated components follow F1 racing design principles:

- **Colors**: `theme.colors.primary` (#dc2626), `theme.colors.border` (#4b5563)
- **Spacing**: `theme.spacing.md`, `theme.spacing.lg`
- **Typography**: Racing-inspired fonts with proper hierarchy
- **Components**: F1Container, F1Header, F1Form, F1Table

## 📚 Pattern Library

### Container Pattern
```tsx
<F1Container variant="default" maxWidth={800}>
  <Suspense fallback={<LoadingSpinner />}>
    <ComponentContent />
  </Suspense>
</F1Container>
```

### Form Pattern
```tsx
const { field } = useFormField({
  initialValue: '',
  required: true,
  validationRules: [validationRules.required()]
});

<F1FormField
  label="Symbol"
  field={field}
  type="text"
  required
/>
```

### Header Pattern
```tsx
<F1Header
  title="Trading Dashboard"
  subtitle="Performance Analytics"
  variant="dashboard"
  isLive={true}
  actions={<RefreshButton />}
/>
```

## 🔧 Configuration

Create `.f1devrc.json` for custom configuration:

```json
{
  "autoFix": true,
  "generateTests": true,
  "generateStories": true,
  "themeValidation": true,
  "patterns": {
    "container": { "maxLines": 150 },
    "form": { "maxLines": 200 },
    "header": { "maxLines": 100 }
  }
}
```

## 📈 Success Metrics

Track your progress with built-in metrics:

- **Component Health**: Average health score across codebase
- **Pattern Adoption**: Percentage of components using patterns
- **Code Quality**: Reduction in complexity and duplication
- **Development Speed**: Time to create new components

## 🚀 Next Iteration Targets

Based on current analysis, prioritize these refactoring opportunities:

1. **TradesTable.tsx** (329 lines, Health: 30/100) - Apply SortableTable pattern
2. **Settings.tsx** (271 lines) - Add F1Header and F1Form patterns
3. **TradeFormBasicFields.tsx** (337 lines) - Extract F1FormField components

## 🤝 Contributing

When adding new tools:

1. Follow the established pattern structure
2. Add comprehensive JSDoc documentation
3. Include interactive mode integration
4. Provide clear success/error messaging
5. Add to the main CLI interface

## 📖 Documentation

- **Architecture Guide**: `/docs/architecture.md`
- **Pattern Guide**: `/docs/patterns.md`
- **Component Library**: `/docs/components.md`
- **Migration Guide**: `/docs/migration.md`

---

**🏁 Happy coding with F1 Development Tools!**

*Built for the ADHD Trading Dashboard with ❤️ and ⚡*
