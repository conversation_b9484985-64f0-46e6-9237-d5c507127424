{"name": "@adhd-trading-dashboard/dev-tools", "version": "1.0.0", "description": "Development tools suite for ADHD Trading Dashboard", "type": "module", "main": "index.js", "bin": {"f1-dev": "./index.js"}, "scripts": {"start": "node index.js", "interactive": "node index.js interactive", "analyze": "node index.js analyze", "generate": "node index.js generate", "validate": "node index.js validate", "health": "node index.js health"}, "dependencies": {"@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.0", "chalk": "^5.3.0", "commander": "^11.1.0", "figlet": "^1.7.0", "glob": "^10.3.0", "inquirer": "^9.2.0"}, "devDependencies": {"@types/babel__traverse": "^7.20.0", "@types/figlet": "^1.5.0", "@types/inquirer": "^9.0.0"}, "keywords": ["development-tools", "react", "typescript", "f1-theme", "component-library", "refactoring", "code-analysis"], "author": "ADHD Trading Dashboard Team", "license": "MIT"}