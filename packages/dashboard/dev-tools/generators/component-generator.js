/**
 * Component Generator
 * 
 * Intelligent component generator that creates components following our proven patterns.
 * Generates F1-themed components with proper TypeScript, testing, and Storybook integration.
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const TEMPLATES = {
  container: {
    files: ['Container.tsx', 'Container.test.tsx', 'Container.stories.tsx'],
    dependencies: ['F1Container', 'Suspense', 'ErrorBoundary']
  },
  form: {
    files: ['Form.tsx', 'FormFields.tsx', 'useForm.ts', 'Form.test.tsx', 'Form.stories.tsx'],
    dependencies: ['F1Form', 'F1FormField', 'useFormField', 'validationRules']
  },
  header: {
    files: ['Header.tsx', 'Header.test.tsx', 'Header.stories.tsx'],
    dependencies: ['F1Header', 'F1HeaderVariant']
  },
  table: {
    files: ['Table.tsx', 'TableRow.tsx', 'useTable.ts', 'Table.test.tsx', 'Table.stories.tsx'],
    dependencies: ['F1Table', 'SortableTable', 'useTableSort']
  },
  component: {
    files: ['Component.tsx', 'Component.test.tsx', 'Component.stories.tsx'],
    dependencies: ['React', 'styled-components']
  }
};

/**
 * Generate component with specified pattern
 */
export async function generateComponent(type, name, options = {}) {
  try {
    const pattern = options.pattern || detectPatternFromType(type);
    const template = TEMPLATES[pattern];
    
    if (!template) {
      console.log(chalk.red(`❌ Unknown pattern: ${pattern}`));
      console.log(chalk.gray('Available patterns: container, form, header, table, component'));
      return;
    }

    const componentPath = getComponentPath(type, name);
    
    // Check if component exists
    if (fs.existsSync(componentPath) && !options.force) {
      console.log(chalk.yellow(`⚠️  Component already exists: ${componentPath}`));
      console.log(chalk.gray('Use --force to overwrite'));
      return;
    }

    // Create directory
    fs.mkdirSync(componentPath, { recursive: true });

    // Generate files
    const generatedFiles = [];
    for (const file of template.files) {
      const fileName = file.replace('Component', name).replace('Form', name).replace('Table', name).replace('Header', name).replace('Container', name);
      const filePath = path.join(componentPath, fileName);
      
      const content = await generateFileContent(pattern, name, file, options);
      fs.writeFileSync(filePath, content);
      generatedFiles.push(fileName);
    }

    // Generate index file
    const indexContent = generateIndexFile(name, template.files);
    fs.writeFileSync(path.join(componentPath, 'index.ts'), indexContent);
    generatedFiles.push('index.ts');

    console.log(chalk.green(`✅ Generated ${pattern} component: ${name}`));
    console.log(chalk.gray(`📁 Location: ${componentPath}`));
    console.log(chalk.gray(`📄 Files: ${generatedFiles.join(', ')}`));
    
    // Show next steps
    showNextSteps(name, pattern, componentPath);
    
    return { path: componentPath, files: generatedFiles };
    
  } catch (error) {
    console.log(chalk.red(`❌ Generation failed: ${error.message}`));
    return null;
  }
}

/**
 * Detect pattern from component type
 */
function detectPatternFromType(type) {
  const typePatterns = {
    'container': 'container',
    'form': 'form',
    'header': 'header',
    'table': 'table',
    'modal': 'component',
    'button': 'component',
    'card': 'component'
  };
  
  return typePatterns[type.toLowerCase()] || 'component';
}

/**
 * Get component path based on type and name
 */
function getComponentPath(type, name) {
  const basePath = path.resolve('src');
  
  // Determine path based on type
  if (type === 'page') {
    return path.join(basePath, 'pages', name);
  } else if (type === 'feature') {
    return path.join(basePath, 'features', name, 'components');
  } else if (type === 'shared') {
    return path.join(basePath, 'components', 'shared', name);
  } else {
    return path.join(basePath, 'components', type, name);
  }
}

/**
 * Generate file content based on template
 */
async function generateFileContent(pattern, name, fileName, options) {
  const templatePath = path.join(__dirname, '../templates', pattern, fileName);
  
  // Check if template exists
  if (fs.existsSync(templatePath)) {
    let template = fs.readFileSync(templatePath, 'utf8');
    return processTemplate(template, name, options);
  }
  
  // Generate content dynamically if no template
  return generateDynamicContent(pattern, name, fileName, options);
}

/**
 * Process template with variables
 */
function processTemplate(template, name, options) {
  return template
    .replace(/{{NAME}}/g, name)
    .replace(/{{LOWER_NAME}}/g, name.toLowerCase())
    .replace(/{{UPPER_NAME}}/g, name.toUpperCase())
    .replace(/{{CAMEL_NAME}}/g, toCamelCase(name))
    .replace(/{{KEBAB_NAME}}/g, toKebabCase(name))
    .replace(/{{DESCRIPTION}}/g, options.description || `${name} component`)
    .replace(/{{AUTHOR}}/g, options.author || 'ADHD Trading Dashboard')
    .replace(/{{DATE}}/g, new Date().toISOString().split('T')[0]);
}

/**
 * Generate dynamic content when no template exists
 */
function generateDynamicContent(pattern, name, fileName, options) {
  if (fileName.endsWith('.tsx')) {
    return generateComponentFile(pattern, name, options);
  } else if (fileName.endsWith('.test.tsx')) {
    return generateTestFile(pattern, name, options);
  } else if (fileName.endsWith('.stories.tsx')) {
    return generateStoryFile(pattern, name, options);
  } else if (fileName.endsWith('.ts')) {
    return generateHookFile(pattern, name, options);
  }
  
  return `// Generated ${fileName} for ${name}\n`;
}

/**
 * Generate main component file
 */
function generateComponentFile(pattern, name, options) {
  const imports = getImportsForPattern(pattern);
  const props = getPropsForPattern(pattern, name);
  const component = getComponentForPattern(pattern, name);
  
  return `/**
 * ${name} Component
 * 
 * Generated using F1 ${pattern} pattern.
 * Follows ADHD Trading Dashboard architectural standards.
 */

import React from 'react';
import styled from 'styled-components';
${imports}

export interface ${name}Props {
  /** Custom className */
  className?: string;
${props}
}

const Container = styled.div\`
  display: flex;
  flex-direction: column;
  gap: \${({ theme }) => theme.spacing?.md || '12px'};
  background: \${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: \${({ theme }) => theme.borderRadius?.lg || '8px'};
  padding: \${({ theme }) => theme.spacing?.lg || '16px'};
  border: 1px solid \${({ theme }) => theme.colors?.border || '#4b5563'};
\`;

/**
 * ${name} Component
 * 
 * ${getComponentDescription(pattern)}
 */
export const ${name}: React.FC<${name}Props> = ({
  className,
  ...props
}) => {
  return (
    <Container className={className}>
${component}
    </Container>
  );
};

export default ${name};`;
}

/**
 * Generate test file
 */
function generateTestFile(pattern, name, options) {
  return `/**
 * ${name} Component Tests
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import { theme } from '@adhd-trading-dashboard/shared';
import { ${name} } from './${name}';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('${name}', () => {
  it('renders without crashing', () => {
    renderWithTheme(<${name} />);
  });

  it('applies custom className', () => {
    const { container } = renderWithTheme(<${name} className="custom-class" />);
    expect(container.firstChild).toHaveClass('custom-class');
  });

  // Add more tests based on component functionality
});`;
}

/**
 * Generate Storybook story file
 */
function generateStoryFile(pattern, name, options) {
  return `/**
 * ${name} Stories
 */

import type { Meta, StoryObj } from '@storybook/react';
import { ${name} } from './${name}';

const meta: Meta<typeof ${name}> = {
  title: 'Components/${name}',
  component: ${name},
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '${getComponentDescription(pattern)}'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Custom CSS class'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {}
};

export const WithCustomClass: Story = {
  args: {
    className: 'custom-styling'
  }
};

// Add more stories based on component variants
`;
}

/**
 * Generate hook file
 */
function generateHookFile(pattern, name, options) {
  const hookName = `use${name}`;
  
  return `/**
 * ${hookName} Hook
 * 
 * Custom hook for ${name} component logic.
 */

import { useState, useCallback } from 'react';

export interface ${hookName}Return {
  // Define return type based on pattern
}

/**
 * ${hookName} Hook
 * 
 * Manages state and logic for ${name} component.
 */
export const ${hookName} = (): ${hookName}Return => {
  // Implement hook logic based on pattern
  
  return {
    // Return hook values
  };
};

export default ${hookName};`;
}

/**
 * Generate index file
 */
function generateIndexFile(name, files) {
  const exports = files
    .filter(file => file.endsWith('.tsx') || file.endsWith('.ts'))
    .filter(file => !file.includes('.test.') && !file.includes('.stories.'))
    .map(file => {
      const baseName = file.replace(/\.(tsx|ts)$/, '');
      const componentName = baseName.replace('Component', name).replace('Form', name).replace('Table', name).replace('Header', name).replace('Container', name);
      return `export { ${componentName} } from './${componentName}';`;
    })
    .join('\n');

  return `/**
 * ${name} Component Exports
 */

${exports}
`;
}

/**
 * Get imports for pattern
 */
function getImportsForPattern(pattern) {
  const patternImports = {
    container: "import { F1Container } from '@adhd-trading-dashboard/shared';",
    form: "import { F1Form, F1FormField, useFormField } from '@adhd-trading-dashboard/shared';",
    header: "import { F1Header } from '@adhd-trading-dashboard/shared';",
    table: "import { F1Table, SortableTable } from '@adhd-trading-dashboard/shared';",
    component: ""
  };
  
  return patternImports[pattern] || "";
}

/**
 * Get props for pattern
 */
function getPropsForPattern(pattern, name) {
  const patternProps = {
    container: "  /** Loading state */\n  isLoading?: boolean;\n  /** Error message */\n  error?: string | null;",
    form: "  /** Form submission handler */\n  onSubmit?: (data: any) => void;\n  /** Initial form values */\n  initialValues?: any;",
    header: "  /** Header title */\n  title?: string;\n  /** Header actions */\n  actions?: React.ReactNode;",
    table: "  /** Table data */\n  data?: any[];\n  /** Column definitions */\n  columns?: any[];",
    component: "  /** Component children */\n  children?: React.ReactNode;"
  };
  
  return patternProps[pattern] || "";
}

/**
 * Get component content for pattern
 */
function getComponentForPattern(pattern, name) {
  const patternContent = {
    container: `      <F1Container variant="default" maxWidth={800}>
        {/* Container content */}
      </F1Container>`,
    form: `      <F1Form title="${name} Form" onSubmit={props.onSubmit}>
        {/* Form fields */}
      </F1Form>`,
    header: `      <F1Header 
        title={props.title || "${name}"}
        variant="default"
        actions={props.actions}
      />`,
    table: `      <F1Table 
        data={props.data || []}
        columns={props.columns || []}
      />`,
    component: `      {props.children || <div>${name} Component</div>}`
  };
  
  return patternContent[pattern] || `      <div>${name} Component</div>`;
}

/**
 * Get component description for pattern
 */
function getComponentDescription(pattern) {
  const descriptions = {
    container: "Container component with error boundaries and loading states.",
    form: "Form component with validation and F1 theme integration.",
    header: "Header component following F1 racing design principles.",
    table: "Table component with sorting and F1 theme styling.",
    component: "Reusable component following F1 design system."
  };
  
  return descriptions[pattern] || "Component following F1 design principles.";
}

/**
 * Show next steps after generation
 */
function showNextSteps(name, pattern, componentPath) {
  console.log(chalk.blue('\n🚀 NEXT STEPS'));
  console.log(chalk.blue('============='));
  console.log(chalk.white('1. Review generated files and customize as needed'));
  console.log(chalk.white('2. Run tests: npm test -- --testNamePattern=' + name));
  console.log(chalk.white('3. View in Storybook: npm run storybook'));
  console.log(chalk.white('4. Import in your application:'));
  console.log(chalk.gray(`   import { ${name} } from '${componentPath.replace(process.cwd(), '.')}';`));
}

/**
 * Utility functions
 */
function toCamelCase(str) {
  return str.charAt(0).toLowerCase() + str.slice(1);
}

function toKebabCase(str) {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '');
}
