/**
 * Component Analyzer
 * 
 * Advanced component analysis tool that provides intelligent refactoring suggestions
 * based on our proven patterns and architectural principles.
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';

const PATTERNS = {
  container: {
    indicators: ['Provider', 'Context', 'withLoading', 'Suspense'],
    maxLines: 150,
    maxComplexity: 15,
    shouldHave: ['error boundaries', 'loading states', 'context usage']
  },
  form: {
    indicators: ['onSubmit', 'useState', 'validation', 'FormField'],
    maxLines: 200,
    maxComplexity: 20,
    shouldHave: ['validation', 'error handling', 'form hooks']
  },
  header: {
    indicators: ['Header', 'title', 'navigation', 'actions'],
    maxLines: 100,
    maxComplexity: 10,
    shouldHave: ['F1 theme', 'responsive design', 'accessibility']
  },
  table: {
    indicators: ['Table', 'rows', 'columns', 'sort'],
    maxLines: 250,
    maxComplexity: 25,
    shouldHave: ['sorting', 'pagination', 'virtualization']
  }
};

/**
 * Analyze component and provide refactoring suggestions
 */
export async function analyzeComponent(componentPath, options = {}) {
  try {
    const fullPath = path.resolve(componentPath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(chalk.red(`❌ Component not found: ${componentPath}`));
      return;
    }

    const content = fs.readFileSync(fullPath, 'utf8');
    const analysis = await performAnalysis(fullPath, content);
    
    displayAnalysis(analysis, options);
    
    if (options.suggestions) {
      displayRefactoringSuggestions(analysis);
    }
    
    return analysis;
    
  } catch (error) {
    console.log(chalk.red(`❌ Analysis failed: ${error.message}`));
    return null;
  }
}

/**
 * Perform comprehensive component analysis
 */
async function performAnalysis(filePath, content) {
  const fileName = path.basename(filePath);
  const lines = content.split('\n');
  const nonEmptyLines = lines.filter(line => line.trim().length > 0);
  
  // Parse AST for detailed analysis
  let ast;
  try {
    ast = parse(content, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript', 'decorators-legacy']
    });
  } catch (error) {
    console.warn(chalk.yellow(`⚠️  Could not parse AST: ${error.message}`));
    ast = null;
  }

  const analysis = {
    file: {
      path: filePath,
      name: fileName,
      size: content.length,
      lines: nonEmptyLines.length
    },
    metrics: calculateMetrics(content, ast),
    patterns: detectPatterns(content, fileName),
    issues: findIssues(content, nonEmptyLines.length),
    suggestions: [],
    score: 0
  };

  // Calculate overall score
  analysis.score = calculateScore(analysis);
  
  // Generate suggestions
  analysis.suggestions = generateSuggestions(analysis);

  return analysis;
}

/**
 * Calculate component metrics
 */
function calculateMetrics(content, ast) {
  const metrics = {
    imports: (content.match(/^import\s+/gm) || []).length,
    exports: (content.match(/^export\s+/gm) || []).length,
    hooks: (content.match(/use[A-Z]\w*/g) || []).length,
    components: (content.match(/const\s+[A-Z]\w*.*=.*React\.FC/g) || []).length,
    styledComponents: (content.match(/const\s+[A-Z]\w*.*=.*styled\./g) || []).length,
    complexity: calculateCyclomaticComplexity(content),
    responsibilities: countResponsibilities(content)
  };

  // AST-based metrics if available
  if (ast) {
    const astMetrics = analyzeAST(ast);
    Object.assign(metrics, astMetrics);
  }

  return metrics;
}

/**
 * Calculate cyclomatic complexity
 */
function calculateCyclomaticComplexity(content) {
  const complexityPatterns = [
    /\bif\b/g, /\belse\b/g, /\bswitch\b/g, /\bcase\b/g,
    /\bfor\b/g, /\bwhile\b/g, /\bdo\b/g, /\bcatch\b/g,
    /&&/g, /\|\|/g, /\?/g
  ];
  
  return complexityPatterns.reduce((total, pattern) => {
    const matches = content.match(pattern);
    return total + (matches ? matches.length : 0);
  }, 1); // Base complexity of 1
}

/**
 * Count component responsibilities
 */
function countResponsibilities(content) {
  const responsibilities = [
    { name: 'State Management', pattern: /useState|useReducer|useContext/g },
    { name: 'Side Effects', pattern: /useEffect|useLayoutEffect/g },
    { name: 'API Calls', pattern: /fetch|axios|api\.|query/g },
    { name: 'Form Handling', pattern: /onSubmit|onChange|validation/g },
    { name: 'Event Handling', pattern: /onClick|onKeyDown|onFocus|onBlur/g },
    { name: 'Styling', pattern: /styled\.|css`|className/g },
    { name: 'Routing', pattern: /useRouter|useNavigate|Link/g },
    { name: 'Error Handling', pattern: /try\s*{|catch|Error|throw/g }
  ];

  return responsibilities.filter(resp => resp.pattern.test(content)).length;
}

/**
 * Detect component patterns
 */
function detectPatterns(content, fileName) {
  const detected = [];
  
  Object.entries(PATTERNS).forEach(([patternName, config]) => {
    const score = config.indicators.reduce((total, indicator) => {
      const regex = new RegExp(indicator, 'gi');
      const matches = content.match(regex);
      return total + (matches ? matches.length : 0);
    }, 0);
    
    if (score > 0) {
      detected.push({
        name: patternName,
        confidence: Math.min(score / config.indicators.length, 1),
        score
      });
    }
  });

  // Sort by confidence
  return detected.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Find issues in component
 */
function findIssues(content, lineCount) {
  const issues = [];

  // Size issues
  if (lineCount > 300) {
    issues.push({
      type: 'critical',
      category: 'size',
      message: `Component is too large (${lineCount} lines). Consider splitting.`,
      suggestion: 'Apply container pattern or extract sub-components'
    });
  } else if (lineCount > 200) {
    issues.push({
      type: 'warning',
      category: 'size',
      message: `Component is getting large (${lineCount} lines).`,
      suggestion: 'Monitor for refactoring opportunities'
    });
  }

  // Pattern issues
  if (content.includes('useState') && content.includes('useEffect') && lineCount > 150) {
    issues.push({
      type: 'warning',
      category: 'pattern',
      message: 'Complex state management detected in large component.',
      suggestion: 'Extract custom hooks or apply container pattern'
    });
  }

  // F1 theme issues
  if (!content.includes('theme.colors') && content.includes('styled.')) {
    issues.push({
      type: 'info',
      category: 'theme',
      message: 'Not using F1 theme system.',
      suggestion: 'Migrate to F1 theme colors and spacing'
    });
  }

  // Performance issues
  if (content.includes('map(') && !content.includes('React.memo') && lineCount > 100) {
    issues.push({
      type: 'warning',
      category: 'performance',
      message: 'Potential performance issue with list rendering.',
      suggestion: 'Consider React.memo or virtualization'
    });
  }

  return issues;
}

/**
 * Calculate overall component score
 */
function calculateScore(analysis) {
  let score = 100;
  
  // Deduct for size
  if (analysis.file.lines > 300) score -= 30;
  else if (analysis.file.lines > 200) score -= 15;
  else if (analysis.file.lines > 100) score -= 5;
  
  // Deduct for complexity
  if (analysis.metrics.complexity > 30) score -= 25;
  else if (analysis.metrics.complexity > 20) score -= 15;
  else if (analysis.metrics.complexity > 10) score -= 5;
  
  // Deduct for responsibilities
  if (analysis.metrics.responsibilities > 6) score -= 20;
  else if (analysis.metrics.responsibilities > 4) score -= 10;
  
  // Deduct for issues
  analysis.issues.forEach(issue => {
    switch (issue.type) {
      case 'critical': score -= 15; break;
      case 'warning': score -= 10; break;
      case 'info': score -= 5; break;
    }
  });
  
  return Math.max(0, score);
}

/**
 * Generate refactoring suggestions
 */
function generateSuggestions(analysis) {
  const suggestions = [];
  
  // Pattern-based suggestions
  const topPattern = analysis.patterns[0];
  if (topPattern && topPattern.confidence > 0.7) {
    const pattern = PATTERNS[topPattern.name];
    if (analysis.file.lines > pattern.maxLines) {
      suggestions.push({
        type: 'refactor',
        priority: 'high',
        title: `Apply ${topPattern.name} pattern`,
        description: `Component shows ${topPattern.name} characteristics but exceeds recommended size.`,
        action: `Extract into ${topPattern.name} container with focused sub-components`,
        estimatedEffort: '2-3 days',
        expectedBenefit: '70-80% complexity reduction'
      });
    }
  }

  // F1 component library suggestions
  if (analysis.file.name.includes('Form') && !analysis.file.path.includes('F1Form')) {
    suggestions.push({
      type: 'migrate',
      priority: 'medium',
      title: 'Migrate to F1Form components',
      description: 'Use F1 component library for consistent styling and behavior.',
      action: 'Replace custom form components with F1Form, F1FormField, etc.',
      estimatedEffort: '1-2 days',
      expectedBenefit: 'Consistent theme, reduced code, better UX'
    });
  }

  return suggestions;
}

/**
 * Display analysis results
 */
function displayAnalysis(analysis, options) {
  console.log(chalk.blue('\n📊 COMPONENT ANALYSIS REPORT'));
  console.log(chalk.blue('================================'));
  
  // File info
  console.log(chalk.white(`📄 File: ${analysis.file.name}`));
  console.log(chalk.gray(`   Path: ${analysis.file.path}`));
  console.log(chalk.gray(`   Size: ${analysis.file.lines} lines`));
  
  // Score
  const scoreColor = analysis.score >= 80 ? 'green' : analysis.score >= 60 ? 'yellow' : 'red';
  console.log(chalk[scoreColor](`🎯 Health Score: ${analysis.score}/100`));
  
  // Metrics
  if (options.detailed) {
    console.log(chalk.white('\n📈 Metrics:'));
    console.log(`   Imports: ${analysis.metrics.imports}`);
    console.log(`   Hooks: ${analysis.metrics.hooks}`);
    console.log(`   Complexity: ${analysis.metrics.complexity}`);
    console.log(`   Responsibilities: ${analysis.metrics.responsibilities}`);
  }
  
  // Patterns
  if (analysis.patterns.length > 0) {
    console.log(chalk.white('\n🎨 Detected Patterns:'));
    analysis.patterns.forEach(pattern => {
      const confidence = Math.round(pattern.confidence * 100);
      console.log(`   ${pattern.name}: ${confidence}% confidence`);
    });
  }
  
  // Issues
  if (analysis.issues.length > 0) {
    console.log(chalk.white('\n⚠️  Issues:'));
    analysis.issues.forEach(issue => {
      const color = issue.type === 'critical' ? 'red' : issue.type === 'warning' ? 'yellow' : 'blue';
      console.log(chalk[color](`   ${issue.type.toUpperCase()}: ${issue.message}`));
    });
  }
}

/**
 * Display refactoring suggestions
 */
function displayRefactoringSuggestions(analysis) {
  if (analysis.suggestions.length === 0) {
    console.log(chalk.green('\n✅ No refactoring suggestions - component looks good!'));
    return;
  }
  
  console.log(chalk.white('\n🔧 REFACTORING SUGGESTIONS'));
  console.log(chalk.white('============================'));
  
  analysis.suggestions.forEach((suggestion, index) => {
    const priorityColor = suggestion.priority === 'high' ? 'red' : 
                         suggestion.priority === 'medium' ? 'yellow' : 'blue';
    
    console.log(chalk[priorityColor](`\n${index + 1}. ${suggestion.title} (${suggestion.priority.toUpperCase()})`));
    console.log(chalk.gray(`   ${suggestion.description}`));
    console.log(chalk.white(`   Action: ${suggestion.action}`));
    console.log(chalk.gray(`   Effort: ${suggestion.estimatedEffort}`));
    console.log(chalk.green(`   Benefit: ${suggestion.expectedBenefit}`));
  });
}

/**
 * Analyze AST for advanced metrics
 */
function analyzeAST(ast) {
  const metrics = {
    functions: 0,
    classes: 0,
    variables: 0,
    jsxElements: 0
  };

  traverse.default(ast, {
    FunctionDeclaration: () => metrics.functions++,
    ArrowFunctionExpression: () => metrics.functions++,
    ClassDeclaration: () => metrics.classes++,
    VariableDeclarator: () => metrics.variables++,
    JSXElement: () => metrics.jsxElements++
  });

  return metrics;
}
