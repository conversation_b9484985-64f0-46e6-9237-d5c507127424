/**
 * Refactor Analyzer
 * 
 * Intelligent refactoring suggestions based on our proven patterns and metrics.
 * Prioritizes refactoring opportunities by impact and effort.
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import { glob } from 'glob';
import { analyzeComponent } from './component-analyzer.js';

const REFACTOR_PATTERNS = {
  container: {
    name: 'Container Pattern',
    indicators: ['Provider', 'Context', 'useEffect', 'useState'],
    threshold: { lines: 150, complexity: 15 },
    effort: 'medium',
    impact: 'high',
    description: 'Extract container logic into focused components'
  },
  
  form: {
    name: 'Form Pattern',
    indicators: ['onSubmit', 'onChange', 'validation', 'form'],
    threshold: { lines: 200, complexity: 20 },
    effort: 'medium',
    impact: 'high',
    description: 'Apply F1 form patterns with validation hooks'
  },
  
  table: {
    name: 'Table Pattern',
    indicators: ['Table', 'rows', 'columns', 'sort', 'map'],
    threshold: { lines: 250, complexity: 25 },
    effort: 'high',
    impact: 'high',
    description: 'Extract sortable table with virtualization'
  },
  
  header: {
    name: 'Header Pat<PERSON>',
    indicators: ['Header', 'title', 'navigation'],
    threshold: { lines: 100, complexity: 10 },
    effort: 'low',
    impact: 'medium',
    description: 'Apply F1 header pattern for consistency'
  },
  
  hooks: {
    name: 'Custom Hooks',
    indicators: ['useState', 'useEffect', 'useCallback'],
    threshold: { lines: 100, complexity: 15 },
    effort: 'low',
    impact: 'medium',
    description: 'Extract reusable custom hooks'
  },
  
  performance: {
    name: 'Performance Optimization',
    indicators: ['map', 'filter', 'large arrays', 'heavy rendering'],
    threshold: { lines: 150, complexity: 20 },
    effort: 'medium',
    impact: 'high',
    description: 'Add memoization and virtualization'
  }
};

/**
 * Analyze refactoring opportunities across codebase
 */
export async function refactorSuggestions(options = {}) {
  try {
    const srcPath = path.resolve('src');
    const suggestions = [];
    
    console.log(chalk.blue('🔍 Analyzing refactoring opportunities...'));
    
    // Get all component files
    const files = await glob('**/*.{ts,tsx}', { 
      cwd: srcPath,
      ignore: ['**/*.test.*', '**/*.stories.*', '**/node_modules/**']
    });
    
    // Analyze each file
    for (const file of files) {
      const filePath = path.join(srcPath, file);
      const analysis = await analyzeComponent(filePath, { silent: true });
      
      if (analysis) {
        const fileSuggestions = generateRefactoringSuggestions(analysis);
        suggestions.push(...fileSuggestions);
      }
    }
    
    // Sort by priority and impact
    const prioritizedSuggestions = prioritizeSuggestions(suggestions);
    
    // Filter by target or priority if specified
    let filteredSuggestions = prioritizedSuggestions;
    
    if (options.target) {
      filteredSuggestions = filteredSuggestions.filter(s => 
        s.component.toLowerCase().includes(options.target.toLowerCase())
      );
    }
    
    if (options.priority) {
      filteredSuggestions = filteredSuggestions.filter(s => 
        s.priority === options.priority
      );
    }
    
    // Display results
    displayRefactoringSuggestions(filteredSuggestions.slice(0, 10)); // Top 10
    
    return filteredSuggestions;
    
  } catch (error) {
    console.log(chalk.red(`❌ Analysis failed: ${error.message}`));
    return [];
  }
}

/**
 * Generate refactoring suggestions for a component
 */
function generateRefactoringSuggestions(analysis) {
  const suggestions = [];
  const fileName = path.basename(analysis.file.path);
  const componentName = fileName.replace(/\.(tsx|ts)$/, '');
  
  // Check each refactoring pattern
  Object.entries(REFACTOR_PATTERNS).forEach(([patternKey, pattern]) => {
    const score = calculatePatternScore(analysis, pattern);
    
    if (score.shouldRefactor) {
      suggestions.push({
        component: componentName,
        filePath: analysis.file.path,
        pattern: patternKey,
        patternName: pattern.name,
        description: pattern.description,
        priority: calculatePriority(analysis, pattern, score),
        effort: pattern.effort,
        impact: pattern.impact,
        estimatedEffort: getEstimatedEffort(analysis, pattern),
        expectedBenefit: getExpectedBenefit(analysis, pattern),
        currentMetrics: {
          lines: analysis.file.lines,
          complexity: analysis.metrics.complexity,
          responsibilities: analysis.metrics.responsibilities,
          score: analysis.score
        },
        projectedMetrics: getProjectedMetrics(analysis, pattern),
        actionItems: generateActionItems(analysis, pattern)
      });
    }
  });
  
  return suggestions;
}

/**
 * Calculate pattern score to determine if refactoring is needed
 */
function calculatePatternScore(analysis, pattern) {
  let score = 0;
  let reasons = [];
  
  // Check size threshold
  if (analysis.file.lines > pattern.threshold.lines) {
    score += 30;
    reasons.push(`Exceeds size threshold (${analysis.file.lines} > ${pattern.threshold.lines})`);
  }
  
  // Check complexity threshold
  if (analysis.metrics.complexity > pattern.threshold.complexity) {
    score += 25;
    reasons.push(`High complexity (${analysis.metrics.complexity} > ${pattern.threshold.complexity})`);
  }
  
  // Check pattern indicators
  const content = fs.readFileSync(analysis.file.path, 'utf8');
  const indicatorScore = pattern.indicators.reduce((total, indicator) => {
    const regex = new RegExp(indicator, 'gi');
    const matches = content.match(regex);
    return total + (matches ? matches.length : 0);
  }, 0);
  
  if (indicatorScore > 3) {
    score += 20;
    reasons.push(`Strong pattern indicators (${indicatorScore} matches)`);
  }
  
  // Check responsibilities
  if (analysis.metrics.responsibilities > 4) {
    score += 15;
    reasons.push(`Too many responsibilities (${analysis.metrics.responsibilities})`);
  }
  
  // Check overall health score
  if (analysis.score < 60) {
    score += 10;
    reasons.push(`Low health score (${analysis.score}/100)`);
  }
  
  return {
    score,
    shouldRefactor: score >= 50,
    reasons
  };
}

/**
 * Calculate refactoring priority
 */
function calculatePriority(analysis, pattern, score) {
  // High priority: Critical health + high impact
  if (analysis.score < 40 && pattern.impact === 'high') {
    return 'high';
  }
  
  // High priority: Very large components
  if (analysis.file.lines > 400) {
    return 'high';
  }
  
  // Medium priority: Good candidates with medium-high impact
  if (score.score >= 70 && (pattern.impact === 'high' || pattern.impact === 'medium')) {
    return 'medium';
  }
  
  // Low priority: Everything else
  return 'low';
}

/**
 * Get estimated effort for refactoring
 */
function getEstimatedEffort(analysis, pattern) {
  const baseEffort = {
    'low': 1,
    'medium': 3,
    'high': 5
  }[pattern.effort];
  
  // Adjust based on component size
  const sizeMultiplier = Math.min(analysis.file.lines / 200, 2);
  const adjustedDays = Math.ceil(baseEffort * sizeMultiplier);
  
  if (adjustedDays === 1) return '1 day';
  if (adjustedDays <= 3) return `${adjustedDays} days`;
  return `${adjustedDays} days (1 week)`;
}

/**
 * Get expected benefit from refactoring
 */
function getExpectedBenefit(analysis, pattern) {
  const benefits = [];
  
  // Size reduction
  const expectedReduction = pattern.name === 'Container Pattern' ? 80 : 
                           pattern.name === 'Form Pattern' ? 70 :
                           pattern.name === 'Table Pattern' ? 75 : 60;
  benefits.push(`${expectedReduction}% complexity reduction`);
  
  // Performance improvement
  if (pattern.name.includes('Performance') || analysis.file.lines > 300) {
    benefits.push('Significant performance improvement');
  }
  
  // Maintainability
  if (analysis.metrics.responsibilities > 4) {
    benefits.push('Better separation of concerns');
  }
  
  // Reusability
  if (pattern.name.includes('Pattern')) {
    benefits.push('Increased reusability');
  }
  
  return benefits.join(', ');
}

/**
 * Get projected metrics after refactoring
 */
function getProjectedMetrics(analysis, pattern) {
  const reductionFactor = pattern.name === 'Container Pattern' ? 0.2 : 
                         pattern.name === 'Form Pattern' ? 0.3 :
                         pattern.name === 'Table Pattern' ? 0.25 : 0.4;
  
  return {
    lines: Math.ceil(analysis.file.lines * reductionFactor),
    complexity: Math.ceil(analysis.metrics.complexity * 0.5),
    responsibilities: Math.min(analysis.metrics.responsibilities, 3),
    score: Math.min(analysis.score + 30, 100)
  };
}

/**
 * Generate specific action items for refactoring
 */
function generateActionItems(analysis, pattern) {
  const actions = [];
  
  switch (pattern.name) {
    case 'Container Pattern':
      actions.push('Extract container component with F1Container');
      actions.push('Move business logic to custom hooks');
      actions.push('Add error boundaries and loading states');
      actions.push('Split into focused sub-components');
      break;
      
    case 'Form Pattern':
      actions.push('Replace with F1Form components');
      actions.push('Extract form fields using F1FormField');
      actions.push('Implement validation with useFormField');
      actions.push('Add form state management hook');
      break;
      
    case 'Table Pattern':
      actions.push('Replace with F1Table or SortableTable');
      actions.push('Extract table row components');
      actions.push('Add sorting and pagination');
      actions.push('Consider virtualization for large datasets');
      break;
      
    case 'Header Pattern':
      actions.push('Replace with F1Header component');
      actions.push('Apply F1 theme colors and spacing');
      actions.push('Add responsive design');
      actions.push('Extract header actions');
      break;
      
    case 'Custom Hooks':
      actions.push('Extract useState logic to custom hook');
      actions.push('Move useEffect logic to custom hook');
      actions.push('Create reusable hook interface');
      actions.push('Add proper TypeScript typing');
      break;
      
    case 'Performance Optimization':
      actions.push('Add React.memo for expensive components');
      actions.push('Implement useMemo for calculations');
      actions.push('Add useCallback for event handlers');
      actions.push('Consider virtualization for lists');
      break;
  }
  
  return actions;
}

/**
 * Prioritize suggestions by impact and effort
 */
function prioritizeSuggestions(suggestions) {
  const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
  const impactOrder = { 'high': 3, 'medium': 2, 'low': 1 };
  const effortOrder = { 'low': 3, 'medium': 2, 'high': 1 }; // Lower effort = higher priority
  
  return suggestions.sort((a, b) => {
    const aScore = (priorityOrder[a.priority] * 3) + 
                   (impactOrder[a.impact] * 2) + 
                   (effortOrder[a.effort] * 1);
    const bScore = (priorityOrder[b.priority] * 3) + 
                   (impactOrder[b.impact] * 2) + 
                   (effortOrder[b.effort] * 1);
    
    return bScore - aScore;
  });
}

/**
 * Display refactoring suggestions
 */
function displayRefactoringSuggestions(suggestions) {
  if (suggestions.length === 0) {
    console.log(chalk.green('\n✅ No immediate refactoring opportunities found!'));
    console.log(chalk.gray('Your codebase is following good patterns.'));
    return;
  }
  
  console.log(chalk.blue('\n🔧 REFACTORING OPPORTUNITIES'));
  console.log(chalk.blue('============================'));
  
  suggestions.forEach((suggestion, index) => {
    const priorityColor = suggestion.priority === 'high' ? 'red' : 
                         suggestion.priority === 'medium' ? 'yellow' : 'blue';
    
    console.log(chalk[priorityColor](`\n${index + 1}. ${suggestion.component} - ${suggestion.patternName}`));
    console.log(chalk.gray(`   ${suggestion.description}`));
    console.log(chalk.white(`   Priority: ${suggestion.priority.toUpperCase()} | Effort: ${suggestion.estimatedEffort} | Impact: ${suggestion.impact}`));
    console.log(chalk.green(`   Benefit: ${suggestion.expectedBenefit}`));
    
    // Show current vs projected metrics
    const current = suggestion.currentMetrics;
    const projected = suggestion.projectedMetrics;
    console.log(chalk.gray(`   Current: ${current.lines} lines, ${current.complexity} complexity, ${current.score}/100 health`));
    console.log(chalk.gray(`   Projected: ${projected.lines} lines, ${projected.complexity} complexity, ${projected.score}/100 health`));
    
    // Show top action items
    if (suggestion.actionItems.length > 0) {
      console.log(chalk.white('   Action Items:'));
      suggestion.actionItems.slice(0, 2).forEach(action => {
        console.log(chalk.gray(`     • ${action}`));
      });
    }
  });
  
  // Summary
  const highPriority = suggestions.filter(s => s.priority === 'high').length;
  const mediumPriority = suggestions.filter(s => s.priority === 'medium').length;
  const lowPriority = suggestions.filter(s => s.priority === 'low').length;
  
  console.log(chalk.white('\n📊 Summary:'));
  console.log(chalk.red(`   High Priority: ${highPriority}`));
  console.log(chalk.yellow(`   Medium Priority: ${mediumPriority}`));
  console.log(chalk.blue(`   Low Priority: ${lowPriority}`));
  
  if (highPriority > 0) {
    console.log(chalk.gray('\n💡 Focus on high priority items for maximum impact'));
  }
}
