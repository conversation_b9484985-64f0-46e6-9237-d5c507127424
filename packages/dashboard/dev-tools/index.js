#!/usr/bin/env node

/**
 * ADHD Trading Dashboard Development Tools Suite
 * 
 * Comprehensive toolkit for accelerating development iterations.
 * Built specifically for our refactoring patterns and F1 component library.
 * 
 * FEATURES:
 * - Component analysis and refactoring suggestions
 * - Pattern compliance checking
 * - Automated code generation
 * - Performance monitoring
 * - Dependency graph analysis
 * - F1 theme validation
 * - Storybook integration
 */

import { program } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import { analyzeComponent } from './analyzers/component-analyzer.js';
import { generateComponent } from './generators/component-generator.js';
import { validatePatterns } from './validators/pattern-validator.js';
import { checkPerformance } from './monitors/performance-monitor.js';
import { buildDependencyGraph } from './analyzers/dependency-analyzer.js';
import { validateF1Theme } from './validators/f1-theme-validator.js';
import { generateStorybook } from './generators/storybook-generator.js';
import { refactorSuggestions } from './analyzers/refactor-analyzer.js';

// Display banner
console.log(chalk.red(figlet.textSync('F1 DEV TOOLS', { horizontalLayout: 'full' })));
console.log(chalk.gray('🏎️  ADHD Trading Dashboard Development Suite\n'));

// Configure CLI
program
  .name('f1-dev')
  .description('Development tools for ADHD Trading Dashboard')
  .version('1.0.0');

// Component Analysis
program
  .command('analyze <component>')
  .description('Analyze component complexity and suggest refactoring')
  .option('-d, --detailed', 'Show detailed analysis')
  .option('-s, --suggestions', 'Show refactoring suggestions')
  .action(async (component, options) => {
    console.log(chalk.blue('🔍 Analyzing component...'));
    await analyzeComponent(component, options);
  });

// Component Generation
program
  .command('generate <type> <name>')
  .description('Generate component using F1 patterns')
  .option('-p, --pattern <pattern>', 'Use specific pattern (container|form|header|table)')
  .option('-f, --force', 'Overwrite existing files')
  .action(async (type, name, options) => {
    console.log(chalk.green('🚀 Generating component...'));
    await generateComponent(type, name, options);
  });

// Pattern Validation
program
  .command('validate')
  .description('Validate pattern compliance across codebase')
  .option('-f, --fix', 'Auto-fix pattern violations')
  .option('-r, --report', 'Generate detailed report')
  .action(async (options) => {
    console.log(chalk.yellow('✅ Validating patterns...'));
    await validatePatterns(options);
  });

// Performance Monitoring
program
  .command('perf')
  .description('Monitor component performance and bundle size')
  .option('-w, --watch', 'Watch for changes')
  .option('-b, --bundle', 'Analyze bundle size')
  .action(async (options) => {
    console.log(chalk.magenta('📊 Monitoring performance...'));
    await checkPerformance(options);
  });

// Dependency Analysis
program
  .command('deps')
  .description('Analyze dependency graph and detect issues')
  .option('-g, --graph', 'Generate visual dependency graph')
  .option('-c, --circular', 'Check for circular dependencies')
  .action(async (options) => {
    console.log(chalk.cyan('🔗 Analyzing dependencies...'));
    await buildDependencyGraph(options);
  });

// F1 Theme Validation
program
  .command('theme')
  .description('Validate F1 theme consistency')
  .option('-f, --fix', 'Auto-fix theme violations')
  .option('-c, --colors', 'Check color usage')
  .action(async (options) => {
    console.log(chalk.red('🎨 Validating F1 theme...'));
    await validateF1Theme(options);
  });

// Storybook Generation
program
  .command('storybook <component>')
  .description('Generate Storybook stories for component')
  .option('-a, --all', 'Generate stories for all components')
  .option('-u, --update', 'Update existing stories')
  .action(async (component, options) => {
    console.log(chalk.blue('📚 Generating Storybook stories...'));
    await generateStorybook(component, options);
  });

// Refactoring Suggestions
program
  .command('refactor')
  .description('Get intelligent refactoring suggestions')
  .option('-t, --target <component>', 'Target specific component')
  .option('-p, --priority <level>', 'Filter by priority (high|medium|low)')
  .action(async (options) => {
    console.log(chalk.green('🔧 Analyzing refactoring opportunities...'));
    await refactorSuggestions(options);
  });

// Interactive Mode
program
  .command('interactive')
  .alias('i')
  .description('Launch interactive development mode')
  .action(async () => {
    console.log(chalk.rainbow('🎮 Launching interactive mode...'));
    const { startInteractiveMode } = await import('./interactive/interactive-mode.js');
    await startInteractiveMode();
  });

// Health Check
program
  .command('health')
  .description('Run comprehensive health check')
  .option('-f, --fix', 'Auto-fix issues where possible')
  .action(async (options) => {
    console.log(chalk.green('🏥 Running health check...'));
    
    // Run all checks
    await Promise.all([
      validatePatterns({ silent: true }),
      checkPerformance({ silent: true }),
      buildDependencyGraph({ silent: true }),
      validateF1Theme({ silent: true })
    ]);
    
    console.log(chalk.green('✅ Health check complete!'));
  });

// Development Server
program
  .command('dev')
  .description('Start development server with enhanced tools')
  .option('-p, --port <port>', 'Port number', '3000')
  .option('-w, --watch', 'Enable file watching')
  .action(async (options) => {
    console.log(chalk.blue('🚀 Starting enhanced development server...'));
    const { startDevServer } = await import('./server/dev-server.js');
    await startDevServer(options);
  });

// Migration Tools
program
  .command('migrate <from> <to>')
  .description('Migrate components to new patterns')
  .option('-d, --dry-run', 'Show what would be changed')
  .option('-b, --backup', 'Create backup before migration')
  .action(async (from, to, options) => {
    console.log(chalk.yellow('🔄 Running migration...'));
    const { runMigration } = await import('./migrations/pattern-migrator.js');
    await runMigration(from, to, options);
  });

// Code Quality
program
  .command('quality')
  .description('Run comprehensive code quality checks')
  .option('-f, --fix', 'Auto-fix issues')
  .option('-r, --report', 'Generate quality report')
  .action(async (options) => {
    console.log(chalk.blue('🎯 Checking code quality...'));
    const { runQualityCheck } = await import('./quality/quality-checker.js');
    await runQualityCheck(options);
  });

// Export utilities for programmatic use
export {
  analyzeComponent,
  generateComponent,
  validatePatterns,
  checkPerformance,
  buildDependencyGraph,
  validateF1Theme,
  generateStorybook,
  refactorSuggestions
};

// Parse CLI arguments
program.parse();

// Show help if no command provided
if (!process.argv.slice(2).length) {
  program.outputHelp();
  console.log(chalk.gray('\n💡 Try: f1-dev interactive for guided experience'));
  console.log(chalk.gray('📖 Docs: https://github.com/your-repo/dev-tools\n'));
}
