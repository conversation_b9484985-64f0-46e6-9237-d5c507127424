{"program": {"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/typescript/lib/lib.es2022.d.ts", "../../node_modules/typescript/lib/lib.esnext.d.ts", "../../node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.array.d.ts", "../../node_modules/typescript/lib/lib.es2022.error.d.ts", "../../node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.object.d.ts", "../../node_modules/typescript/lib/lib.es2022.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2022.string.d.ts", "../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/@types/react/global.d.ts", "../../node_modules/csstype/index.d.ts", "../../node_modules/@types/prop-types/index.d.ts", "../../node_modules/@types/react/index.d.ts", "../../node_modules/@types/react/jsx-runtime.d.ts", "../../node_modules/@remix-run/router/dist/history.d.ts", "../../node_modules/@remix-run/router/dist/utils.d.ts", "../../node_modules/@remix-run/router/dist/router.d.ts", "../../node_modules/@remix-run/router/dist/index.d.ts", "../../node_modules/react-router/dist/lib/context.d.ts", "../../node_modules/react-router/dist/lib/components.d.ts", "../../node_modules/react-router/dist/lib/hooks.d.ts", "../../node_modules/react-router/dist/index.d.ts", "../../node_modules/react-router-dom/dist/dom.d.ts", "../../node_modules/react-router-dom/dist/index.d.ts", "../shared/dist/types/trading.d.ts", "../shared/dist/types/index.d.ts", "../shared/dist/constants/setupelements.d.ts", "../shared/dist/constants/index.d.ts", "../shared/dist/services/tradestorage.d.ts", "../shared/dist/api/index.d.ts", "../shared/dist/components/atoms/badge.d.ts", "../shared/dist/components/atoms/button.d.ts", "../shared/dist/components/atoms/input.d.ts", "../shared/dist/components/atoms/loadingplaceholder.d.ts", "../shared/dist/components/atoms/select.d.ts", "../shared/dist/components/atoms/statusindicator.d.ts", "../shared/dist/components/atoms/tag.d.ts", "../shared/dist/components/atoms/index.d.ts", "../shared/dist/components/molecules/card.d.ts", "../shared/dist/components/molecules/emptystate.d.ts", "../shared/dist/components/molecules/errorboundary.d.ts", "../shared/dist/components/molecules/unifiederrorboundary.d.ts", "../shared/dist/components/molecules/formfield.d.ts", "../shared/dist/components/molecules/modal.d.ts", "../shared/dist/components/molecules/table.d.ts", "../shared/dist/components/molecules/tradetablecolumns.d.ts", "../shared/dist/components/molecules/tradetable.d.ts", "../shared/dist/components/molecules/tradetablerow.d.ts", "../shared/dist/components/molecules/tradetablefilters.d.ts", "../shared/dist/components/molecules/index.d.ts", "../shared/dist/components/organisms/datacard.d.ts", "../shared/dist/components/organisms/index.d.ts", "../shared/dist/components/templates/dashboardtemplate.d.ts", "../shared/dist/components/templates/index.d.ts", "../shared/dist/components/index.d.ts", "../shared/dist/hooks/useasyncdata.d.ts", "../shared/dist/hooks/usedebounce.d.ts", "../shared/dist/hooks/useerrorhandler.d.ts", "../shared/dist/hooks/uselocalstorage.d.ts", "../shared/dist/hooks/usepagination.d.ts", "../shared/dist/hooks/index.d.ts", "../shared/dist/theme/types.d.ts", "../shared/dist/theme/tokens.d.ts", "../shared/dist/theme/f1theme.d.ts", "../shared/dist/theme/lighttheme.d.ts", "../shared/dist/theme/darktheme.d.ts", "../shared/dist/theme/themeprovider.d.ts", "../shared/dist/theme/index.d.ts", "../shared/dist/state/createstorecontext.d.ts", "../shared/dist/state/createselector.d.ts", "../shared/dist/services/persiststate.d.ts", "../shared/dist/state/index.d.ts", "../shared/dist/utils/index.d.ts", "../shared/dist/monitoring/index.d.ts", "../shared/dist/services/tradestorageinterface.d.ts", "../shared/dist/services/index.d.ts", "../shared/dist/index.d.ts", "../../node_modules/@types/hoist-non-react-statics/index.d.ts", "../../node_modules/@types/styled-components/index.d.ts", "./src/layouts/sidebar.tsx", "./src/layouts/header.tsx", "./src/layouts/mainlayout.tsx", "./src/components/molecules/loadingscreen.tsx", "./src/services/transformers/setuptransformer.ts", "./src/features/trading-dashboard/types/index.ts", "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "./src/features/trading-dashboard/components/metricspanel.tsx", "../../node_modules/recharts/types/container/surface.d.ts", "../../node_modules/recharts/types/container/layer.d.ts", "../../node_modules/@types/d3-time/index.d.ts", "../../node_modules/@types/d3-scale/index.d.ts", "../../node_modules/victory-vendor/d3-scale.d.ts", "../../node_modules/recharts/types/util/types.d.ts", "../../node_modules/recharts/types/component/defaultlegendcontent.d.ts", "../../node_modules/recharts/types/util/payload/getuniqpayload.d.ts", "../../node_modules/recharts/types/component/legend.d.ts", "../../node_modules/recharts/types/component/defaulttooltipcontent.d.ts", "../../node_modules/recharts/types/component/tooltip.d.ts", "../../node_modules/recharts/types/component/responsivecontainer.d.ts", "../../node_modules/recharts/types/component/cell.d.ts", "../../node_modules/recharts/types/component/text.d.ts", "../../node_modules/recharts/types/component/label.d.ts", "../../node_modules/recharts/types/component/labellist.d.ts", "../../node_modules/recharts/types/component/customized.d.ts", "../../node_modules/recharts/types/shape/sector.d.ts", "../../node_modules/@types/d3-path/index.d.ts", "../../node_modules/@types/d3-shape/index.d.ts", "../../node_modules/victory-vendor/d3-shape.d.ts", "../../node_modules/recharts/types/shape/curve.d.ts", "../../node_modules/recharts/types/shape/rectangle.d.ts", "../../node_modules/recharts/types/shape/polygon.d.ts", "../../node_modules/recharts/types/shape/dot.d.ts", "../../node_modules/recharts/types/shape/cross.d.ts", "../../node_modules/recharts/types/shape/symbols.d.ts", "../../node_modules/recharts/types/polar/polargrid.d.ts", "../../node_modules/recharts/types/polar/polarradiusaxis.d.ts", "../../node_modules/recharts/types/polar/polarangleaxis.d.ts", "../../node_modules/recharts/types/polar/pie.d.ts", "../../node_modules/recharts/types/polar/radar.d.ts", "../../node_modules/recharts/types/polar/radialbar.d.ts", "../../node_modules/recharts/types/cartesian/brush.d.ts", "../../node_modules/recharts/types/util/ifoverflowmatches.d.ts", "../../node_modules/recharts/types/cartesian/xaxis.d.ts", "../../node_modules/recharts/types/cartesian/yaxis.d.ts", "../../node_modules/recharts/types/cartesian/referenceline.d.ts", "../../node_modules/recharts/types/cartesian/referencedot.d.ts", "../../node_modules/recharts/types/cartesian/referencearea.d.ts", "../../node_modules/recharts/types/cartesian/cartesianaxis.d.ts", "../../node_modules/recharts/types/cartesian/cartesiangrid.d.ts", "../../node_modules/recharts/types/cartesian/line.d.ts", "../../node_modules/recharts/types/cartesian/area.d.ts", "../../node_modules/recharts/types/cartesian/bar.d.ts", "../../node_modules/recharts/types/cartesian/zaxis.d.ts", "../../node_modules/recharts/types/cartesian/errorbar.d.ts", "../../node_modules/recharts/types/cartesian/scatter.d.ts", "../../node_modules/@types/lodash/common/common.d.ts", "../../node_modules/@types/lodash/common/array.d.ts", "../../node_modules/@types/lodash/common/collection.d.ts", "../../node_modules/@types/lodash/common/date.d.ts", "../../node_modules/@types/lodash/common/function.d.ts", "../../node_modules/@types/lodash/common/lang.d.ts", "../../node_modules/@types/lodash/common/math.d.ts", "../../node_modules/@types/lodash/common/number.d.ts", "../../node_modules/@types/lodash/common/object.d.ts", "../../node_modules/@types/lodash/common/seq.d.ts", "../../node_modules/@types/lodash/common/string.d.ts", "../../node_modules/@types/lodash/common/util.d.ts", "../../node_modules/@types/lodash/index.d.ts", "../../node_modules/recharts/types/util/getlegendprops.d.ts", "../../node_modules/recharts/types/util/chartutils.d.ts", "../../node_modules/recharts/types/chart/accessibilitymanager.d.ts", "../../node_modules/recharts/types/util/cursor/getradialcursorpoints.d.ts", "../../node_modules/recharts/types/chart/generatecategoricalchart.d.ts", "../../node_modules/recharts/types/chart/linechart.d.ts", "../../node_modules/recharts/types/chart/barchart.d.ts", "../../node_modules/recharts/types/chart/piechart.d.ts", "../../node_modules/recharts/types/chart/treemap.d.ts", "../../node_modules/recharts/types/chart/sankey.d.ts", "../../node_modules/recharts/types/chart/radarchart.d.ts", "../../node_modules/recharts/types/chart/scatterchart.d.ts", "../../node_modules/recharts/types/chart/areachart.d.ts", "../../node_modules/recharts/types/chart/radialbarchart.d.ts", "../../node_modules/recharts/types/chart/composedchart.d.ts", "../../node_modules/recharts/types/shape/trapezoid.d.ts", "../../node_modules/recharts/types/numberaxis/funnel.d.ts", "../../node_modules/recharts/types/chart/funnelchart.d.ts", "../../node_modules/recharts/types/util/global.d.ts", "../../node_modules/recharts/types/index.d.ts", "./src/features/trading-dashboard/components/performancechart.tsx", "./src/features/trading-dashboard/components/recenttradestable.tsx", "./src/features/trading-dashboard/components/setupanalysis.tsx", "./src/features/trade-journal/types/index.ts", "./src/features/trade-journal/components/trade-form/tradeformheader.tsx", "./src/features/trade-journal/hooks/usetradevalidation.ts", "./src/features/trade-journal/components/trade-form/tradeformbasicfields.tsx", "./src/features/trade-journal/hooks/usetradeformdata.ts", "./src/features/trade-journal/hooks/usetradecalculations.ts", "./src/features/trade-journal/constants/patternquality.ts", "./src/features/trade-journal/hooks/usetradesubmission.ts", "./src/features/trade-journal/hooks/usetradeform.ts", "./src/features/trade-journal/hooks/index.ts", "./src/features/trade-journal/components/trade-form/tradeformtimingfields.tsx", "./src/features/trade-journal/components/trade-form/tradeformriskfields.tsx", "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "./src/features/trade-journal/components/trade-form/tradeformactions.tsx", "./src/features/trade-journal/components/trade-form/tradeformmessages.tsx", "./src/features/trade-journal/components/trade-form/tradeformloading.tsx", "./src/features/trade-journal/components/trade-form/index.ts", "./src/features/trading-dashboard/tradingdashboard.tsx", "./src/features/daily-guide/types.ts", "./src/features/daily-guide/components/marketsummary.tsx", "./src/features/daily-guide/components/marketindicators.tsx", "./src/features/daily-guide/components/marketnews.tsx", "./src/features/daily-guide/components/marketoverview.tsx", "./src/features/daily-guide/components/tradingplan.tsx", "./src/features/daily-guide/components/keylevels.tsx", "./src/features/daily-guide/components/ui/sectioncard.tsx", "./src/features/daily-guide/components/ui/sentimentbadge.tsx", "./src/features/daily-guide/components/ui/prioritytag.tsx", "./src/features/daily-guide/components/ui/index.ts", "./src/features/daily-guide/api/dailyguideapi.ts", "./src/features/daily-guide/context/dailyguidecontext.tsx", "./src/features/daily-guide/dailyguide.tsx", "./src/features/trade-journal/hooks/usetradejournal.ts", "./src/features/trade-journal/hooks/usetradefilters.ts", "./src/features/trade-journal/components/trade-journal/tradejournalheader.tsx", "./src/features/trade-journal/components/trade-journal/tradejournalfilters.tsx", "./src/features/trade-journal/hooks/usetradelist.ts", "./src/features/trade-journal/components/trade-list/tradelistheader.tsx", "./src/features/trade-journal/components/trade-list/tradelistrow.tsx", "./src/features/trade-journal/components/trade-list/tradelistexpandedrow.tsx", "./src/features/trade-journal/components/trade-list/tradelistempty.tsx", "./src/features/trade-journal/components/trade-list/tradelistloading.tsx", "./src/features/trade-journal/components/trade-list/index.ts", "./src/features/trade-journal/components/tradelist.tsx", "./src/features/trade-journal/components/trade-journal/tradejournalcontent.tsx", "./src/features/trade-journal/components/trade-journal/index.ts", "./src/features/trade-journal/tradejournal.tsx", "./src/features/trade-analysis/types.ts", "./src/features/trade-analysis/services/tradeanalysisapi.ts", "./src/features/trade-analysis/hooks/tradeanalysiscontext.tsx", "./src/features/trade-analysis/components/analysisheader.tsx", "./src/features/trade-analysis/components/analysistabs.tsx", "./src/features/trade-analysis/components/filterpanel.tsx", "./src/features/trade-analysis/components/performancesummary.tsx", "./src/features/trade-analysis/components/tradestable.tsx", "./src/features/trade-analysis/components/categoryperformancechart.tsx", "./src/features/trade-analysis/components/timeperformancechart.tsx", "./src/features/trade-analysis/components/tradedetail.tsx", "./src/features/trade-analysis/components/tabcontentrenderer.tsx", "./src/features/trade-analysis/components/tradeanalysiscontainer.tsx", "./src/features/trade-analysis/tradeanalysis.tsx", "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "./src/features/trade-journal/constants/dolanalysis.ts", "./src/features/trade-journal/components/trade-dol-analysis/doltypeselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/dolstrengthselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/dolreactionselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/dolcontextselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/doldetailedanalysis.tsx", "./src/features/trade-journal/components/trade-dol-analysis/doleffectivenessrating.tsx", "./src/features/trade-journal/components/trade-dol-analysis/dolanalysiscomposed.tsx", "./src/features/trade-journal/components/trade-dol-analysis/index.ts", "./src/features/trade-journal/components/trade-dol-analysis/tradedolanalysis.tsx", "./src/features/trade-journal/tradeform.tsx", "./src/features/settings/hooks/usesettings.ts", "./src/features/settings/settings.tsx", "./src/components/notfound.tsx", "./src/routes/routes.tsx", "./src/routes/index.ts", "./src/components/apperrorboundary.tsx", "./src/app.tsx", "./src/minimalapp.tsx", "./src/simpleapp.tsx", "./src/testapp.tsx", "./src/devtools-config.js", "../../node_modules/@types/react-dom/client.d.ts", "../../node_modules/web-vitals/dist/modules/types.d.ts", "../../node_modules/web-vitals/dist/modules/getcls.d.ts", "../../node_modules/web-vitals/dist/modules/getfcp.d.ts", "../../node_modules/web-vitals/dist/modules/getfid.d.ts", "../../node_modules/web-vitals/dist/modules/getlcp.d.ts", "../../node_modules/web-vitals/dist/modules/getttfb.d.ts", "../../node_modules/web-vitals/dist/modules/index.d.ts", "./src/reportwebvitals.ts", "./src/index.tsx", "./src/simple-index.tsx", "./src/components/featureerrorboundary.tsx", "../../node_modules/file-system-cache/lib/filesystemcache.d.ts", "../../node_modules/file-system-cache/lib/index.d.ts", "../../node_modules/@babel/types/lib/index.d.ts", "../../node_modules/@types/babel__generator/index.d.ts", "../../node_modules/@babel/parser/typings/babel-parser.d.ts", "../../node_modules/@types/babel__template/index.d.ts", "../../node_modules/@types/babel__traverse/index.d.ts", "../../node_modules/@types/babel__core/index.d.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/ts5.6/globals.typedarray.d.ts", "./node_modules/@types/node/ts5.6/buffer.buffer.d.ts", "../../node_modules/buffer/index.d.ts", "../../node_modules/undici-types/header.d.ts", "../../node_modules/undici-types/readable.d.ts", "../../node_modules/undici-types/file.d.ts", "../../node_modules/undici-types/fetch.d.ts", "../../node_modules/undici-types/formdata.d.ts", "../../node_modules/undici-types/connector.d.ts", "../../node_modules/undici-types/client.d.ts", "../../node_modules/undici-types/errors.d.ts", "../../node_modules/undici-types/dispatcher.d.ts", "../../node_modules/undici-types/global-dispatcher.d.ts", "../../node_modules/undici-types/global-origin.d.ts", "../../node_modules/undici-types/pool-stats.d.ts", "../../node_modules/undici-types/pool.d.ts", "../../node_modules/undici-types/handlers.d.ts", "../../node_modules/undici-types/balanced-pool.d.ts", "../../node_modules/undici-types/agent.d.ts", "../../node_modules/undici-types/mock-interceptor.d.ts", "../../node_modules/undici-types/mock-agent.d.ts", "../../node_modules/undici-types/mock-client.d.ts", "../../node_modules/undici-types/mock-pool.d.ts", "../../node_modules/undici-types/mock-errors.d.ts", "../../node_modules/undici-types/proxy-agent.d.ts", "../../node_modules/undici-types/api.d.ts", "../../node_modules/undici-types/cookies.d.ts", "../../node_modules/undici-types/patch.d.ts", "../../node_modules/undici-types/filereader.d.ts", "../../node_modules/undici-types/diagnostics-channel.d.ts", "../../node_modules/undici-types/websocket.d.ts", "../../node_modules/undici-types/content-type.d.ts", "../../node_modules/undici-types/cache.d.ts", "../../node_modules/undici-types/interceptors.d.ts", "../../node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/ts5.6/index.d.ts", "../../node_modules/@types/mime/index.d.ts", "../../node_modules/@types/send/index.d.ts", "../../node_modules/@types/qs/index.d.ts", "../../node_modules/@types/range-parser/index.d.ts", "../../node_modules/@types/express-serve-static-core/index.d.ts", "../../node_modules/@types/http-errors/index.d.ts", "../../node_modules/@types/serve-static/index.d.ts", "../../node_modules/@types/connect/index.d.ts", "../../node_modules/@types/body-parser/index.d.ts", "../../node_modules/@types/express/node_modules/@types/express-serve-static-core/index.d.ts", "../../node_modules/@types/express/index.d.ts", "../../node_modules/@storybook/channels/dist/main-c55d8855.d.ts", "../../node_modules/@storybook/channels/dist/postmessage/index.d.ts", "../../node_modules/@storybook/channels/dist/websocket/index.d.ts", "../../node_modules/@storybook/channels/dist/index.d.ts", "../../node_modules/@storybook/types/dist/index.d.ts", "./node_modules/@storybook/react/dist/types-0fc72a6d.d.ts", "../../node_modules/type-fest/source/primitive.d.ts", "../../node_modules/type-fest/source/typed-array.d.ts", "../../node_modules/type-fest/source/basic.d.ts", "../../node_modules/type-fest/source/observable-like.d.ts", "../../node_modules/type-fest/source/internal.d.ts", "../../node_modules/type-fest/source/except.d.ts", "../../node_modules/type-fest/source/simplify.d.ts", "../../node_modules/type-fest/source/writable.d.ts", "../../node_modules/type-fest/source/mutable.d.ts", "../../node_modules/type-fest/source/merge.d.ts", "../../node_modules/type-fest/source/merge-exclusive.d.ts", "../../node_modules/type-fest/source/require-at-least-one.d.ts", "../../node_modules/type-fest/source/require-exactly-one.d.ts", "../../node_modules/type-fest/source/require-all-or-none.d.ts", "../../node_modules/type-fest/source/remove-index-signature.d.ts", "../../node_modules/type-fest/source/partial-deep.d.ts", "../../node_modules/type-fest/source/partial-on-undefined-deep.d.ts", "../../node_modules/type-fest/source/readonly-deep.d.ts", "../../node_modules/type-fest/source/literal-union.d.ts", "../../node_modules/type-fest/source/promisable.d.ts", "../../node_modules/type-fest/source/opaque.d.ts", "../../node_modules/type-fest/source/invariant-of.d.ts", "../../node_modules/type-fest/source/set-optional.d.ts", "../../node_modules/type-fest/source/set-required.d.ts", "../../node_modules/type-fest/source/set-non-nullable.d.ts", "../../node_modules/type-fest/source/value-of.d.ts", "../../node_modules/type-fest/source/promise-value.d.ts", "../../node_modules/type-fest/source/async-return-type.d.ts", "../../node_modules/type-fest/source/conditional-keys.d.ts", "../../node_modules/type-fest/source/conditional-except.d.ts", "../../node_modules/type-fest/source/conditional-pick.d.ts", "../../node_modules/type-fest/source/union-to-intersection.d.ts", "../../node_modules/type-fest/source/stringified.d.ts", "../../node_modules/type-fest/source/fixed-length-array.d.ts", "../../node_modules/type-fest/source/multidimensional-array.d.ts", "../../node_modules/type-fest/source/multidimensional-readonly-array.d.ts", "../../node_modules/type-fest/source/iterable-element.d.ts", "../../node_modules/type-fest/source/entry.d.ts", "../../node_modules/type-fest/source/entries.d.ts", "../../node_modules/type-fest/source/set-return-type.d.ts", "../../node_modules/type-fest/source/asyncify.d.ts", "../../node_modules/type-fest/source/numeric.d.ts", "../../node_modules/type-fest/source/jsonify.d.ts", "../../node_modules/type-fest/source/schema.d.ts", "../../node_modules/type-fest/source/literal-to-primitive.d.ts", "../../node_modules/type-fest/source/string-key-of.d.ts", "../../node_modules/type-fest/source/exact.d.ts", "../../node_modules/type-fest/source/readonly-tuple.d.ts", "../../node_modules/type-fest/source/optional-keys-of.d.ts", "../../node_modules/type-fest/source/has-optional-keys.d.ts", "../../node_modules/type-fest/source/required-keys-of.d.ts", "../../node_modules/type-fest/source/has-required-keys.d.ts", "../../node_modules/type-fest/source/spread.d.ts", "../../node_modules/type-fest/source/split.d.ts", "../../node_modules/type-fest/source/camel-case.d.ts", "../../node_modules/type-fest/source/camel-cased-properties.d.ts", "../../node_modules/type-fest/source/camel-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/delimiter-case.d.ts", "../../node_modules/type-fest/source/kebab-case.d.ts", "../../node_modules/type-fest/source/delimiter-cased-properties.d.ts", "../../node_modules/type-fest/source/kebab-cased-properties.d.ts", "../../node_modules/type-fest/source/delimiter-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/kebab-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/pascal-case.d.ts", "../../node_modules/type-fest/source/pascal-cased-properties.d.ts", "../../node_modules/type-fest/source/pascal-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/snake-case.d.ts", "../../node_modules/type-fest/source/snake-cased-properties.d.ts", "../../node_modules/type-fest/source/snake-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/includes.d.ts", "../../node_modules/type-fest/source/screaming-snake-case.d.ts", "../../node_modules/type-fest/source/join.d.ts", "../../node_modules/type-fest/source/trim.d.ts", "../../node_modules/type-fest/source/replace.d.ts", "../../node_modules/type-fest/source/get.d.ts", "../../node_modules/type-fest/source/last-array-element.d.ts", "../../node_modules/type-fest/source/package-json.d.ts", "../../node_modules/type-fest/source/tsconfig-json.d.ts", "../../node_modules/type-fest/index.d.ts", "./node_modules/@storybook/react/dist/index.d.ts", "./src/components/molecules/profitlosscell.tsx", "./src/components/molecules/profitlosscell.stories.tsx", "./src/components/molecules/profitlosscellrefactored.tsx", "./src/features/daily-guide/dailyguidecomposed.tsx", "./src/features/daily-guide/state/dailyguidestate.ts", "./src/features/daily-guide/state/dailyguideselectors.ts", "./src/features/daily-guide/state/index.ts", "./src/features/daily-guide/hooks/usedailyguide.ts", "./src/features/daily-guide/hooks/index.ts", "./src/features/daily-guide/components/dailyguide.tsx", "./src/features/daily-guide/components/index.ts", "./src/features/daily-guide/index.ts", "./src/features/performance-dashboard/components/metricspanel.tsx", "./src/features/performance-dashboard/components/performancechart.tsx", "./src/features/performance-dashboard/components/recenttradespanel.tsx", "./src/features/performance-dashboard/hooks/usedashboarddata.ts", "./src/features/performance-dashboard/dashboard.tsx", "./src/features/performance-dashboard/dashboardcomposed.tsx", "./src/features/performance-dashboard/index.ts", "./src/features/settings/components/settingssection.tsx", "./src/features/settings/components/settingitem.tsx", "./src/features/settings/components/toggleswitch.tsx", "./src/features/settings/index.ts", "./src/features/trade-analysis/tradeanalysiscomposed.tsx", "./src/features/trade-analysis/hooks/tradeanalysisstate.ts", "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "./src/features/trade-analysis/components/tradeanalysistable.tsx", "./src/features/trade-analysis/components/tradeanalysissummary.tsx", "./src/features/trade-analysis/components/tradeanalysischarts.tsx", "./src/features/trade-analysis/components/tradeanalysis.tsx", "./src/features/trade-analysis/index.ts", "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "./src/features/trade-analysis/components/distributionchart.tsx", "./src/features/trade-analysis/components/equitycurve.tsx", "./src/features/trade-analysis/components/metricspanel.tsx", "./src/features/trade-analysis/components/index.ts", "./src/features/trade-analysis/types/index.ts", "./src/features/trade-entry/components/setupbuilder.tsx", "./src/features/trade-journal/index.ts", "./src/features/trade-journal/components/selectdropdown.tsx", "./src/features/trade-journal/components/tabpanel.tsx", "./src/features/trade-journal/components/timepicker.tsx", "./src/features/trade-journal/components/trade-setup-classification/setupclassificationsection.tsx", "./src/features/trade-journal/constants/setupclassification.ts", "./src/features/trade-journal/components/trade-setup-classification/primarysetupselector.tsx", "./src/features/trade-journal/components/trade-setup-classification/secondarysetupselector.tsx", "./src/features/trade-journal/components/trade-setup-classification/liquidityselector.tsx", "./src/features/trade-journal/components/trade-setup-classification/fvgselector.tsx", "./src/features/trade-journal/components/trade-setup-classification/doltargetselector.tsx", "./src/features/trade-journal/components/trade-setup-classification/parentpdarrayselector.tsx", "./src/features/trade-journal/components/trade-setup-classification/setupclassificationcomposed.tsx", "./src/features/trade-journal/components/trade-setup-classification/index.ts", "./src/features/trade-journal/components/trade-pattern-quality/index.ts", "./src/features/trade-journal/components/index.ts", "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "./src/features/trading-dashboard/components/dashboardtabs.tsx", "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "./src/features/trading-dashboard/components/f1header.tsx", "./src/features/trading-dashboard/components/quicktradeform.tsx", "./src/features/trading-dashboard/components/index.ts", "./src/features/trading-dashboard/components/tradingdashboardcontainer.tsx", "./src/features/trading-dashboard/tradingdashboardrefactored.tsx", "./src/features/trading-dashboard/tradingdashboardwithfeatureflag.tsx", "./src/features/trading-dashboard/index.ts", "./src/features/trading-dashboard/utils/datavalidation.ts", "./src/layouts/index.ts", "./src/pages/dailyguide.tsx", "./src/pages/dashboard.tsx", "./src/pages/notfound.tsx", "./src/pages/settings.tsx", "./src/pages/tradeanalysis.tsx", "./src/pages/tradeform.tsx", "./src/pages/tradejournal.tsx", "./src/routes/components/molecules/loadingscreen.tsx", "./src/routes/layouts/mainlayout.tsx", "./src/services/contracts/tradejournalapiimpl.ts", "../../node_modules/@vitest/utils/dist/types.d.ts", "../../node_modules/@vitest/utils/dist/helpers.d.ts", "../../node_modules/pretty-format/build/types.d.ts", "../../node_modules/pretty-format/build/index.d.ts", "../../node_modules/@vitest/utils/dist/index.d.ts", "../../node_modules/@vitest/runner/dist/tasks-c965d7f6.d.ts", "../../node_modules/@vitest/runner/dist/runner-3b8473ea.d.ts", "../../node_modules/@vitest/runner/dist/index.d.ts", "../../node_modules/@types/chai/index.d.ts", "../../node_modules/@vitest/expect/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment-38cdead3.d.ts", "../../node_modules/@vitest/snapshot/dist/index-6461367c.d.ts", "../../node_modules/@vitest/snapshot/dist/index.d.ts", "../../node_modules/esbuild/lib/main.d.ts", "../../node_modules/vitest/node_modules/vite/types/metadata.d.ts", "../../node_modules/vitest/node_modules/vite/types/hmrpayload.d.ts", "../../node_modules/vitest/node_modules/vite/types/customevent.d.ts", "../../node_modules/vitest/node_modules/rollup/dist/rollup.d.ts", "../../node_modules/vitest/node_modules/vite/types/importglob.d.ts", "../../node_modules/source-map-js/source-map.d.ts", "../../node_modules/postcss/lib/previous-map.d.ts", "../../node_modules/postcss/lib/input.d.ts", "../../node_modules/postcss/lib/css-syntax-error.d.ts", "../../node_modules/postcss/lib/declaration.d.ts", "../../node_modules/postcss/lib/root.d.ts", "../../node_modules/postcss/lib/warning.d.ts", "../../node_modules/postcss/lib/lazy-result.d.ts", "../../node_modules/postcss/lib/no-work-result.d.ts", "../../node_modules/postcss/lib/processor.d.ts", "../../node_modules/postcss/lib/result.d.ts", "../../node_modules/postcss/lib/document.d.ts", "../../node_modules/postcss/lib/rule.d.ts", "../../node_modules/postcss/lib/node.d.ts", "../../node_modules/postcss/lib/comment.d.ts", "../../node_modules/postcss/lib/container.d.ts", "../../node_modules/postcss/lib/at-rule.d.ts", "../../node_modules/postcss/lib/list.d.ts", "../../node_modules/postcss/lib/postcss.d.ts", "../../node_modules/vitest/node_modules/vite/dist/node/index.d.ts", "../../node_modules/@vitest/runner/dist/types.d.ts", "../../node_modules/@vitest/runner/types.d.ts", "../../node_modules/@vitest/utils/dist/diff.d.ts", "../../node_modules/@vitest/utils/diff.d.ts", "../../node_modules/@vitest/runner/dist/utils.d.ts", "../../node_modules/@vitest/runner/utils.d.ts", "../../node_modules/tinybench/dist/index.d.cts", "../../node_modules/vite-node/dist/types.d-1e7e3fdf.d.ts", "../../node_modules/vite-node/dist/types-c39b64bb.d.ts", "../../node_modules/vite-node/dist/client.d.ts", "../../node_modules/@vitest/snapshot/dist/manager.d.ts", "../../node_modules/@vitest/snapshot/manager.d.ts", "../../node_modules/vite-node/dist/index.d.ts", "../../node_modules/source-map/source-map.d.ts", "../../node_modules/vite-node/node_modules/vite/dist/node/index.d.ts", "../../node_modules/vite-node/dist/server.d.ts", "../../node_modules/vitest/dist/types-e3c9754d.d.ts", "../../node_modules/tinyspy/dist/index.d.ts", "../../node_modules/@vitest/spy/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment.d.ts", "../../node_modules/@vitest/snapshot/environment.d.ts", "../../node_modules/vitest/dist/config.d.ts", "../../node_modules/vitest/dist/index.d.ts", "../../node_modules/vitest/globals.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/ts5.6/buffer.buffer.d.ts", "../../node_modules/vite/dist/node/index.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/index.d.ts", "../../node_modules/@types/node/worker_threads.d.ts"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "4b421cbfb3a38a27c279dec1e9112c3d1da296f77a1a85ddadf7e7a425d45d18", "1fc5ab7a764205c68fa10d381b08417795fc73111d6dd16b5b1ed36badb743d9", "746d62152361558ea6d6115cf0da4dd10ede041d14882ede3568bce5dc4b4f1f", "d11a03592451da2d1065e09e61f4e2a9bf68f780f4f6623c18b57816a9679d17", "aea179452def8a6152f98f63b191b84e7cbd69b0e248c91e61fb2e52328abe8c", {"version": "3aafcb693fe5b5c3bd277bd4c3a617b53db474fe498fc5df067c5603b1eebde7", "affectsGlobalScope": true}, {"version": "f3d4da15233e593eacb3965cde7960f3fddf5878528d882bcedd5cbaba0193c7", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "81cac4cbc92c0c839c70f8ffb94eb61e2d32dc1c3cf6d95844ca099463cf37ea", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "6e7997ef61de3132e4d4b2250e75343f487903ddf5370e7ce33cf1b9db9a63ed", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "5e5e095c4470c8bab227dbbc61374878ecead104c74ab9960d3adcccfee23205", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "d7f680a43f8cd12a6b6122c07c54ba40952b0c8aa140dcfcf32eb9e6cb028596", "affectsGlobalScope": true}, {"version": "3787b83e297de7c315d55d4a7c546ae28e5f6c0a361b7a1dcec1f1f50a54ef11", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "2768ef564cfc0689a1b76106c421a2909bdff0acbe87da010785adab80efdd5c", "affectsGlobalScope": true}, {"version": "b248e32ca52e8f5571390a4142558ae4f203ae2f94d5bac38a3084d529ef4e58", "affectsGlobalScope": true}, {"version": "6c55633c733c8378db65ac3da7a767c3cf2cf3057f0565a9124a16a3a2019e87", "affectsGlobalScope": true}, {"version": "fb4416144c1bf0323ccbc9afb0ab289c07312214e8820ad17d709498c865a3fe", "affectsGlobalScope": true}, {"version": "5b0ca94ec819d68d33da516306c15297acec88efeb0ae9e2b39f71dbd9685ef7", "affectsGlobalScope": true}, {"version": "34c839eaaa6d78c8674ae2c37af2236dee6831b13db7b4ef4df3ec889a04d4f2", "affectsGlobalScope": true}, {"version": "34478567f8a80171f88f2f30808beb7da15eac0538ae91282dd33dce928d98ed", "affectsGlobalScope": true}, {"version": "ab7d58e6161a550ff92e5aff755dc37fe896245348332cd5f1e1203479fe0ed1", "affectsGlobalScope": true}, {"version": "6bda95ea27a59a276e46043b7065b55bd4b316c25e70e29b572958fa77565d43", "affectsGlobalScope": true}, {"version": "aedb8de1abb2ff1095c153854a6df7deae4a5709c37297f9d6e9948b6806fa66", "affectsGlobalScope": true}, {"version": "a4da0551fd39b90ca7ce5f68fb55d4dc0c1396d589b612e1902f68ee090aaada", "affectsGlobalScope": true}, {"version": "11ffe3c281f375fff9ffdde8bbec7669b4dd671905509079f866f2354a788064", "affectsGlobalScope": true}, {"version": "52d1bb7ab7a3306fd0375c8bff560feed26ed676a5b0457fa8027b563aecb9a4", "affectsGlobalScope": true}, {"version": "bbdf156fea2fabed31a569445835aeedcc33643d404fcbaa54541f06c109df3f", "affectsGlobalScope": true}, "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "65ff5a0aefd7817a03c1ad04fee85c9cdd3ec415cc3c9efec85d8008d4d5e4ee", {"version": "7fb6faf1006c3503d44e4922c8c65772ddc98e92bad7c2ae2d5db123f3e297b3", "affectsGlobalScope": true}, "af7fd2870746deed40e130fc0a3966de74e8f52a97ec114d0fbb35876ab05ca9", "036b2bdb6931917cd8c03e4448d26d14b760035ff0cc636f582eedbac84cdb8c", "31172fc1c2ff8750789aa6ca42dbb19869ba33acea9df7141e6a65154f49d31b", "adb0acb5deb823b8a300cbb9453cee23d5acdebfc3569cdb2947cfba8e465711", "e9f9b0b6a912d089ea1e008db41892a1a7dedfe37ed3603da7aabcd596b62176", "b620e82a2a4b595315172015f8a7ef55710c05e4dd3ca50835597a4d4196f3ae", "c86b1dc09268220a591c811f103cebdedeffe99c5394241cc7a9fa96d01e168b", "b147482273abaf18f285b79d9d8bfad9a20a0c8a4fd46e4c03978299f19ee518", "adad2f34346602d079ba8572f05bfd67619c1b91497adae041bbb2faaee5b99b", "202767e06b68e590b65afba715122116259e4dc500592cf05e320d6383c4dffb", {"version": "c217e646d1decd693b2e119c2a7741145704546e78e784ec9d62f4997c3c4abe", "affectsGlobalScope": true}, "426496bcb0637c3bed425a19536b00c5c6c601b1b3df1a1ddba24d10069cd42b", "cc4da989ef1e6713abaa708900556120c5aea74b1b6388eb6bf9aca4b5a2c8d2", "995cd3288eb00aa6430175e1bc07b0b7ef7aa448ab31b482f6816130e5ed7f67", "c433e9e0016cd1b1b2c37383477c883e92e9b2ee91180309e28c5622ad79388b", "76492e13445241458813cf0217a6d71116e3e67e51b9f65bd7173256f7aebd51", "5b67735c6e97e275b16bd360edf19dbbc36adf5c67e54579e04c8601ed80b630", "0af74406416d97ceb10d5e41f0b3851935b025781d4d0a947693a64ed6da75ec", "5984bd7cd4a33d6a62bb4986df1f82660eb3dc6d9aebb2ef1db56629a31b8150", "fe53a895b4389e3c21cad33f5b35041c7dd9831be91d6a3ef58e57d3d8a920e8", "50e5bfe31015203c9b3c66d3c9dc49080292f992167e3a1d2934d2327dce0153", "73e009fc65ff5b0b5ce3652d9fa77b9e6dd1742b3bd21c36543c3f49edeeda72", "1f42f5b203dc15e4185f5126aa3c548b39c376930ceb971df8c0418d7d57d704", "5fd13ee8a2c8590011f83eca93235081d1cf12ebcc62b7214aa2d149c640c8b6", "39512de6fb7a1590ed6c91a191f26ce05b3b038c18a605b7294407eae1d9bf78", "cc7d98c158d315757c4f9dd8d807eeb652776304dbb89563f2ad98ad655d6790", "b695e1b4d27cbc64833f59279d98a32f0ebbe119a10b9efbb8b6f6d502a13430", "1bcd9514263deb08e6fbfa81be2ea3a19684f85d9e4b44751724614d48abecb0", "d3b96f3f6c7c26e084c89b0d63a5123c387dbcfd12624bab308c98fa6ab3d8db", "ee05275e187b0e2cae4b9c255f256b431bcb0c67e32d3b812ffc1c5ded364160", "9004bbac65d382939c8e6945426868f823e394fc7894373365ae69ada9777292", "8347c8145de633f44f0e538c68e5bd7555c02bc93aa9f2c996497c74d9aaba91", "2d141e03c7d9ca5a315c6a118d397858596f85b79fe1ea13549410f13811c55a", "0edb5aec5b12de813ee7dabae5aea1e3a8cf5a20850e24359530fcd9a571cac3", "545c3d247b1028989aedfe18902d4ece58f0b814c55f3abf97ac8580da15e6c7", "a8fccbb7131b446b4d681a36bab49af11f221023c3149b4a671d70074da74b5b", "e5496c95a093cb01a2bd3fa64b560a530a7d50ad256cccd2c6329430e7b3c2c7", "b8c3c23d46609991b2cdc69d2c7918cb34a64717ef19df478ad3befd730e94fb", "4eb07556933e9f7f2157e63ddbb80e31d12ecd94c045ae879d192c8b5133c0b3", "8d9af4552465bf59be1a6995cd13f64b84583f29ffd70551e82077a6f15a3170", "4329fcacd20a3098d23b6fc43fc4e7f03961a957e60d6a8c3a57b897c5157c99", "d1df2b0830baee0dab88d08c0b31c42ef43bd477362e3cbb99b806794ff1351d", "74a6818bb70b50fb80af8890374ad85cbbaa5a642245e163c8e0db5feba073f2", "a77171126ba6e14f9300d77e53fbb29253e46d642de45320c485d03d51231b3f", "782e30011e6f412a880a4cb59112515cee93ee6c51ad96449808ddcf202a060a", "a5685c0620e615ecd7324f894284b7d64f788b309237412682e5979a2549daef", "f320233942d2b1ecb3422f8354ab5a9e1515c38a7377548835d006cb9bec9440", "de8696b758c999728b29e27acbd0314fdb3823c4f8f44cb7a2e61bb155d81707", "9fa587290b721ee4bc68999f1f8fcce443e10d15cde1bf22b6b7f9828457c79c", "7e43a0104426c7f1b4c4ffbd4a51a8359c61d5ecc2fb763663d557a62b28bb04", "30ae297a7b366cbb4c6b1c67da3ec813b054a722fe97e686338b69cd020049d7", "6b5c9d9afe1365e13362e4011db724dff0f6a6b9c1db107232f03097ad9492d0", "ffd8cfc5bfb344aec7edbbb337c0335bbc716d82e4cb33c02f631f56e0fa0602", "66b1217c50202967544a033facb58b1045b421ea75bd85f7681dcd826cedf4b4", "f178ea1aae9dc7d8f6f78d677f3ae8ade5fef202e16088d25de53d753b47b7b8", "f16c5ba2cd85559a604e8ff66b64b63087f1fcc58399e3443699fa80841dc555", "3fc60fae5116305e4baf3538f0318ff91c60b83be392432b4fe687faa54550c0", "a4e9e5982fb85d5f27006ec2e5f4417b01192e1c1bddea25fbfbcb8250eeed8e", "fe2fee5d7afbf5c1fea01de58d5af6ea739eedac0457c037f4164e1e28eda15a", "dc69b496c0d736fdbee8882f154f0c9ed6bd5667904ddb842d13dc25d3085c37", "7cbdbbc59e9b464c9b8041a0516cb099b31c7158f89cccc6ae0878adaa8ebfb5", "a0f329336b6ae6f1d931efef2a69e1cbbf7feedee3020f90a5909454a53ef2b7", "8eb5d38060e7551c8aa12245f7e2b1e1ee5e37748cdb193f0421b3cc637a1691", "b5092cb228ba06359717f01c4b4e0941e9669b866d8c7d270de80dc0a75987fc", "b2d0630483bf337ef9dac326c3334a245aa4946e9f60f12baf7da5be44beafbb", {"version": "356701ea5df9eea3bf62b0f29857cb950d95eec9b9063f85c17be705926cdd2a", "affectsGlobalScope": true}, {"version": "45337901c43708013ce67aa6859e393aecafe74d4092f570b4d53c9ddb6e1d98", "signature": "8abbb40ebf04e4c54c903252a4cb34276b888fe466e484bcce13c0e3bef798fd"}, {"version": "734b50615c8700431af4ad447eedc9cc784b3ec11dc6c12f6f56a529b5be1397", "signature": "c9d631f071275484a72947688f51c6dfcf165b0a5597af0e6c9faf3d74933326"}, {"version": "54b51820422d37f23d62bbaad774cf181fdc5a6d91116f403c11562fdf4e9077", "signature": "08fd22931fa2ff638debc6cb64638b45fe901ed5b34c42fc8120cc87a7f3c91f"}, {"version": "2d81c7d3ff6e7c00e9ecd1d58f6109ef579b13b8a956f268ad5f55555c7a0553", "signature": "350d59818bbc7c74258419aac86cb52a5a1e3fe5daf2aa5ddc5609ac29077c37"}, {"version": "5c6217e082ecdc3b08f0d18390a3754fa62814679c44da9a9d53b7ef20c85684", "signature": "04f230d0da57c47a7013d44024fee67a5d923227f981b3b8897f23401bb6c5a8"}, {"version": "ca14c50891cdb8ececf00c45bc65f721dbbad436a4f08c4b3232b381950c6b0c", "signature": "2a0798a7b7769448adf3f18aa606b1c852694eaa2dd94b063cb8e31285890725"}, {"version": "587743fb234b6ee990c1d1b485bc1b9187e682eb3bd63309bcc5433fd24b8f02", "signature": "29be795c0c260e9751f976e218cd1dae3b7e3234aaf88faeaeb9f55c742b9f62"}, {"version": "65ad943f540bb151f8dea77775be793e70b01e18b301c1bfdf5044e83740644a", "signature": "7a82dcba28c745c7964d35d8cc6bf6726082042ac0c68bdcb74241873e08c2b5"}, "af1a175d9870af96701ac1a8a295b88a4b78c6e25a0984a0b506530d4e3e7e35", "928790d1176b143f86a72ce1293fbdd697ac47e99c6cfbb77363b07635d2d073", "56208c500dcb5f42be7e18e8cb578f257a1a89b94b3280c506818fed06391805", "0c94c2e497e1b9bcfda66aea239d5d36cd980d12a6d9d59e66f4be1fa3da5d5a", "eb9271b3c585ea9dc7b19b906a921bf93f30f22330408ffec6df6a22057f3296", "2247161a50d13e2caa4d361de76740752113498dfe20d1929e8fb12c8eea0220", "46c4a99e54dffe9077ae0d5b56b2c9f357ed92798b7a3961092508fdd8fd21e8", "2abbe344595c3eb8680e4b9bed9546621847b18c0b47d3f4c6116e208b73bac3", "6507e5ca1af587957b174941d6d9b713bfc1fee0610ff7e742c914b02356f0c3", "dab96b728b3b3714adbbcccefba1a617f1f49f08ca9c7fb2c195257fb947d480", "aa1e7203ee981d21f0714282c081fbb214ba871676cd5f733ea2e1bf54201a59", "e2eba33e0756b5fc6abbe0438e40b11d0b919c7e9ef8e195c3fa2aa884ad70f8", "49a9650201db6de8ebc7361ea0d0984ad602d912676095fcf595769b92e9f90d", "3355f107464481d7cab90f7acd52c8f291fa833ae9891c5daeb8fb61ac7371fa", "135517a282bc52665b257d869818487c1d15e8ea68b7de9be026a2399e5155c6", "f5374459d1faa9d41c6e3cbe528339a3202361d8fb738540473fac610450e032", "7bf226837398fc2deb479034c1801215e3d7a561e9d85d63aaa5c5b429d03b9d", "46e43b1802dccd07fb4181c78dbae6d799eea8061b61dbbedf8764619cb9c25e", "2c378d9368abcd2eba8c29b294d40909845f68557bc0b38117e4f04fc56e5f9c", {"version": "bb220eaac1677e2ad82ac4e7fd3e609a0c7b6f2d6d9c673a35068c97f9fcd5cd", "affectsGlobalScope": true}, "c60b14c297cc569c648ddaea70bc1540903b7f4da416edd46687e88a543515a1", "d2ea2cac8bd2500a515938bfbedaacb30139b552af0fecbbf8b913ee92fd5475", "3cb68cd4afa5446554f27cd5f268d0dc9719a20c2f9108ba418f1b5f429f1878", "50b7c14dcfd6005c11cb1ad447a16f5b02b74912a4fb6c5ab74de114d85462f2", "115445e53b874659c5de88facfef276bd678b6ae1ee21f24c91528fafe61fe96", "cf746ee75c562746ecae64deb216d07cdc35871e39b0b6268eb14fa92b667916", "4a086660560bd550b43bcdb6bd861b3ae064559932b638e57cf2aeab14dc0978", "cbfb038c69b12de0906d93aa0690712a96660d08338255e2bfdcf6feb85487eb", "5e4a6e61ddd5d9501f7c4bc7d0c1a9c36d7e03eab8340de5fd3e93c9a05a73b5", "55fd91897e28954fc9731b6e6aba3119f597634b5ea12ac0e65c51ee6933ae16", "25c3f21fe02fa5eabceee27c5e048f562b4e4995d054355d6883012ef4ae811f", "0a8b260d4da268f07abec25ff1a8e02f9a62bb30091c714691ead14222dbabc6", "0a31c0f3ff84ea0817b4a0eaf967a1f494ef36d7bd048e62d331b5921612db87", "f382f6e720fe9075431bca0190355594cf6987ead66f28708919248bc536a6b7", "a99ccdff3eda5c028c054812f51feb7a70c516089033d64141bc0c2e545a300e", "072f2c74fa93ace28d012ed430b842e3429f283731c9098bc61458c7943d6f1d", "da85c5089034621051c0730bb8b3c0aa6f0ce8399e1661d1b6ec5035e39c488f", "eef160102ae89c1fab6077588eb777291b2f6e08a191e06d0cfbc29bd50dc9fc", "538d170976d1fad3a15139a2d9f8d64f0435ab46ff7fd2d8e87645a39f0a0ae6", "e95638ec2c66709b779b9c5ae8cb1c155aa3dc14177ec265c5b19a9af1da380f", "ec6d116f270b49fd2c421a28d2b77d3c502f45b7fc2637821ccf20ce6904e532", "78afcc56b4bd140e7ee9d89052e6a66c372ed0a7798dd34bad5f22a3764433bd", "f010d3763e209bf051d0d602a22215cd9f90c8af1febf00787c453aee04e530f", "94eb3ab4761d1385c438b609f323918d07f8a5081b146a51add44c847ba1102e", "91eb0ce83e91586fba013a93cfc7c4136fe4246516028cefcdb00c15343b4063", "7b1dab45d6bd9dc6b65466ad1e69e8b05e06bc9b2431ee48be53ba52a9bea600", "f7ac629b2536e7e6fea08fb7fb529226bee59ddc1c122783d40cc0ebccbe5218", "0aaaa19de5128497cbc36ce182eeeb3b601aed5543562cedd75fa70d67d718d7", "7220461ab7f6d600b313ce621346c315c3a0ebc65b5c6f268488c5c55b68d319", "b14c272987c82d49f0f12184c9d8d07a7f71767be99cb76faa125b777c70e962", "fcf79300e5257a23ed3bacaa6861d7c645139c6f7ece134d15e6669447e5e6db", "187119ff4f9553676a884e296089e131e8cc01691c546273b1d0089c3533ce42", "aa2c18a1b5a086bbcaae10a4efba409cc95ba7287d8cf8f2591b53704fea3dea", "b88749bdb18fc1398370e33aa72bc4f88274118f4960e61ce26605f9b33c5ba2", "0aaef8cded245bf5036a7a40b65622dd6c4da71f7a35343112edbe112b348a1e", "00baffbe8a2f2e4875367479489b5d43b5fc1429ecb4a4cc98cfc3009095f52a", "a873c50d3e47c21aa09fbe1e2023d9a44efb07cc0cb8c72f418bf301b0771fd3", "7c14ccd2eaa82619fffc1bfa877eb68a012e9fb723d07ee98db451fadb618906", "49c36529ee09ea9ce19525af5bb84985ea8e782cb7ee8c493d9e36d027a3d019", "df996e25faa505f85aeb294d15ebe61b399cf1d1e49959cdfaf2cc0815c203f9", "4f6a12044ee6f458db11964153830abbc499e73d065c51c329ec97407f4b13dd", "44c1a26f578277f8ccef3215a4bd642a0a4fbbaf187cf9ae3053591c891fdc9c", "815f7cbe81eea0dbf8588a4b816508474afe4fb11798ce62fdbfad9883c48442", "9696e4d7201576a5cbca4ff661919ac4970310d2077d5372d4d8c8946899bde0", "161ac1fb2e087312cb732b4f84a1bf955e630501fde9898433f747b265680ed2", "6c246edf923812e030066371c9880d0900f40c605c495598eb4c6a46f5b5e604", "22a9a62a9a0df6ea92af561dbc26f5080d053921bc1bbbb79efa0de3904a9364", "78b0065ca3731769efd285dd3600326bad750b6e92583220aa3343fa1daf23ff", "1f0719c370f347558eb826b423f6bb467575b26b1e3b7eb6de71d53292f0eafe", "fc7f9c8676a128e870e8f476a97da1651e4793bccef5fb3ab263386daa967549", "0a65df27e0e29b3a6aac41fca2028ca19ca9259a0a38a3a1f48b990b477b071a", "6655716b4c860ac51e746ed8ad294c2a205661a5e006dafcb0b73a20b368e6d8", "99daea5e406b93dcb3f237ca63521a74f6e33a2d6359b2db05c4d95709481317", "dcd915ec1eb7841ffdd71296e01a577bef2a6330da2c2e163b01107da48dbe1b", "167e6b4e019d056223783db9a0c60a2dcdfdbe3b0b0c913832d8166ac55af45d", "b9e654d169b2b06fcc1f7b8f285d7f4c5813abd8b3c939a65e02e3a02df50404", "671c388abd87ec4dd2a6006708f295a57263c680391162f0c04144e127af8ec7", "4379ba1e366a31ccbb53f9bfbe12c5d4047d9011d8f241b6b789074b97aaeee3", "cd8da02aa032154cef7cb1170d5fb234e9fe020bb08984d86c153534fd2d5de0", "7c1f4b5bf6b970bf77e0597fd8f2bdcd2237879c0b8902d85ff4225376207348", "456105330b371dd2eaabd40146a9c60da59f23e24f2dc6eb7d921ca26d014165", {"version": "64b99a241a46e573f21a7f753f35ad3bd8af0f31a1354568d01a4f4aedfb886f", "signature": "3b6c787b451f30597e435a3ca84398762f0c88e594eccca365e8e2e99cb7805a"}, {"version": "cdb26065c5880c9ebcf219fbb7fbe913a889f048adf4bec0d05653c5e2ad248d", "signature": "0081909fd0b10430f822d5d772186180982a26f0266348c63f620835ce3c1101"}, {"version": "9020060e8c5cdb012d1a9f5a039295efee292eda408304b04484f2b49ddaf562", "signature": "69ea7afc755c65ee4d1b162ecd5802d23734d16e60ed9b927e9df65785793326"}, {"version": "f6b56d73cdab96a9bc32e28cc81166ff1fd41ecefee4d3c725de6d777423aa6c", "signature": "8efde14cc84ab9c0dee8d2db8b09a5140c580d464f87e0551be71ee52bd23b48"}, {"version": "0445312f2ba56733ebce3da8da8f220d48d95b21f64a15298d75246455dfd3dc", "signature": "67246f72bfabdb9ec95695e7cd266bdce51829aa50451f43c834931a5c345c13"}, {"version": "0ac139a96b68dc833f772176b2cbe7e7142da9281f1e764c4e861290b31a0fd6", "signature": "b828d82178c5a3901a2b0bc311252437999647f15a893268279cf56d8f01243e"}, {"version": "b0f348a2a7fb86d52ef1c1737024558352fee05d825c56248e40e21601bd94c0", "signature": "81c77b2f0131ca78fa28cca857652a8606803970729d7c902476579c384487e6"}, {"version": "17b0d07cbd8bc837b8cedd8d1c582ca06d778b76c758c60457bae832c1d10466", "signature": "52815a92f08930e43334f2556b27583c71bcaabd21c10c60595d2b1bba0d94d5"}, {"version": "da6e558739ca301babf27766271e60f41d31a589803711509eb0a314a899d919", "signature": "8e72774646b24a5c834b0391db4933e24bce7df6228d6ae9298e342b39d128c6"}, {"version": "0736ff24e8d4af1bc8a53fd38247ee9dade70a1240257ccc502ccba2be03e244", "signature": "cb19e63846f7ac0645d8978ff18d51f695432072d3fa224cd1eb01cd57dd7611"}, {"version": "5f18ff1d2d62f81a4ebf2c53d52e811a5c57b5d695af0c99551faa9219a4ff58", "signature": "02dca07d0de13d9980100b25d929f5a22fdec4c0f1af5aab975ac9d47242022f"}, {"version": "2d78278c1eb495461bd1907b8dce42609d812d09fbbd266c23d69d76d59bf9ea", "signature": "3275623823b525e38da84710daab6ea6b3feabb49a47717f581a72b4c8695b48"}, {"version": "e2b67df8fbfe9204fc05ccd63a4c230eb6d9bc219c721e63e2f11eeb1c701df1", "signature": "da5e742b0e4bc8b4aee90fb644a811c694dfb21769054b0b615a7f02fede046c"}, {"version": "93d830051bdc8541c5edb7142bb086b46215601bbd76370e058c97d5594ca5f3", "signature": "97f0e0bf25f2efbeb0c1a2e1dc7dce39f9ae67bfefd0201279518360c9d775e8"}, {"version": "f755cc0fd789232a8d90861252ccfb44cd55383fd5d4faeed56c220878e52822", "signature": "acdd4bbd9bc6307c4069e07678fdb554a81b126471fcf422d0235c59c8c7843a"}, {"version": "966dcb6249946e3f801af4014d62850a6ee720bb8f99d509c78427bacc6962e0", "signature": "b357574c6cad3ed368356d17f980db9eefbd6bde3810ac3f22b7c866a324ae98"}, {"version": "b84484c791b75b9f59bbc7b210d99e2050bb9e0979f3f9e82681aa9944c5406c", "signature": "3d8a668aac5b5a165f2fc02896aa8e6ea80c7f0dc865f1e0b4c414590ff7d774"}, {"version": "4d1cf05b4f662dc23b2789a6d365a2cc1baf256b86e9944b0f7a30a0d91d07c0", "signature": "27361f53ea94cbe3c98b6c8fdb619a9ae40ea7729516fe5354dfa7fecec93a1e"}, {"version": "c3c6855fbfc0cd6209686a476d7d89458403a3ef5c3acd00c2880b8531760f3a", "signature": "4b151bd99f67b3841de035aec954fa5b4f471f9b453b3833fce9a7a07a86da80"}, {"version": "f5c3487a93890d2ef5b9d718789ee33e61ad70d0bdb3f31a8968b1fb0fd34f95", "signature": "572908b1c86c56b216cf041d2edd654f0966b296561ae0b5e3a3b09ae7bd4598"}, {"version": "02c0eb2945adc949664fefa5d1a69db1817841df2ba533498c61ee64b651238e", "signature": "f6fadd9797e8f82fcf93858942edb7efe8c3b81ecad4a44cb65885b2ddcac386"}, {"version": "73e07cb21743cdddec3bd43d98d3dc9297981d5e6c132f52dc4e56a0cecc10e5", "signature": "388a025c8124b2e31a4480f059160e2cb25573752cfbf2de04064bb3d7338893"}, {"version": "58f4fd808e5990f14a97603dd5e9782c9d51f3d0858399e4af08cefa3f7ed898", "signature": "c526ab4ab651c404a60b9e16ea7ba4060563bf911a1a56c9773fb17c351e2b5f"}, {"version": "0c8816f09a7deee9873dcaca099b8ba5d55b93d0bbcd57cbd5033d908652a625", "signature": "8ef77dde4c08e42bcb4a0df172aa874a5eebc91d754511c87f0064e9c8cf61fa"}, {"version": "3704b79cbe8c884756900060abcd289abceb58627bff640ee1b4b518382a0aa8", "signature": "aa762a1db4389c3315cf6f3ce11a1d4b03317cb7823889062e59a25476e1e4cb"}, {"version": "b71e4ab2e0d16d2368526c89e29573ad985b0e2c8834a0a5f39d49f8488f370f", "signature": "672ab733129d33d3928f5376ed70ad5eebbd722db2aefd4e9fbe2dc7728e1c49"}, {"version": "f899d998eadb8db2692ae7ea47244d9bbba24ac1baa283e356f7743e8c1c4a81", "signature": "f54891c82314113fe135ae8116bd9dfd60196099305d2c31cba89dd8f1665195"}, {"version": "f51544755e6a928131ccfd4ae5af2831449f8e5ef216861c163d32a3f4805200", "signature": "d3bf0b7e522962b891fb165f781e4c78964fdf10360c04a7ac29cde8a5c1ed9b"}, {"version": "74568c3cf35cbdafe9702d220e6014615595cd028e5fb722c44c3b535fc47d58", "signature": "11ac11ee16c56a6e1333d17d6bb5c334bcc5b2a45d183f147c60273fc36ad4bc"}, {"version": "4fd559bab2bc5907ba8977c87c4a09028147193fd7b2baff3ebda8e19791819a", "signature": "8a07a1cbdb060e1103966afee15126750e6ae8011f0fc5fe5631d37bbd181177"}, {"version": "548dd9a5f21738f6ef3709823fe6540fbb9f163bb7046214235929ad06b69175", "signature": "039df669133bfc1f6190409183de1720d1b21970e072e9b807554e8db89a9059"}, {"version": "a8cafaafb985f82e89293a24140f3ba32dcf80ace105f8e6d1fd72df46e58751", "signature": "70ebc0b228ac83b2b859b4db4d49918138ad035c90d9c8b2b2e336e4c16c9beb"}, {"version": "e60e61545ded6ec34e8a4d8404701be339d2917d3ae78a5f7a3f4041e5f43916", "signature": "dae7514854d2624d6b793b4f9d19d274376a79ee57f2a171cfd3b608b35fcbe8"}, {"version": "78daa0250c76940bb06266742b561f9484d9ef4b90d9ca8060b2b220e7d0b8a8", "signature": "990d6d364a17014799b16380bcfdd49f8c321b82f5bc8da1f8c386ad6958dd32"}, {"version": "b06a89fddd54928fb2982e5f340a4c3c85ad6a1342f23a5fba299997e0a8e7ec", "signature": "ea4dedea402bf55a0a3e76529dc7e75174b686fa9a8e1dcad91e7e6e93938fcf"}, {"version": "6c61accf6dd1f01a6bf68ba5365066a472ae958b4810ba0e794240bed8adcbad", "signature": "cce5a5feae2b66542043b6a3bda2a120981cf366280bc9cadb153ee3a836de4c"}, {"version": "1400da1614db019ff4466cab7de86b535f9455d1aae9fcec9b06ce146c558774", "signature": "6abbce020d13ec33e276f6383d9c55e6c11e20c58e4be64fac355f6bf31dc36e"}, {"version": "ff4ba63133128c085db68c7d71d05eb096ebbd079f9cd677249d3f81d44b90f4", "signature": "da8802fb7d49aad66e3fa4766b487bf1665b82c9e61faafba96787d2f8cde535"}, {"version": "487f43f12e39f59ccce53743080f7de4b21e2b6ba73858f881b2e64608fa9b88", "signature": "33ebc6001bf73f852c565f0aaee162043d6940ad27d096d3f8aa1884b45f6086"}, {"version": "51911cb7cd596ff8e75c5feda55c93b293097d201fbe38ea17421e1eb09a6c5e", "signature": "12f8651679c2e624237b4de23e57d447be9d37720a97bcc011d88b909caba986"}, {"version": "6fb5a58f7c5c3d94a601b3558950c7984df3b8047a1d0ee8a4d2ab7fed2c97c3", "signature": "aff4cdabd4d02429afe37d51af56443ef9b5d2567537629e663912c8b79e3477"}, {"version": "e561aa45b4dc68d05d7f2bdbff87c51b637ee644c3001efeac07bbd9144d017f", "signature": "479db6912b111c096a2ba328ddcbed32d17437261ed4ad8950f52bda9a8bb791"}, {"version": "604ef0f7c055862f41aa81aa60fc6d2be0687aa231f68697a548233aeb5b1f49", "signature": "1aba384721af58b05b673243284d5091a77a4d9d651680fc218853d983a25623"}, {"version": "14985a52c146354bebeb4e6f7cf62ddeadc8fce638fc95cbb1e1f11f490045a2", "signature": "9fe3fa46cd94bff442485c781f11bf0006095d646f9ecac02afd08482f38f506"}, {"version": "b132d31c2cb1806eb50e7ab316c3f91d10467f827aaab725c0fa8d3d875ef4af", "signature": "049d6908b545a31667703326f9ed6194353caae61635ddf324cea526875104ad"}, {"version": "42a95225752b7f31c8af23edb3dc016c02df37e683bb52bfb7aaa4bd5926452e", "signature": "f2c77620dc1afbfe85198d6075c3961def047b4da4322dd0b4a7a9876ce7d571"}, {"version": "29ed723c0b58e2424ec62a5c1ee295dbfd477b1b0b14ea16723703cc8b0a5a75", "signature": "9b956a2a18d4b3fb21aebef2381762d9ac0070a4d6ea8bcdfb7a296a6358bd40"}, {"version": "ddf984c0f708761d1a5559063a012fa8926b6917fa7459a9999b575f0c2cd0bc", "signature": "9709693ede26e0152dea3808e781be0f4f5342005c9f263ea55fa65b9b6d7b2b"}, {"version": "97bc3a3c110c6fc6d25db07a42522acc746606408a2052459b55ec259a2eae44", "signature": "a4d34cb65f0e882dcafcda4dbe78856a5e6de2c25faaece0f854b8a1e1a46e80"}, {"version": "cab16c65ac4eff8325e2c4854064b91e87941ff9d407f48ddf8a805923441d50", "signature": "0e665076fb7d542b669da6ae7ceee8e5c8176b81e8e796518e614fbc20b433ea"}, {"version": "24aca19ec208759527262bdf50df693b3f48c9f911fdd680f74ed26aa8d3d951", "signature": "4c418109f950883f414e07939bb6dca99a3b639fc63ea7f7f438fc8a716a94e1"}, {"version": "29fd1558b1914364a6330c6e39a322e21904b61fa4827674247593f4979e85a3", "signature": "076645c76c5adc1aae21fec3a45d6dac4a1ae4f05d3b1c633917beed770f1b39"}, {"version": "d0f0af361bfc7e4ea926e3c8e21561e71cc7667cd82f3a2363eb957963b28c22", "signature": "d9a2165aac2dc2a9d72c991133a9b52762a47ce919b82609506eceb42cfcec88"}, {"version": "3247feeee712080d1b4b95368d6f975f8fd2ce5115543b96d80830d21c428dd6", "signature": "a3c26ed1ee8f7735d02f892d0cd7beeb3dddab6135daf682cfe31f94dc5e7991"}, {"version": "6c772bc1859ec29a78858506bad80a3f59235487e2966f26a7da1ed752c76238", "signature": "56b4193b7b1abf1d58eb562131e472cbe9887f7ad2fb8ef5ae8acb6ae2285efc"}, {"version": "10f51805007cda7c0375e0af179aac16993849548912bdd77777aa2257d80516", "signature": "b922dc02a083fb54b89eeac6fdeb65db063485db805049b2e3d757c76b1622bd"}, {"version": "e5c2cbbc61814dd9b88cc9215b4e9fda2e1fa50bfc71389e037d260c341b8b70", "signature": "08f107bdb5a87c19c7f97038f9a48c176c4d57388964a85655a12988bab7883a"}, {"version": "c2cffb2e7b771533c84cd1a64c964268e1ba428dee9b8f81ef953c29858017e3", "signature": "5b0f8caca0e296331727f12717162969ac88b45d22e81c7298f479b26d06451a"}, {"version": "0628306b25a98ce5cfbd81e570c5598e998d553353490ad94d2bc8bbbcd5a94f", "signature": "80770d683d60893ad2cc24ea6d584fce7a1f834b573eca2d6d4e62686b50c4d5"}, {"version": "0d16f853e7029117e959a190012e66fbfed48e81ffd17d82354c25672322f649", "signature": "007859fee5a297cf8c832bc4172ae3801343a1c20f9c9c2e6461b4192d7a34d5"}, {"version": "3e6238cd93b55813a0546f23b997feea6af223ea3e03abedf65e25255ff10d09", "signature": "6467bb2c0551ba97674a89cd2e17dd3102119badd75e45730d6ab18dbc0209b7"}, {"version": "1e1416dec1b470fb8752c3866f12f5255dcf89b7be2f7de9e29fd77eaecb4e4e", "signature": "960d686d1ac34aff444ba37374721537ea3ca6b3623b0a9762fdba80df903843"}, {"version": "abc8c45c3e46da8d2136140f8446664b29b43b52c60439202dccef906de08717", "signature": "b7a4fc1e70c9f6754df9f29d5a7b35b32af55c338576109bde92ebb93397ce63"}, {"version": "4cab2fede2300fc7ce073016c3360491e29a557e13cf0d9f4eab9c4532267e3a", "signature": "92b865bd32b79c959421f6b4038abed31addf71188441b2b3699d3171bf3bc17"}, {"version": "34d05aeeb72fce9b19f3b0e357fabf7fcdd964b01cdc75841a2170ae6ff5014a", "signature": "bc97e287525a89f8383d31b293a5864fe407a5c90a4d04102c3b9c9c1c8323cd"}, {"version": "37d47df5cb8a71d19f280cd1376b0cf275c8ef5b703c1edee2bff0698d68aa59", "signature": "90fd983ea7aba602945fb86159a96d671818a9340a09e36edeb069b187ce8a0d"}, {"version": "4da6a84fcf0f3a58d8b73298d4e5665288b1a593032c99511cc8e8d5551e383e", "signature": "1cacf783e1851ec49e29a26d18ac819790bae3323752df7e3b1199f9c8d7ad78"}, {"version": "2c34533555f022bf0367657ef78a7ae5d13d3a7aedb6d6044966b536f5560861", "signature": "16386df1fbaeccbcf0337d3e482661761b35e1e7a1fc2439d225afedd1bde445"}, {"version": "c7c7f2cf80fc23b2d9b81a599e66d4ff5eb195bb9ea39c8c4493fee409bea524", "signature": "6665138a546417854e2e4052c4ad623491c10d2db87e93b50a6c6e56d88493e0"}, {"version": "4bc0af1811177d089e8c5ac42c9ba4deb830abdf4ad73448dc613fecb3a7623f", "signature": "93e1b6020078a55235ca74a8fc69cb23708ce4782d4e92c6feb9bfc7657f568c"}, {"version": "8edbbe77ed76d1fd34473e6eee32842ad924125a0e1f4d830168db2fa0e4586a", "signature": "84ceab1f99c513c5c5a9fba45e4c81ed0db4ee4ba39d044bc618d4c63d3cc493"}, {"version": "1ad5cd08861e9baba6bc79f3366490bac1262b7e8bf14f5e85a480a61ead56b0", "signature": "2e1de782466c173dd37d52346d138d6e977f3357ccd2859e6b0fa1e2c48d95bd"}, {"version": "84f21c3c5fd5a18eda20a686b75a50d2d9ceaa403f4e231fb0600b4e3eff35cc", "signature": "8246da6759ef4efe159d91009fc7c53d6dc819dba8d27428ba8d50d420b7679c"}, {"version": "f9652a5dd482cb931e5bea4b50bbe63c8f88d39b622913f1a953fbce2c20c056", "signature": "23307dc30ad4d4d3533b7a08403a8efed5bfc03eb33a4cb0880633eea8506ac2"}, {"version": "8b123efd89bbb087e54fffcd5dc0b6f16dc87a603e5a44dbeaf38790e0b58c6c", "signature": "a17145df38d7e8f2e5cc15af6610c6e2754598a7a71e18eef65b23d03f50af9b"}, {"version": "653f796e57f57e8289868a7f95ca6a9dce6631ca4c2338ae031ca3be61135195", "signature": "ba41642922250cc4bc3d4357abf263cabe583f2a6623170c2e32ea41b614d093"}, {"version": "acf15ea11890b048061fe4c1fd95f255a6ee13c771603113a6dda1a2aecadbbd", "signature": "2021be1ed16b739dff6794dd0421da3d5ceeffaaf40e1f19b6df840dc04eac0d"}, {"version": "ab0328b70167ba40d364246343b088622f32efd6a818119ab59b799ece96f4f7", "signature": "e94a372e7350675877f9547072eaa91d44b23ccaf74fafba2b379696b25dd967"}, {"version": "6d454d5a00da6738726a8939dcd35c9c98641217ff06a76f5143a4aa6fc9db99", "signature": "938374e24d6dea631dbd49be346b7abd481da3a2f6b7c4c4d6ff2d5f460a21a7"}, {"version": "5f98e58589cfb3b0296ff4989c7364405abf9b5d2c6d74aae7aa10c07abfa5ca", "signature": "e955b0f4edddc076b686fd478d96bd62df91ed88b774d4d7972912f532891958"}, {"version": "e9a3e63b4c267eb93ce37380c357c6099352fdd2ccd1233847a0c97a46b770a6", "signature": "c2970147fdbc5d276935accaa6e2a6e2844e303bc2a6dd92f7623fd3a94eee2f"}, {"version": "e23540c598fd9e4548cee450ff93e73eb823dd86c35cf860792460a5c2e8f49a", "signature": "b7c6cc6ebc4191a0739f76c97d51890e5798c07b889b921db304ac02bd0b8f09"}, {"version": "33fe8d1005cf9f915feecc660ec8bc79f50b109727bd91c427f4b389e816f952", "signature": "633e661b422e08d7f74e13bf7fa8848ff045230aeaafa2025aa71641e965f694"}, {"version": "c987588ba3d07e5294ca8ea7e2b4c47de4b187e8069c8fabf5b03da6be3eb7a2", "signature": "646ffdd61f99892108b3c026b02e0b3f2b3fd55150f552bb8b57ae9764f796d7"}, {"version": "6feba102d6c6bdf21b014061133ae44541b48b2a22b6d1c764e9cd805f862caa", "signature": "716bce3d042d4c46cf3fa979c4577b6a23d5859a79a907ef55139f17e8195ad2"}, {"version": "0aa6cc8333ed1e5d0f067f6e296c92fc5c2d4bd4769bea663f573129456c05cc", "signature": "9917b371f53b7838810d6c170c5ba590f43b60b2925a82acb55b9c8ef2221f96"}, {"version": "5aa882be52b887f656af7a6f7e6d5ca971f07d8e277ab272b65bec34cbb6fa56", "signature": "ec89de6c4914ecab4385756a26409a8526df923dd0a9767c818cf42824c7b545"}, {"version": "4d7a0c8e2da53fd5b0d98c85039f0ade6354a7ed4662e919c3730c34820fdae6", "signature": "f8e5e23c57153a10f1b33bcf66b3b6402482996c528344dfd54c4114b115789d"}, "83e27bbd7304ea67f9afa1535f1d4fdb15866089f0d893c784cbb5b1c6fb3386", {"version": "6231095d0b356894ceec8856ce4e354a38a773a62067978f3af9c478acbcf2e9", "affectsGlobalScope": true}, "66754e21a2ac3ffc0eb3fc55cf5c33deb2c115b726fb2c4368b870c5e85106ce", "ccd9f8f7c8760d8eb0c0c112c772a9756d59365a61fd5cc102a889fed79d0076", "d7ccbcf0e92d0209f499729768c791fbc278ecad02c3f596db5b519eb4f5d496", "85542ea0f889f8993e6b4631c541640c17c230bf5cf18d483184de9105536b4d", "8cc2eed7caa0e187fae67a1e7fdb4da79079489744c6e5be9e0839d258fd5995", "aaf2cc4016c33c836fcca0dbe20d7a97bf17cafeb581a57a62267b1c38930bd4", {"version": "b56f87a14cc2ca254c01b08da27fb4d33d1d6a84148575af34a51185cb206b8a", "signature": "22e2ce0dbe0b43a105afc528395f86a8c758a4efa0a1ab2aa3956bcc9624b8d9"}, {"version": "80f4b99d577887214270884a40cdb328c1954a18ae2de1f1db0822d98d3d7278", "signature": "85e1cdcaa35e580205336a696020943072a7285f894d3d546927f3c95cfaa3e3"}, {"version": "3f6eae5e1255ed7145a5d3b86fdd946aa49e595d8c661f0c8b7aedce450c4841", "signature": "9c0d91c8dd07033d2bdc78c408507d4cd5c5f96be51b89fbf7931bc2df7752a0"}, {"version": "e51c4e755a9002e36179512ac648dbd11af6a244f1143f1339a8a73e590012ce", "signature": "a8ce1f67cf1af5cf481828bc9eff67efb4c1475b14f52c5f0c3bd0f7589a664d"}, "76473bcf4c38aeed6cde4eaaf8dcc808741dbe5280b957b982603a85421163a6", "40c4f089842382be316ea12fd4304184b83181ab0a6aa1050d3014d3a34c5f8f", "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "b6d03c9cfe2cf0ba4c673c209fcd7c46c815b2619fd2aad59fc4229aaef2ed43", "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "81df92841a7a12d551fcbc7e4e83dbb7d54e0c73f33a82162d13e9ae89700079", "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", {"version": "6c7176368037af28cb72f2392010fa1cef295d6d6744bca8cfb54985f3a18c3e", "affectsGlobalScope": true}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true}, {"version": "437e20f2ba32abaeb7985e0afe0002de1917bc74e949ba585e49feba65da6ca1", "affectsGlobalScope": true}, "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", {"version": "613b21ccdf3be6329d56e6caa13b258c842edf8377be7bc9f014ed14cdcfc308", "affectsGlobalScope": true}, {"version": "d2662405c15ec112ebc0c3ec787edb82d58d6acb1a9d109317d7bf9cff9d09a7", "affectsGlobalScope": true}, "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "7180c03fd3cb6e22f911ce9ba0f8a7008b1a6ddbe88ccf16a9c8140ef9ac1686", "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "54cb85a47d760da1c13c00add10d26b5118280d44d58e6908d8e89abbd9d7725", "3e4825171442666d31c845aeb47fcd34b62e14041bb353ae2b874285d78482aa", "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "a967bfe3ad4e62243eb604bf956101e4c740f5921277c60debaf325c1320bf88", "e9775e97ac4877aebf963a0289c81abe76d1ec9a2a7778dbe637e5151f25c5f3", "471e1da5a78350bc55ef8cef24eb3aca6174143c281b8b214ca2beda51f5e04a", "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "db3435f3525cd785bf21ec6769bf8da7e8a776be1a99e2e7efb5f244a2ef5fee", "c3b170c45fc031db31f782e612adf7314b167e60439d304b49e704010e7bafe5", "40383ebef22b943d503c6ce2cb2e060282936b952a01bea5f9f493d5fb487cc7", "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "3a84b7cb891141824bd00ef8a50b6a44596aded4075da937f180c90e362fe5f6", "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "33203609eba548914dc83ddf6cadbc0bcb6e8ef89f6d648ca0908ae887f9fcc5", "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "e53a3c2a9f624d90f24bf4588aacd223e7bec1b9d0d479b68d2f4a9e6011147f", "339dc5265ee5ed92e536a93a04c4ebbc2128f45eeec6ed29f379e0085283542c", "9f0a92164925aa37d4a5d9dd3e0134cff8177208dba55fd2310cd74beea40ee2", "8bfdb79bf1a9d435ec48d9372dc93291161f152c0865b81fc0b2694aedb4578d", "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "d32275be3546f252e3ad33976caf8c5e842c09cb87d468cb40d5f4cf092d1acc", "4a0c3504813a3289f7fb1115db13967c8e004aa8e4f8a9021b95285502221bd1", {"version": "62e6d5e4c96562631e514973edcc8680849e555692ea320b1ca56ab163393ecf", "affectsGlobalScope": true}, "2aadab4729954c700a3ae50977f5611a8487dc3e3dc0e7f8fcd57f40475260a8", "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "75eb536b960b85f75e21490beeab53ea616646a995ad203e1af532d67a774fb6", {"version": "befbf9d2259d0266234e6a021267b15a430efd1e1fdb8ed5c662d19e7be53763", "affectsGlobalScope": true}, "51bb58ef3a22fdc49a2d338a852050855d1507f918d4d7fa77a68d72fee9f780", "7646ad748a9ca15bf43d4c88f83cc851c67f8ec9c1186295605b59ba6bb36dcb", {"version": "cef8931bc129687165253f0642427c2a72705a4613b3ac461b9fa78c7cdaef32", "affectsGlobalScope": true}, "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "47b62c294beb69daa5879f052e416b02e6518f3e4541ae98adbfb27805dd6711", "f8375506002c556ec412c7e2a5a9ece401079ee5d9eb2c1372e9f5377fac56c7", "e3460c2b8af8bf0fdf0994388a9e642fff700dc0bcedf6c7c0b9bed4a956b3da", "548d9051fd6a3544216aec47d3520ce922566c2508df667a1b351658b2e46b8d", "c175f4dd3b15b38833abfe19acb8ee38c6be2f80f5964b01a4354cafb676a428", "b9a4824bb83f25d6d227394db2ed99985308cf2a3a35f0d6d39aa72b15473982", {"version": "ee1ee365d88c4c6c0c0a5a5701d66ebc27ccd0bcfcfaa482c6e2e7fe7b98edf7", "affectsGlobalScope": true}, {"version": "b84f34005e497dbc0c1948833818cdb38e8c01ff4f88d810b4d70aa2e6c52916", "affectsGlobalScope": true}, "8e8e284b3832911aeede987e4d74cf0a00f2b03896b2fd3bf924344cc0f96b3c", "37d37474a969ab1b91fc332eb6a375885dfd25279624dfa84dea48c9aedf4472", "577f17531e78a13319c714bde24bf961dd58823f255fa8cabaca9181bd154f2a", "6b3c4aa0ce6eb9cf6187e61d352cd269ff0e492f333ae102dda121e76f90285c", "565fda33feca88f4b5db23ba8e605da1fd28b6d63292d276bdbd2afe6cd4c490", "e822320b448edce0c7ede9cbeada034c72e1f1c8c8281974817030564c63dcb1", {"version": "ebfc5ac063aa88ab26982757a8a9e6e9299306a5f9ea3e03ea5fd78c23dc5d79", "affectsGlobalScope": true}, "f23e3d484de54d235bf702072100b541553a1df2550bad691fe84995e15cf7be", "821c79b046e40d54a447bebd9307e70b86399a89980a87bbc98114411169e274", "17bc38afc78d40b2f54af216c0cc31a4bd0c6897a5945fa39945dfc43260be2c", {"version": "0e6726f7ab7649f3c668f4eadb45461dcfaab2c5899dd7db1e08f8a63905eb94", "affectsGlobalScope": true}, {"version": "d44445141f204d5672c502a39c1124bcf1df225eba05df0d2957f79122be87b5", "affectsGlobalScope": true}, "de905bc5f7e7a81cb420e212b95ab5e3ab840f93e0cfa8ce879f6e7fa465d4a2", "bc2ff43214898bc6d53cab92fb41b5309efec9cbb59a0650525980aee994de2b", "bede3143eeddca3b8ec3592b09d7eb02042f9e195251040c5146eac09b173236", "64a40cf4ec8a7a29db2b4bc35f042e5be8537c4be316e5221f40f30ca8ed7051", "294c082d609e6523520290db4f1d54114ebc83643fb42abd965be5bcc5d9416b", "5a64238d944ada60d4bec0f91ba970a064618ae3795cff27bb163c84b811284a", "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", {"version": "63c3208a57f10a4f89944c80a6cdb31faff343e41a2d3e06831c621788969fa7", "affectsGlobalScope": true}, "b85151402164ab7cb665e58df5c1a29aa25ea4ed3a367f84a15589e7d7a9c8ca", {"version": "5d8cd11d44a41a6966a04e627d38efce8d214edb36daf494153ec15b2b95eee2", "affectsGlobalScope": true}, {"version": "bc6cb10764a82f3025c0f4822b8ad711c16d1a5c75789be2d188d553b69b2d48", "affectsGlobalScope": true}, "41d510caf7ed692923cb6ef5932dc9cf1ed0f57de8eb518c5bab8358a21af674", "2751c5a6b9054b61c9b03b3770b2d39b1327564672b63e3485ac03ffeb28b4f6", "dc058956a93388aab38307b7b3b9b6379e1021e73a244aab6ac9427dc3a252a7", "f33302cf240672359992c356f2005d395b559e176196d03f31a28cc7b01e69bc", {"version": "3ce25041ff6ae06c08fcaccd5fcd9baf4ca6e80e6cb5a922773a1985672e74c2", "affectsGlobalScope": true}, {"version": "652c0de14329a834ff06af6ad44670fac35849654a464fd9ae36edb92a362c12", "affectsGlobalScope": true}, "3b1e178016d3fc554505ae087c249b205b1c50624d482c542be9d4682bab81fc", "dae82763de98657d13112532b6f88fbf4c401d4656c4588a0cd2a95e7b35272b", "cf45d0510b661f1da461479851ff902f188edb111777c37055eff12fa986a23a", {"version": "cb41c174db409193c4b26e1e02b39a80f3050318a6af120cc304323f29e1ec1b", "affectsGlobalScope": true}, "37bef1064b7d015aeaa7c0716fe23a0b3844abe2c0a3df7144153ca8445fe0da", "75bd411256302c183207051fd198b4e0dbab45d28a6daf04d3ad61f70a2c8e90", "d3f2d715f57df3f04bf7b16dde01dec10366f64fce44503c92b8f78f614c1769", "b78cd10245a90e27e62d0558564f5d9a16576294eee724a59ae21b91f9269e4a", "baac9896d29bcc55391d769e408ff400d61273d832dd500f21de766205255acb", "2f5747b1508ccf83fad0c251ba1e5da2f5a30b78b09ffa1cfaf633045160afed", {"version": "86ea91bfa7fef1eeb958056f30f1db4e0680bc9b5132e5e9d6e9cfd773c0c4fd", "affectsGlobalScope": true}, "b71c603a539078a5e3a039b20f2b0a0d1708967530cf97dec8850a9ca45baa2b", "0e13570a7e86c6d83dd92e81758a930f63747483e2cd34ef36fcdb47d1f9726a", "104c67f0da1bdf0d94865419247e20eded83ce7f9911a1aa75fc675c077ca66e", "cc0d0b339f31ce0ab3b7a5b714d8e578ce698f1e13d7f8c60bfb766baeb1d35c", {"version": "a45c25e77c911c1f2a04cade78f6f42b4d7d896a3882d4e226efd3a3fcd5f2c4", "affectsGlobalScope": true}, "5c45abf1e13e4463eacfd5dedda06855da8748a6a6cb3334f582b52e219acc04", "62f2d6b25ff2f5a3e73023781800892ff84ea55e9f97c1b23e1a32890a0d1487", "858c5fcc43edd48a3795281bd611d0ccc0ff9d7fdce15007be9e30dad87feb8e", "7d1466792b53ca98aa82a54dbed78b778a3996d4cbda4c1f3ba3e2ed7ba5683a", "1828d8c12af983359ea7d8b87ec847bbaf0f7e6f3c62fb306098467720072354", {"version": "5e2485f40a10d12efc03acf267bee528620075f8dc9f0cb0b570bcb9709ba14e", "affectsGlobalScope": true}, "a7a9834ecced9288b5b6f5859443307adedaf02a59b1412c8a9af2055081823f", "cd51ceafea7762ad639afb3ca5b68e1e4ffeaacaa402d7ef2cae17016e29e098", "1b8357b3fef5be61b5de6d6a4805a534d68fe3e040c11f1944e27d4aec85936a", "4a15fc59b27b65b9894952048be2afc561865ec37606cd0f5e929ee4a102233b", {"version": "744e7c636288493667d553c8f8ebd666ccbc0e715df445a4a7c4a48812f20544", "affectsGlobalScope": true}, "c05dcfbd5bd0abcefa3ad7d2931424d4d8090bc55bbe4f5c8acb8d2ca5886b2e", "326da4aebf555d54b995854ff8f3432f63ba067be354fa16c6e1f50daa0667de", "90748076a143bbeb455f8d5e8ad1cc451424c4856d41410e491268a496165256", "76e3f3a30c533bf20840d4185ce2d143dc18ca955b64400ac09670a89d388198", "144dfcee38ebc38aae93a85bc47211c9268d529b099127b74d61242ec5c17f35", "2cf38989b23031694f04308b6797877534a49818b2f5257f4a5d824e7ea82a5a", "f981ffdbd651f67db134479a5352dac96648ca195f981284e79dc0a1dbc53fd5", "e4ace1cf5316aa7720e58c8dd511ba86bab1c981336996fb694fa64b8231d5f0", "a1c85a61ff2b66291676ab84ae03c1b1ff7139ffde1942173f6aee8dc4ee357b", "f35a727758da36dd885a70dd13a74d9167691aaff662d50eaaf66ed591957702", "116205156fb819f2afe33f9c6378ea11b6123fa3090f858211c23f667fff75da", "8fe68442c15f8952b8816fa4e7e6bd8d5c45542832206bd7bcf3ebdc77d1c3f3", "3add9402f56a60e9b379593f69729831ac0fc9eae604b6fafde5fa86d2f8a4b9", "cc28c8b188905e790de427f3cd00b96734c9c662fb849d68ff9d5f0327165c0d", "da2aa652d2bf03cc042e2ff31e4194f4f18f042b8344dcb2568f761daaf7869f", "03ed68319c97cd4ce8f1c4ded110d9b40b8a283c3242b9fe934ccfa834e45572", "de2b56099545de410af72a7e430ead88894e43e4f959de29663d4d0ba464944d", "eec9e706eef30b4f1c6ff674738d3fca572829b7fa1715f37742863dabb3d2f2", "cec67731fce8577b0a90aa67ef0522ddb9f1fd681bece50cdcb80a833b4ed06f", "a14679c24962a81ef24b6f4e95bbc31601551f150d91af2dc0bce51f7961f223", "3f4d43bb3f61d173a4646c19557e090a06e9a2ec9415313a6d84af388df64923", "18b86125c67d99150f54225df07349ddd07acde086b55f3eeac1c34c81e424d8", "d5a5025f04e7a3264ecfa3030ca9a3cb0353450f1915a26d5b84f596240a11cd", "03f4449c691dd9c51e42efd51155b63c8b89a5f56b5cf3015062e2f818be8959", "23b213ec3af677b3d33ec17d9526a88d5f226506e1b50e28ce4090fb7e4050a8", "f0abf96437a6e57b9751a792ba2ebb765729a40d0d573f7f6800b305691b1afb", "7d30aee3d35e64b4f49c235d17a09e7a7ce2961bebb3996ee1db5aa192f3feba", "eb1625bab70cfed00931a1e09ecb7834b61a666b0011913b0ec24a8e219023ef", "1a923815c127b27f7f375c143bb0d9313ccf3c66478d5d2965375eeb7da72a4c", "4f92df9d64e5413d4b34020ae6b382edda84347daec97099e7c008a9d5c0910b", "fcc438e50c00c9e865d9c1777627d3fdc1e13a4078c996fb4b04e67e462648c8", "d0f07efa072420758194c452edb3f04f8eabc01cd4b3884a23e7274d4e2a7b69", "7086cca41a87b3bf52c6abfc37cda0a0ec86bb7e8e5ef166b07976abec73fa5e", "4571a6886b4414403eacdd1b4cdbd854453626900ece196a173e15fb2b795155", "c122227064c2ebf6a5bd2800383181395b56bb71fd6683d5e92add550302e45f", "60f476f1c4de44a08d6a566c6f1e1b7de6cbe53d9153c9cc2284ca0022e21fba", "84315d5153613eeb4b34990fb3bc3a1261879a06812ee7ae481141e30876d8dc", "4f0781ec008bb24dc1923285d25d648ea48fb5a3c36d0786e2ee82eb00eff426", "8fefaef4be2d484cdfc35a1b514ee7e7bb51680ef998fb9f651f532c0b169e6b", "8be5c5be3dbf0003a628f99ad870e31bebc2364c28ea3b96231089a94e09f7a6", "6626bbc69c25a92f6d32e6d2f25038f156b4c2380cbf29a420f7084fb1d2f7d7", "f351eaa598ba2046e3078e5480a7533be7051e4db9212bb40f4eeb84279aa24d", "5126032fe6e999f333827ee8e67f7ca1d5f3d6418025878aa5ebf13b499c2024", "4ce53edb8fb1d2f8b2f6814084b773cdf5846f49bf5a426fbe4029327bda95bf", "1edc9192dfc277c60b92525cdfa1980e1bfd161ae77286c96777d10db36be73c", "1573cae51ae8a5b889ec55ecb58e88978fe251fd3962efa5c4fdb69ce00b23ba", "75a7db3b7ddf0ca49651629bb665e0294fda8d19ba04fddc8a14d32bb35eb248", "f2d1ac34b05bb6ce326ea1702befb0216363f1d5eccdd1b4b0b2f5a7e953ed8a", "789665f0cd78bc675a31140d8f133ec6a482d753a514012fe1bb7f86d0a21040", "bb30fb0534dceb2e41a884c1e4e2bb7a0c668dadd148092bba9ff15aafb94790", "6ef829366514e4a8f75ce55fa390ebe080810b347e6f4a87bbeecb41e612c079", "8f313aa8055158f08bd75e3a57161fa473a50884c20142f3318f89f19bfc0373", "e789eb929b46299187312a01ff71905222f67907e546e491952c384b6f956a63", "a0147b607f8c88a5433a5313cdc10443c6a45ed430e1b0a335a413dc2b099fd5", "a86492d82baf906c071536e8de073e601eaa5deed138c2d9c42d471d72395d7e", "6b1071c06abcbe1c9f60638d570fdbfe944b6768f95d9f28ebc06c7eec9b4087", "92eb8a98444729aa61be5e6e489602363d763da27d1bcfdf89356c1d360484da", "1285ddb279c6d0bc5fe46162a893855078ae5b708d804cd93bfc4a23d1e903d9", "d729b8b400507b9b51ff40d11e012379dbf0acd6e2f66bf596a3bc59444d9bf1", "fc3ee92b81a6188a545cba5c15dc7c5d38ee0aaca3d8adc29af419d9bdb1fdb9", "a14371dc39f95c27264f8eb02ce2f80fd84ac693a2750983ac422877f0ae586d", "755bcc456b4dd032244b51a8b4fe68ee3b2d2e463cf795f3fde970bb3f269fb1", "c00b402135ef36fb09d59519e34d03445fd6541c09e68b189abb64151f211b12", "e08e58ac493a27b29ceee80da90bb31ec64341b520907d480df6244cdbec01f8", "c0fe2b1135ca803efa203408c953e1e12645b8065e1a4c1336ad8bb11ea1101b", "f3dedc92d06e0fdc43e76c2e1acca21759dd63d2572c9ec78a5188249965d944", "25b1108faedaf2043a97a76218240b1b537459bbca5ae9e2207c236c40dcfdef", "a1d1e49ccd2ac07ed8a49a3f98dfd2f7357cf03649b9e348b58b97bb75116f18", "7ad042f7d744ccfbcf6398216203c7712f01359d6fd4348c8bd8df8164e98096", "0e0b8353d6d7f7cc3344adbabf3866e64f2f2813b23477254ba51f69e8fdf0eb", "8e7653c13989dca094412bc4de20d5c449457fc92735546331d5e9cdd79ac16e", "189dedb255e41c8556d0d61d7f1c18506501896354d0925cbd47060bcddccab1", "48f0819c2e14214770232f1ab0058125bafdde1d04c4be84339d5533098bf60a", "2641aff32336e35a5b702aa2d870a0891da29dc1c19ae48602678e2050614041", "e133066d15e9e860ca96220a548dee28640039a8ac33a9130d0f83c814a78605", "574346a298daa75f0ae64ff8a9f5d2c90e1e3596364434ba70d5a7ed32ec41c9", {"version": "7cb7ea228360ac7a88254ce376598782259b6ea8ebedd43200b234cdbffac85b", "signature": "7cd3884f21f26d2aca97e468591dedd2488ae5cf8b95f47a39e33b05c3cf2a5b"}, {"version": "58e441d017e0eebff07c9981664c3af7c941c53b12a9294b623df6cb3c8e8055", "signature": "41e8cc43189ec8d9f04159c636f7d5b0a9c002c0dc4c3c98d14b70df261125e1"}, {"version": "f424d8684519d78c2cc380a115979d7d1e661eb1ef0679b9a43de99cb9391f67", "signature": "92edc8696e1af2e753ad0ebb161454ddc877b1a97b09d5b03c028c5c127600ce"}, {"version": "cf78ae1052f10064367b50fc6dbbdd6dde8ad16f1f4aa0360cbfc279bc92f5e4", "signature": "e2d08789c80579cb7efc4e7365a9da951c75085281686155d49a2806a0236515"}, {"version": "8b32e44312f0e8c0da9f415c2be8cb513d954e308e0e46bc6bd40b24508fb3d5", "signature": "798ddb023eaed5316ac5d40dbefdd5a537a543d622ad36a30de28079c65db2fa"}, {"version": "6e2506a65839fe525922f094b67c7a7d5afb2d94e70a5659e82bd184bc4eac47", "signature": "5f372fe4e9f87f1140d165842cc5b08c18074cc3c8ef93815821cc498c516df1"}, {"version": "cc5110831fe610aece076220cfdc163817fc295ff4f898a52464c17461f20eae", "signature": "a7601b4dcb10a9be5babb7a5ba56c48e1dc6d9a53f61e472ec3f12ae016c5191"}, {"version": "38bccc1f09383eb4a7d7aee2a6e2cfd8fc3287c177bf3a9e77b81a2464cfc578", "signature": "865685d162727e367ea7ea9e3eaa34c5df6d79c0a2f838b4567a28cb72174736"}, {"version": "9891c9c20f63447b3c3f757c7dc4f058c91107232ec18056ad55d5911ac4b940", "signature": "6ffac02737acffadbae66f13aceaa74c525600d2f565af8b7d2f3a36cb82da4b"}, {"version": "ac4fb1a0af0eb93f5d3612b91bfe99cce3641fb258e218f95a2ad6cabb66fd3c", "signature": "b3f559d8a8cd73299d508a459e0fd5b4cfc9208c814ca53f2830fdb748db4a8a"}, {"version": "12158b45b18483114d9c4d6b31cae9c7fed6de3a5482348973bc82a1272b6b93", "signature": "7a2999a4f3fb2f512865ea7a19860ed20347ab22bac0d9aa0a4d5d996c0be97b"}, {"version": "ed8f18925a664eb9943a0f4c8d9e73a24890dd0d0d9f63e7f0250062edc4f9e8", "signature": "e697418da584fc270971cc340be7a5239094a90eb32c0da3503db05cc0cdd220"}, {"version": "7289b0f801fa6546eb60b16a9e57c6e0742ccc32724e32563b1b153b564941b4", "signature": "af898fa56c457099a6dd1d8b3a304ddc40f004ffd56140c08fb2190d6cff1cd1"}, {"version": "a463e539fb7637f43bfc6f2a23182a9c9c929ad6e17514928eeb544863f0a90a", "signature": "a43b57a8bec795499823bb1412f6390fa94eb4582f1da89021fa402b39782159"}, {"version": "f9ac8a06a26bed2c99182b737e526df835b378222b577b6c120875888b0917e7", "signature": "0489d190849e39ef2e37ed37eaaa997656e25acfe280b7d8472435e898066219"}, {"version": "f41637b37a4d3c40a282ff3b0c18fc31be440d98354ac5194d575da54a45b72d", "signature": "3fdf938c520663236af53b01a076b25e90bf8432c6fa8425bf5329726a782115"}, {"version": "486f0d226b3387baba7e970e8a967f6af79c83aafcce8b35334d17878030c77c", "signature": "3789b482cdd1e3b95965c9eeb116da588b2b8e9073e2e077b0b20eafdce9ccd4"}, {"version": "8d154fac60715bfb5fae947bf1ccd133a2bd43203f6b438465d0e7ef2cf7ae27", "signature": "8fc32d346f0670e899aeba502e9638e207d3b1b367712cc257cc1f5e224e845f"}, {"version": "425c8e9f4bf4c5e7d611bbc99464e723a4490a97dfa568dd9a885e33f9dddac9", "signature": "96f7d35cf46a4caedd1f7e9f326bfe3b3a7507d28cc63d09df1555992402bce0"}, {"version": "24ba46e305519c126156b89b597c67f3e5d50a1695bf07b04b99ba8ddbb5f12b", "signature": "bf8bb3155417be61d573eee2e0ba945e8884e8590ddb4e081e16ba7d949a0b27"}, {"version": "812b8c063db0007ab1020258d1fce6846d272a09fcd85bc7de100e2e5c85870f", "signature": "ecd12ffa345eec9cccefeb1b7113bd42b362440965adadfc648c523d6f8f3bd1"}, {"version": "653a4fe259b55c32cd5a67cb7fb467d99a0994c02a6e271715b1c0390762efb7", "signature": "68d07ee170f6a3b98e66a967cda122255171bb477c5819fe27c9759298e5c3fc"}, {"version": "a8b729499c700fc6ae6187eda9a4de77f8810f803ed1005410c6ac5177845c51", "signature": "59f44b5f3bb7e5d28a57aed81f63a3a76c2170ed1d702c4ee3a6920b44bb7e8d"}, {"version": "8762eba4e297aa3edb8af9be3710ac873af3165c1e349ee3baebcbfc09857572", "signature": "641d17d17b8d442abeaa1312bd62047bacd999d27ac4d6c1e72416e281c24da6"}, {"version": "17263ab8f8afe006c3fc4d553bd478ea1240737afcd22584a1a9ef7d579c3c92", "signature": "76852155771324e2401caf1bd1c141c6866434a982ae06636aa78cce52487e5d"}, {"version": "389d11080c931bdce2289a8398b2e90c89e1189fd2109dda00e883813c3c32bd", "signature": "d24c265f6d9b4ced8f28eda8ce2c99f239f419ab3e4b923a673660f3e0a0b860"}, {"version": "46532c6f7a4050488007f21ecd64d87adfdc783d544b54892a7176d125d5f2aa", "signature": "19e0c89ba6925d5b05bee8636d05e34091c76f5e6d773ad16c326618a1506599"}, {"version": "196e4a7243ecb0f7bebc135cf1a7f0e352f2cf32e172b1932cfa61155023e4ca", "signature": "30a3122607edfbfc8c99ff5cdda55b5a1faed243f4ae3ed41d3228c7f7b4f7dc"}, {"version": "3acdbe54b18a522546fc6e622d0232d1d2ea082db3b838001c5cbb4218ea8e14", "signature": "419416682191008fd4eee29bf9d8fb71b8f3982e299b97314db32a41969f264a"}, {"version": "f21422e95538bb44411cf01a1140e7e4a2f094694e37ead8967eed1347dc29a7", "signature": "708d31baa2361105516ec2b499dd2e41000368b50149379412c13d284ffb9872"}, {"version": "b5e3ee52725067d3c171f4e0f686c0e2cc20414fd7758be3601e918c7f1731e3", "signature": "55ac7dc7905c3c426157c8448b95147135b7178589c7bc4cdde17956fb53ef8c"}, {"version": "77940e81de13a65c046e8cacd5132326b6344200f69d3488d4ab206e976457d6", "signature": "6a6f70fdcbe7b972e6da2f5df66e0e7da0d78f04f6527121fd270aa82e7dd56d"}, {"version": "af5d7c1a27c9da5c1cfc80a96f9c64dbe37191e0f8f21166e50ce050009449c2", "signature": "93742f90dd04e1618c57022b337b20dc5686ff13371a4362e5cd78e49f4ded53"}, {"version": "6ff60483264628ca2382b79136730095c1a9da871e8dcb3855e50710c623944d", "signature": "8faa4e48e246bb267970a386e080abd9d9c33a4fd2dd0f8e963773cae438161c"}, {"version": "8081f196201d6c506b42c555bd1367c31d08674f905c7b8a8ccca88eda974e30", "signature": "ef0622fe1d82ea6e4b98a7e455d17d2906206d2948ec90c7e54e944f79b981c7"}, {"version": "ebbdf25dbdde9e03ae04739ed4f191d467b738dc2691d5f1ca9e5b06141c2634", "signature": "9fc71a6c8e905a92b10bdce5ebd40a83a33b9a4c7fc9c4b211d4a2c70fc072ce"}, {"version": "1b2e9c14a9b4a36c192ee3ab9b55d869d26c2c08cbf8eaeeab159c70cecd139d", "signature": "82ec0cfeb73ba5f53530fdb4c32778f006dee431eba01b0ec87632589d8f3033"}, {"version": "e3acf79fa8502256a404571a4da2cd876a04b809674d1f1439ea118c8f0f0bc9", "signature": "56eebbbac657fb184885e904b2925c1f934a763421743ab080bbeeca5159081c"}, {"version": "61c4665fa0560115fba39343d08b7c34b69c6e39367791ec602bb91f2cd70570", "signature": "1324919c76063cb07cd0f470799b6f38619661acb0d06fe97d3995e39b1251eb"}, {"version": "65d1e024372f3c533fb57e0d33aeddb837f217d2f9816093fadbc36cead4bb21", "signature": "96f1e93b3fdddd105339be35afe132292e47d1dc201da985cd952b325ba0f8fc"}, {"version": "d0b17a9119a82817973e7bc148233cd47fdd93170134a3f82e877707cd9eff5c", "signature": "ff10ea19e886fdea6e821f8bae3a3b2f9efde13cfce6411fcea2189f77dc90dc"}, {"version": "f17de8f86995601f21db443f9da339cfd0ce9cc0b2f7d8515c262503fadc7580", "signature": "99d786ab622c9b56adcb58f9f0538172bcded1e8b068423d496eeaec27d90220"}, {"version": "4a568319899914004ee04d5a406baf1b0cc7758d1fa8861d5c44e335b7487820", "signature": "5783c7bc7e2f935270dbcb674c47b947a06d02aa08cee2c43defb52975b0b5cf"}, {"version": "d575dc2957d389dee056d0da527900b910ba71cfa2748c23c2fb58a3edab5fcb", "signature": "dfc91ce2f3302d76ab31bc26a5bca17b0930483af768dfdb53f6207b0725b547"}, {"version": "70d6322f426d8692b186f9bad7fd714f19f644957a97ea896b3d972cc31d8693", "signature": "083f81f27279cf3bc71c7dc3f2490e0e2f98a167db3a7349a34a9ac5c6a1f32f"}, {"version": "3b5cc449a490bcf2ad20d19d8c7cf49aaf43c0e99f983ea030f4d016667318de", "signature": "70c225304a3e295fc5f3a12696e35a9088179252438cfe0bba0df41ce4c82b15"}, {"version": "262671818d41386f1ceb990bc4fbb5d34bce7d37514c7702268a3a02e191398c", "signature": "98604a7b2d61fb13fc636f9bd4440dbec7b635bf4c0b0179fe29a353f1c41b94"}, {"version": "9b1f6ed99f4d40ac24e529e16d25f84d848d3e6a2ac6631434f54fc1465049a6", "signature": "7bfa1bc25d6d19426c56f5fd0b448ae007cf94d570bd8524ceb9c0170ecbbf21"}, {"version": "efb903cbcbe8604ea6d13f2233d1f6dcb20f20f5deebda2594af62423f2a5fac", "signature": "aef77375bac953388684d4d36d94932f68311ec0c5d33e068aa3dfadbe87c7fa"}, {"version": "0a7402b2a95ae5064616b6211f2b37b5f16a215ede87f97c69a048a210865334", "signature": "0846db15e4df2e4ae51c197f90d18d3c7d187f6452a2cae0e2b7dd01190b698b"}, {"version": "71511cfb3032991d4cd604bfe72f3b8beda6ed9e2dbd7e0bad647cf5317ed856", "signature": "c5065b45bb379eb29c1a4d26c1172488dfbc19932151196fd90eec5e61647044"}, {"version": "03cc825b05408e1fda8cda582d6bec45f28e712043e2db7fd52787ed2a6becce", "signature": "756ab4441a15c3afb0f10ceb0cb3b422eb3b3bdaf11feea14a80fab24d1361c0"}, {"version": "a3948f74fa37d7397ce11acbd4683eb75e79ba91667a03705d0e62d1d6c97e4e", "signature": "09e6c4dd1ff8d9558cb386121c8e6ad9b53777d303bc21d22b8a6a5b949435b9"}, {"version": "8609c36448e9fcca92b08f352ea9ca3cec4f24c58688c786790a7e27d3e71655", "signature": "083f71b521c5925869102d3141a0c65886a42e3fcca54402f9b607fd94cfc7bb"}, {"version": "18fa011aaa839648d175c1d510633c5b6061612a6132b25737c03b4fcbc454bd", "signature": "89e759e2648e10e5f09b96d2e665a0b3490c7788b002181f75794d52cf8e4580"}, {"version": "a11e8b79e4e324859ca3577f5fd27c3070208ec64f2a06f352e9d828128ac1f7", "signature": "78016a5fb447b3117f15d043304f8db74611ba27df8c43f8a21f6d8b1d5f2774"}, {"version": "c90152ec9ebf7f4598ddc0db88156f5d0dc9722b6b4d2ba38d7647a352c0d798", "signature": "73cd088ba940f039c076b77f4623e30ca9ee03a7e2e0fbc4c8bd73d894bda7bd"}, {"version": "769b93e46e85f088db565672d06e4e21ecdeeeaddabcbdf24c0fad4341afa10d", "signature": "68c6e319d77dfc17a58077b4392cc7dc0d95972eba5a903a38bd671e02cd3db1"}, {"version": "bbe9c53bdfb60d60e0a43e5c27c887906f9dc9db65d00f9548159e132449c08d", "signature": "2c70e919da5b9bdd32ce3e98ad4510b4539896d01186c01da509b6008144581e"}, {"version": "3b5ba57b597593988cacce6fda8d2842d0cc20bb48e5e64d1062b797d4ec7d14", "signature": "e1acbd7cc0050649f7b6ca12fc14b3a6e26b078656634399a0f8512337771cab"}, {"version": "c556b05c64c73b2de82b97e0290f96dbd9577e55b662bf3123aec2fb008f9722", "signature": "364093b4bd3311295fea2542bdda0d86be71de0e800eb172f512e6881ffb811e"}, {"version": "93d8a6a42800e56ecf016080114546e627bbccfe001a406b93829e7bc98c746e", "signature": "e72a801902f892b431e76036e4d8aeaeea85d46bdfe564b440bf040e506ec649"}, {"version": "ecdbc1ff0a6bf41065004c9e977d3b579ea8dadeaa1f7321c665142473fc6ed6", "signature": "2a9c4da9b2d18ab316279b8b0b0a92a3b8d69271921046f863b13515acec0b19"}, {"version": "169ed9b954d51d281449050ddfa19570fd146c85d373443b76e085f758fef01b", "signature": "1385dfc030c52d9880962b35d895cbfe85d74faee541055b1d5d95f87bda44f7"}, {"version": "3e7d58aa47644e6b8716f55dd64a19c1bef0c65971afdc48c9ca6f20b176b943", "signature": "f31f66f5dba4107dc1879eae4d9f467ba761b4a5e89c99a178b92b5069589f76"}, {"version": "80018d66296b9cfc8e885020855dcb2bb22a1ece02b653e997f7946f9808389b", "signature": "582299407aed7241327f72cff215a91a914a8a8f2965decd6f5a9e315c42ff99"}, {"version": "e4a6145d4d3da2bba7504f5855099fda31955db3b91f4d2ee5f489f1dc4fecb8", "signature": "5f2e73c64a9355052107f432a1b790bb8f7a66c8b65215c5e607c86de700b7f1"}, {"version": "1ea76f9ca11a86a1a2d4adfa7753139d70f5bb3be390b3297751bcf22aa21fb7", "signature": "6fe9fd42d86fd49c85e717dafaea3e8ac2a5aee992a3a422cd27c606bb743d40"}, {"version": "3c12545c3495552d7c8d3b65d28b7eb952d787f81402945a83a4c6c7eb8bfc1e", "signature": "9a2373d0a5162a5abbaff359f02a8399ff45fcbae63aacf0a386b942cd363472"}, {"version": "4eb49f78e0f38b229f141fd50fe6d7b66037bc57292eb5904bfbda363f8264ba", "signature": "93eeef004631133d6ccd289df70099fe33f4eaf4c5923e0cdbe1aad789fc8486"}, {"version": "901f8cbe7714844625c3a8dd5ea80f369a7f6217056e271514a7dc9ac4abd6c3", "signature": "44ed844582052d9d07f6c0d36c46b9a9bb4e1fbaa6b9df91d28ff0fd3f6d3b8f"}, {"version": "1e4f1d67be003a7fcd7ff5233b8db813903b2a3bd7c0d64eb1743fe4eca2cd33", "signature": "c61c1f4c5b971501c028e1398a7749e0bf6a1cc4eda9459f033a69b06517176e"}, {"version": "04eb0d46c106586193550266be2153fb8d5a988fdd439c9a3cfd8a8a19a2e5b8", "signature": "975330316fb64cc62d7a7e98732337ab5ea57c06c45f9809b6ed23433507bd85"}, {"version": "7fd6a6bc5906feabfdce83b81269e3d9e6bd44a78e852af19f2c0b3ccbd44e69", "signature": "7861c472817e23e8e8569ef8a49d2891d39c7b1c277f620fba58ca36332de135"}, {"version": "5b04f43f3c465681fde1f787dec39f4ff56dea30519942e345160842ebfb4c8a", "signature": "66ad5d5919ff36b51d9650b2a3c39a16fc63697f3f8fb595fe22dfda2a998f9d"}, {"version": "d01d28b18673372f59b1d54b5eb04d7dc8882d853268b457b3751c340346c0bf", "signature": "90e5fcd8a17d123144e6cf65a93bc2f0c12306572512c4a5e62e8b21af94b2f2"}, {"version": "8e64938da757c1cda6a517989bf8b9e237f66cbe78984750bf4628ca8627e708", "signature": "b03122126494020f4e0194825a105ccdab306cf050eefe0e38d13e89c208defe"}, {"version": "d6a298754fc71ab31f7b85d8b3132c5b1bba42c3aa793b64f5c29c352682a00b", "signature": "c2a5b242c6435b8802dca3e01be30ac087551d3c4d75cc4f15996290c34cd33f"}, "19ce9ec982b542ef6d04d29ce678aad2fa52a67d8087e9c6cd95a4d6d98784c8", "4af47b2f19621ce8f638167a32f141a3a2c0e71ce8ebf51384393ca1c2654e60", "462bccdf75fcafc1ae8c30400c9425e1a4681db5d605d1a0edb4f990a54d8094", "5923d8facbac6ecf7c84739a5c701a57af94a6f6648d6229a6c768cf28f0f8cb", "0beeeb5964025d8564c03cb0bf1a4b60dc40c01f065ad1a4e9030415f5bc7bc2", "370d29706526cf66ee767a4a3ee4218c9544a252ce22f231648414348704cb4c", "6bf53608a27a76ef8580f9577618174f0dd5325142cafb8b3a19aa4850319afb", "821fe27bd167988c3cc518af8d9591ac1bd8d9e9d231ee9eac7b82786dd9f3a6", {"version": "eef204f061321360559bd19235ea32a9d55b3ec22a362cc78d14ef50d4db4490", "affectsGlobalScope": true}, "2ea3c07887a34c982f1d902139569a86bfa4fbf5ab79c3397aec80b2ceb20b05", "73b67d2e87ac7d7baaca64ca33fd1523c0b3c850cb7db5b9c014f1be7996bed1", "5d5ae61fce1581fd6424269790a9071e3f8e69b029f5d0fcb46ce618c5dbc565", "38a0ccc7106312a9f60e034e7cd8ac218774d8aa65f832cee3363a7e65f99325", "850040826cfa77593d44f44487133af21917f4f21507258bd4269501b80d32f0", "8f07f2b6514744ac96e51d7cb8518c0f4de319471237ea10cf688b8d0e9d0225", "bcb6ea18f23dae2c48459d7b86d3adccd6898f824fcbf9da08b935f559896580", "1363ba7d52f2353d0c4306d0ecdaf171bf4509c0148842f9fd8d3986c098a2eb", "3a24f4a428f24cad90b83fab329a620c4adbace083a8eda62c63365065b79e73", "739c2c46edc112421fc023c24b4898b1f413f792bb6a02b40ba182c648e56c2f", "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "33f3718dababfc26dfd9832c150149ea4e934f255130f8c118a59ae69e5ed441", "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "1bb61aa2f08ab4506d41dbe16c5f3f5010f014bbf46fa3d715c0cbe3b00f4e1c", "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "e42820cd611b15910c204cd133f692dcd602532b39317d4f2a19389b27e6f03d", "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "f234315aeb08f02d74769341155afa47ef6ec6873607f55c6a9104d50fc27383", "1c53e1884dc6550ce179c68e9e3086f54af258fff39eb70274ea9294eb7ce6df", "2d57bdaafc7cd0ebc006f0e77c869d6fe6148809c08e8b5677aef4150cf4a7c7", "05c7aef6a4e496b93c2e682cced8903c0dfe6340d04f3fe616176e2782193435", "67856637713bace00feca7f0d4a9907e6a85bcceeb507e07df852cb5f6467834", "e666e31d323fef5642f87db0da48a83e58f0aaf9e3823e87eabd8ec7e0441a36", "69bf2422313487956e4dacf049f30cb91b34968912058d244cb19e4baa24da97", "d12ab69ace581804d4f264eabc71094ca8dadfa70ae2bf5ccd54a8d6105ab84b", "973af20e33ebed2f6c3af36062c214b03daf2a0a3193554f6538ea928228b671", "ca179564af22b92d588ce07d527042767d37bacce79fb78cd6fc7d8ff8c1f329", "e72396ce544667ab49df38ffb91cb3f80ff17d2ad3df903ec30b1d2ca8ea68de", "b7e28e06011460436d5c2ec2996846ac0c451e135357fc5a7269e5665a32fbd7", "d422e50d00562af6bb419fca3a81c8437391acc13f52672dcffdfc3da2a93125", "2887592574fcdfd087647c539dcb0fbe5af2521270dad4a37f9d17c16190d579", "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "632d6fe34a09b481d3007779ad03e4202e4ed4f73bee02fdfb6b23e51ca4aebd", {"version": "7c4d59b36618af873cc489404906c46e2a2f0629a8416ee08b711f5f02096f3f", "affectsGlobalScope": true}, "3fa571018b674c0cdc74584b04f32c421829c409236e1332af4a87ad904b504d", "2d37a18dbc84466a72a4b00d0293ecfe3170fc664ca32126db0b7eace05824d5", "f63e23230f3960b712450cf08f0f31e56acdae3ce65e0bf31bfdd6442b29d115", "f2d1a59a658165341b0e2b7879aa2e19ea6a709146b2d3f70ee8a07159d3d08e", "5309c17206a98ed2bdd130eb25a213e864697f5b017f774141c12030e82db573", "8a3ff3da0cc09f4c5523f6e336635cd0e2cd5cc7e5297186b44b6a6b06e3ef96", {"version": "829325a03054bf6c70506fa5cfcd997944faf73c54c9285d1a2d043d239f4565", "affectsGlobalScope": true}], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "jsx": 4, "module": 99, "noEmitOnError": false, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": false, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": false, "strictNullChecks": false, "target": 7}, "fileIdsList": [[318, 329, 367], [329, 367], [62, 63, 64, 329, 367], [62, 63, 329, 367], [62, 329, 367], [329, 367, 427, 428, 429], [329, 367, 427], [60, 317, 323, 329, 367, 382, 426, 430], [318, 319, 320, 321, 322, 329, 367], [318, 320, 329, 367], [329, 367, 382, 415, 423], [329, 367, 382, 415], [137, 329, 367], [153, 329, 367], [329, 367, 379, 382, 415, 417, 418, 419], [329, 367, 418, 420, 422, 424, 425], [60, 329, 367], [183, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 329, 367], [183, 184, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 329, 367], [184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 329, 367], [183, 184, 185, 187, 188, 189, 190, 191, 192, 193, 194, 195, 329, 367], [183, 184, 185, 186, 188, 189, 190, 191, 192, 193, 194, 195, 329, 367], [183, 184, 185, 186, 187, 189, 190, 191, 192, 193, 194, 195, 329, 367], [183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 329, 367], [183, 184, 185, 186, 187, 188, 189, 191, 192, 193, 194, 195, 329, 367], [183, 184, 185, 186, 187, 188, 189, 190, 192, 193, 194, 195, 329, 367], [183, 184, 185, 186, 187, 188, 189, 190, 191, 193, 194, 195, 329, 367], [183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 194, 195, 329, 367], [183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 195, 329, 367], [183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 329, 367], [57, 58, 59, 329, 367], [329, 367, 380, 397, 415, 416], [329, 367, 382, 415, 417, 421], [58, 60, 125, 329, 367], [329, 367, 595, 599], [329, 367, 595, 596, 597], [329, 367, 596], [329, 367, 595], [329, 367, 595, 596, 633], [329, 367, 630], [329, 367, 634], [329, 367, 601], [329, 367, 594, 601], [329, 367, 594, 601, 602], [329, 367, 649], [329, 367, 640], [329, 367, 647], [329, 367, 632], [329, 367, 591], [329, 367, 409, 591, 592, 594], [316, 329, 367], [329, 367, 625], [329, 367, 623, 625], [329, 367, 614, 622, 623, 624, 626], [329, 367, 612], [329, 367, 615, 620, 625, 628], [329, 367, 611, 628], [329, 367, 615, 616, 619, 620, 621, 628], [329, 367, 615, 616, 617, 619, 620, 628], [329, 367, 612, 613, 614, 615, 616, 620, 621, 622, 624, 625, 626, 628], [329, 367, 610, 612, 613, 614, 615, 616, 617, 619, 620, 621, 622, 623, 624, 625, 626, 627], [329, 367, 610, 628], [329, 367, 615, 617, 618, 620, 621, 628], [329, 367, 619, 628], [329, 367, 620, 621, 625, 628], [329, 367, 613, 623], [329, 367, 593], [65, 69, 329, 367], [60, 65, 69, 70, 329, 367], [65, 66, 67, 68, 329, 367], [60, 65, 66, 329, 367], [60, 65, 329, 367], [60, 140, 156, 159, 170, 171, 329, 367], [60, 140, 149, 157, 170, 171, 329, 367], [60, 139, 140, 329, 367], [60, 140, 329, 367], [60, 140, 170, 171, 329, 367], [60, 140, 170, 171, 177, 179, 182, 329, 367], [60, 140, 149, 156, 159, 170, 171, 329, 367], [60, 140, 149, 157, 169, 170, 171, 329, 367], [60, 140, 149, 159, 169, 170, 171, 329, 367], [60, 140, 149, 169, 170, 171, 329, 367], [60, 140, 144, 150, 156, 161, 170, 171, 180, 181, 329, 367], [140, 329, 367], [60, 140, 195, 198, 199, 200, 329, 367], [60, 140, 195, 197, 198, 199, 329, 367], [60, 140, 157, 329, 367], [60, 140, 149, 329, 367], [60, 140, 141, 142, 329, 367], [60, 140, 142, 144, 329, 367], [135, 136, 140, 141, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 329, 367], [60, 140, 211, 329, 367], [60, 140, 152, 329, 367], [60, 140, 159, 163, 164, 329, 367], [60, 140, 150, 152, 329, 367], [60, 140, 155, 329, 367], [60, 140, 155, 196, 329, 367], [60, 143, 197, 329, 367], [60, 139, 329, 367], [329, 367, 433, 434, 435, 436, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510], [329, 367, 459], [329, 367, 459, 472], [329, 367, 437, 486], [329, 367, 487], [329, 367, 438, 461], [329, 367, 461], [329, 367, 437], [329, 367, 490], [329, 367, 470], [329, 367, 437, 478, 486], [329, 367, 481], [329, 367, 483], [329, 367, 433], [329, 367, 453], [329, 367, 434, 435, 474], [329, 367, 494], [329, 367, 492], [329, 367, 438, 439], [329, 367, 440], [329, 367, 451], [329, 367, 437, 442], [329, 367, 496], [329, 367, 438], [329, 367, 490, 499, 502], [329, 367, 438, 439, 483], [329, 339, 343, 367, 408], [329, 339, 367, 397, 408], [329, 334, 367], [329, 336, 339, 367, 405, 408], [329, 367, 387, 405], [329, 367, 415], [329, 334, 367, 415], [329, 336, 339, 367, 387, 408], [329, 331, 332, 335, 338, 367, 379, 397, 408], [329, 331, 337, 367], [329, 335, 339, 367, 400, 408, 415], [329, 355, 367, 415], [329, 333, 334, 367, 415], [329, 339, 367], [329, 333, 334, 335, 336, 337, 338, 339, 340, 341, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 356, 357, 358, 359, 360, 361, 367], [329, 339, 346, 347, 367], [329, 337, 339, 347, 348, 367], [329, 338, 367], [329, 331, 334, 339, 367], [329, 339, 343, 347, 348, 367], [329, 343, 367], [329, 337, 339, 342, 367, 408], [329, 331, 336, 337, 339, 343, 346, 367], [329, 367, 397], [329, 334, 339, 355, 367, 413, 415], [138, 329, 367], [154, 329, 367], [329, 367, 637, 638], [329, 367, 629, 637, 638, 646], [329, 367, 637], [329, 367, 379, 380, 382, 384, 387, 397, 405, 408, 414, 415, 604, 605, 606, 607, 608, 609, 628], [329, 367, 380, 413, 595, 598, 599, 600, 603, 629, 631, 635, 636, 639, 641, 642, 643, 645, 646], [329, 367, 380, 413, 595, 598, 599, 600, 603, 629, 631, 635, 636, 639, 641, 642, 643, 645, 646, 648, 650, 651], [329, 367, 652], [329, 367, 606], [329, 367, 608], [305, 329, 367], [305, 306, 307, 308, 309, 310, 329, 367], [60, 329, 367, 431, 432, 511], [60, 329, 367, 431], [329, 364, 367], [329, 366, 367], [329, 367, 372, 400], [329, 367, 368, 379, 380, 387, 397, 408], [329, 367, 368, 369, 379, 387], [324, 325, 326, 329, 367], [329, 367, 370, 409], [329, 367, 371, 372, 380, 388], [329, 367, 372, 397, 405], [329, 367, 373, 375, 379, 387], [329, 366, 367, 374], [329, 367, 375, 376], [329, 367, 379], [329, 367, 377, 379], [329, 366, 367, 379], [329, 367, 379, 380, 381, 397, 408], [329, 367, 379, 380, 381, 394, 397, 400], [329, 362, 367, 413], [329, 367, 375, 379, 382, 387, 397, 408], [329, 367, 379, 380, 382, 383, 387, 397, 405, 408], [329, 367, 382, 384, 397, 405, 408], [329, 367, 379, 385], [329, 367, 386, 408, 413], [329, 367, 375, 379, 387, 397], [329, 367, 388], [329, 367, 389], [329, 366, 367, 390], [329, 367, 391, 407, 413], [329, 367, 392], [329, 367, 393], [329, 367, 379, 394, 395], [329, 367, 394, 396, 409, 411], [329, 367, 379, 397, 398, 400], [329, 367, 399, 400], [329, 367, 397, 398], [329, 367, 400], [329, 367, 401], [329, 367, 379, 403, 404], [329, 367, 403, 404], [329, 367, 372, 387, 397, 405], [329, 367, 406], [367], [327, 328, 329, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414], [329, 367, 387, 407], [329, 367, 382, 393, 408], [329, 367, 372, 409], [329, 367, 397, 410], [329, 367, 386, 411], [329, 367, 412], [329, 367, 372, 379, 381, 390, 397, 408, 411, 413], [329, 367, 397, 414], [60, 61, 71, 124, 297, 298, 329, 367], [60, 61, 124, 329, 367], [60, 61, 126, 329, 367], [60, 61, 126, 329, 367, 512, 513], [60, 61, 124, 126, 329, 367], [60, 61, 71, 126, 329, 367], [61, 329, 367], [61, 237, 329, 367], [60, 61, 124, 126, 241, 242, 243, 329, 367, 519, 521], [61, 241, 242, 243, 329, 367, 522], [60, 61, 124, 126, 237, 329, 367], [60, 61, 126, 237, 329, 367], [60, 61, 124, 126, 237, 238, 239, 240, 329, 367], [61, 244, 245, 246, 329, 367], [60, 61, 237, 248, 329, 367], [60, 61, 124, 126, 240, 241, 242, 243, 247, 249, 329, 367], [60, 61, 124, 126, 240, 241, 242, 243, 249, 329, 367], [61, 329, 367, 520], [60, 61, 237, 329, 367, 519], [61, 237, 240, 247, 249, 250, 329, 367, 519, 521, 523], [61, 124, 237, 329, 367], [61, 329, 367, 517, 518], [60, 61, 126, 329, 367, 525, 526, 527, 528], [60, 61, 124, 126, 329, 367, 525, 526, 527, 528], [61, 329, 367, 529], [61, 293, 294, 329, 367, 532, 533, 534], [60, 61, 126, 293, 329, 367], [60, 61, 124, 126, 266, 268, 329, 367], [61, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 329, 367, 539, 540, 541, 542, 545, 546, 547, 548], [60, 61, 124, 268, 272, 273, 274, 275, 276, 329, 367], [60, 61, 124, 329, 367, 537, 538, 539, 540, 541, 542], [60, 61, 124, 266, 329, 367], [60, 61, 124, 126, 268, 269, 270, 271, 277, 329, 367], [60, 61, 124, 329, 367, 537], [60, 61, 124, 266, 267, 329, 367], [61, 124, 329, 367], [60, 61, 124, 266, 329, 367, 537], [61, 124, 266, 268, 271, 272, 273, 274, 275, 276, 279, 329, 367, 538, 539, 540, 541, 542, 543], [61, 124, 266, 329, 367], [60, 61, 268, 278, 329, 367], [60, 61, 124, 126, 268, 271, 272, 273, 274, 275, 276, 329, 367], [61, 235, 261, 262, 264, 290, 329, 367, 553, 554, 555, 565, 566], [60, 61, 124, 126, 290, 329, 367], [60, 61, 126, 282, 329, 367], [61, 283, 284, 285, 286, 287, 288, 289, 291, 329, 367], [60, 61, 126, 290, 329, 367], [61, 124, 220, 222, 229, 230, 231, 232, 233, 234, 329, 367], [60, 61, 71, 124, 126, 329, 367], [60, 61, 124, 126, 219, 221, 329, 367], [60, 61, 124, 126, 219, 329, 367], [60, 61, 124, 126, 219, 221, 228, 329, 367], [61, 124, 253, 254, 263, 329, 367], [60, 61, 124, 126, 254, 262, 329, 367], [61, 124, 256, 257, 258, 259, 260, 329, 367], [60, 61, 124, 126, 262, 329, 367], [60, 61, 126, 219, 225, 329, 367], [61, 280, 281, 329, 367], [60, 61, 126, 219, 225, 280, 329, 367], [60, 61, 126, 329, 367, 557], [61, 329, 367, 556, 558, 559, 560, 561, 562, 563, 564], [60, 61, 124, 126, 329, 367, 565], [60, 61, 126, 329, 367, 565], [60, 61, 71, 124, 126, 131, 219, 255, 261, 329, 367], [61, 219, 329, 367], [61, 124, 221, 223, 224, 226, 227, 329, 367], [60, 61, 124, 219, 329, 367], [60, 61, 71, 124, 221, 223, 224, 226, 329, 367], [60, 61, 123, 219, 329, 367], [60, 61, 71, 124, 219, 225, 329, 367], [61, 124, 265, 292, 329, 367], [60, 61, 71, 124, 126, 227, 235, 281, 291, 329, 367], [60, 61, 124, 126, 251, 252, 264, 329, 367], [61, 329, 367, 569, 570, 571, 572, 573, 575], [60, 61, 126, 132, 329, 367], [60, 61, 126, 132, 215, 329, 367], [60, 61, 124, 126, 132, 329, 367], [60, 61, 124, 126, 134, 216, 217, 218, 329, 367, 571, 573, 574], [60, 61, 124, 329, 367, 569, 570], [60, 61, 124, 131, 132, 329, 367], [61, 124, 132, 133, 134, 216, 217, 218, 236, 329, 367], [60, 61, 124, 126, 133, 134, 216, 217, 218, 235, 329, 367], [60, 61, 124, 126, 133, 329, 367], [60, 61, 329, 367, 575], [60, 61, 126, 236, 329, 367, 575], [60, 61, 299, 303, 304, 312, 329, 367], [61, 127, 128, 129, 329, 367], [60, 61, 71, 126, 127, 128, 329, 367], [60, 61, 329, 367], [60, 61, 329, 367, 578], [60, 61, 71, 315, 329, 367, 544], [60, 61, 71, 292, 329, 367], [61, 311, 329, 367], [61, 296, 329, 367], [60, 61, 71, 129, 130, 236, 250, 265, 279, 292, 294, 295, 329, 367], [60, 61, 301, 303, 304, 329, 367], [72, 76, 329, 367], [78, 79, 80, 81, 82, 83, 84, 329, 367], [85, 97, 99, 101, 329, 367], [86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 329, 367], [60, 72, 93, 329, 367], [60, 72, 329, 367], [60, 88, 329, 367], [60, 86, 329, 367], [98, 329, 367], [100, 329, 367], [74, 329, 367], [103, 104, 105, 106, 107, 329, 367], [73, 75, 77, 102, 108, 115, 119, 120, 121, 123, 329, 367], [76, 118, 122, 329, 367], [116, 329, 367], [72, 329, 367], [72, 73, 329, 367], [116, 117, 118, 329, 367], [109, 329, 367], [109, 110, 111, 112, 113, 114, 329, 367], [60, 109, 329, 367], [654], [62, 63, 64, 654], [62, 63, 654], [62, 654], [655, 656], [137, 655, 656], [153, 655, 656], [60, 655, 656], [183, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 655, 656], [183, 184, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 655, 656], [184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 655, 656], [183, 184, 185, 187, 188, 189, 190, 191, 192, 193, 194, 195, 655, 656], [183, 184, 185, 186, 188, 189, 190, 191, 192, 193, 194, 195, 655, 656], [183, 184, 185, 186, 187, 189, 190, 191, 192, 193, 194, 195, 655, 656], [183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 655, 656], [183, 184, 185, 186, 187, 188, 189, 191, 192, 193, 194, 195, 655, 656], [183, 184, 185, 186, 187, 188, 189, 190, 192, 193, 194, 195, 655, 656], [183, 184, 185, 186, 187, 188, 189, 190, 191, 193, 194, 195, 655, 656], [183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 194, 195, 655, 656], [183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 195, 655, 656], [183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 655, 656], [57, 58, 59, 655, 656], [58, 60, 125, 655, 656], [595, 599, 655, 656], [595, 596, 597, 655, 656], [596, 655, 656], [595, 655, 656], [595, 596, 633, 655, 656], [630, 655, 656], [634, 655, 656], [601, 655, 656], [594, 601, 655, 656], [594, 601, 602, 655, 656], [649, 655, 656], [640, 655, 656], [647, 655, 656], [632, 655, 656], [591, 655, 656], [591, 592, 594, 654, 655, 656], [625, 655, 656], [623, 625, 655, 656], [614, 622, 623, 624, 626, 655, 656], [612, 655, 656], [615, 620, 625, 628, 655, 656], [611, 628, 655, 656], [615, 616, 619, 620, 621, 628, 655, 656], [615, 616, 617, 619, 620, 628, 655, 656], [612, 613, 614, 615, 616, 620, 621, 622, 624, 625, 626, 628, 655, 656], [610, 612, 613, 614, 615, 616, 617, 619, 620, 621, 622, 623, 624, 625, 626, 627, 655, 656], [610, 628, 655, 656], [615, 617, 618, 620, 621, 628, 655, 656], [619, 628, 655, 656], [620, 621, 625, 628, 655, 656], [613, 623, 655, 656], [593, 655, 656], [65, 69, 654], [60, 65, 69, 70, 654], [65, 66, 67, 68, 654], [60, 65, 66, 654], [60, 65, 654], [138, 655, 656], [154, 655, 656], [637, 638, 655, 656], [637, 638, 646, 655, 656, 657], [637, 655, 656], [604, 605, 606, 607, 608, 609, 628, 654, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667], [595, 598, 599, 600, 603, 631, 635, 636, 639, 641, 642, 643, 645, 646, 655, 656, 657, 659, 668], [595, 598, 599, 600, 603, 631, 635, 636, 639, 641, 642, 643, 645, 646, 648, 650, 651, 655, 656, 657, 659, 668], [652, 655, 656], [606, 654], [608, 654], [305, 655, 656], [305, 306, 307, 308, 309, 310, 655, 656], [60], [512, 513], [237], [241, 242, 243, 522], [60, 237], [244, 245, 246], [520], [237, 240, 247, 249, 250, 519, 521, 523], [117, 237], [60, 124, 237], [517, 518], [60, 124], [124], [529], [293, 294, 532, 533, 534], [269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 539, 540, 541, 542, 545, 546, 547, 548], [60, 266], [60, 537], [60, 117, 124], [124, 266, 537], [266, 268, 271, 272, 273, 274, 275, 276, 279, 538, 539, 540, 541, 542, 543], [266], [235, 261, 262, 264, 290, 553, 554, 555, 565, 566], [283, 284, 285, 286, 287, 288, 289, 291], [220, 222, 229, 230, 231, 232, 233, 234], [60, 219, 221], [60, 219], [253, 254, 263], [256, 257, 258, 259, 260], [60, 262], [60, 124, 262], [60, 219, 225], [280, 281], [556, 558, 559, 560, 561, 562, 563, 564], [219], [221, 223, 224, 226, 227], [60, 124, 221], [124, 219], [265, 292], [569, 570, 571, 572, 573, 575], [60, 132], [60, 124, 569], [132], [124, 132], [132, 133, 134, 216, 217, 218, 236], [303], [127, 128, 129], [311], [296]], "referencedMap": [[320, 1], [318, 2], [62, 2], [65, 3], [64, 4], [63, 5], [430, 6], [427, 2], [428, 7], [429, 7], [431, 8], [323, 9], [319, 1], [321, 10], [322, 1], [424, 11], [599, 2], [423, 12], [153, 2], [138, 13], [154, 14], [137, 2], [420, 15], [426, 16], [425, 15], [125, 17], [421, 2], [184, 18], [185, 19], [183, 20], [186, 21], [187, 22], [188, 23], [189, 24], [190, 25], [191, 26], [192, 27], [193, 28], [194, 29], [195, 30], [416, 2], [59, 2], [418, 2], [419, 2], [304, 17], [57, 2], [60, 31], [61, 17], [417, 32], [422, 33], [126, 34], [600, 35], [598, 36], [597, 37], [596, 38], [630, 36], [634, 39], [631, 40], [635, 41], [601, 2], [649, 42], [602, 43], [603, 44], [640, 44], [650, 45], [641, 46], [648, 47], [633, 48], [632, 2], [592, 49], [595, 50], [591, 2], [330, 2], [58, 2], [604, 2], [316, 2], [317, 51], [626, 52], [624, 53], [625, 54], [613, 55], [614, 53], [621, 56], [612, 57], [617, 58], [627, 2], [618, 59], [623, 60], [628, 61], [611, 62], [619, 63], [620, 64], [615, 65], [622, 52], [616, 66], [594, 67], [593, 2], [70, 68], [71, 69], [69, 70], [67, 71], [66, 72], [68, 71], [178, 73], [179, 74], [168, 75], [175, 76], [176, 77], [181, 78], [177, 79], [174, 80], [173, 81], [172, 82], [182, 83], [170, 76], [171, 76], [180, 76], [198, 84], [208, 85], [202, 85], [210, 85], [213, 85], [200, 86], [201, 85], [203, 85], [206, 85], [209, 85], [205, 87], [207, 85], [204, 76], [147, 17], [151, 17], [141, 76], [144, 17], [149, 76], [150, 88], [143, 89], [146, 17], [148, 17], [145, 90], [136, 17], [135, 17], [215, 91], [212, 92], [165, 93], [164, 76], [162, 17], [163, 76], [166, 94], [167, 95], [160, 17], [156, 96], [159, 76], [158, 76], [157, 76], [152, 76], [161, 96], [211, 76], [197, 97], [199, 84], [196, 98], [214, 2], [169, 2], [142, 2], [140, 99], [610, 2], [643, 2], [636, 2], [647, 2], [511, 100], [460, 101], [473, 102], [435, 2], [487, 103], [489, 104], [488, 104], [462, 105], [461, 2], [463, 106], [490, 107], [494, 108], [492, 108], [471, 109], [470, 2], [479, 107], [438, 107], [466, 2], [507, 110], [482, 111], [484, 112], [502, 107], [437, 113], [454, 114], [469, 2], [504, 2], [475, 115], [491, 108], [495, 116], [493, 117], [508, 2], [477, 2], [451, 113], [443, 2], [442, 118], [467, 107], [468, 107], [441, 119], [474, 2], [436, 2], [453, 2], [481, 2], [509, 120], [448, 107], [449, 121], [496, 104], [498, 122], [497, 122], [433, 2], [452, 2], [459, 2], [450, 107], [480, 2], [447, 2], [506, 2], [446, 2], [444, 123], [445, 2], [483, 2], [476, 2], [503, 124], [457, 118], [455, 118], [456, 118], [472, 2], [439, 2], [499, 108], [501, 116], [500, 117], [486, 2], [485, 125], [478, 2], [465, 2], [505, 2], [510, 2], [434, 2], [464, 2], [458, 2], [440, 118], [11, 2], [12, 2], [14, 2], [13, 2], [2, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [20, 2], [21, 2], [22, 2], [3, 2], [4, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [8, 2], [49, 2], [46, 2], [47, 2], [48, 2], [50, 2], [9, 2], [51, 2], [52, 2], [53, 2], [54, 2], [55, 2], [1, 2], [10, 2], [56, 2], [346, 126], [353, 127], [345, 126], [360, 128], [337, 129], [336, 130], [359, 131], [354, 132], [357, 133], [339, 134], [338, 135], [334, 136], [333, 131], [356, 137], [335, 138], [340, 139], [341, 2], [344, 139], [331, 2], [362, 140], [361, 139], [348, 141], [349, 142], [351, 143], [347, 144], [350, 145], [355, 131], [342, 146], [343, 147], [352, 148], [332, 149], [358, 150], [139, 151], [155, 152], [639, 153], [642, 153], [645, 154], [638, 155], [637, 2], [644, 156], [651, 157], [652, 158], [646, 157], [653, 159], [608, 2], [629, 156], [607, 160], [606, 2], [609, 2], [605, 161], [306, 162], [307, 162], [308, 162], [309, 162], [310, 162], [311, 163], [305, 2], [512, 164], [432, 165], [364, 166], [365, 166], [366, 167], [367, 168], [368, 169], [369, 170], [324, 2], [327, 171], [325, 2], [326, 2], [370, 172], [371, 173], [372, 174], [373, 175], [374, 176], [375, 177], [376, 177], [378, 178], [377, 179], [379, 180], [380, 181], [381, 182], [363, 183], [382, 184], [383, 185], [384, 186], [385, 187], [386, 188], [387, 189], [388, 190], [389, 191], [390, 192], [391, 193], [392, 194], [393, 195], [394, 196], [395, 196], [396, 197], [397, 198], [399, 199], [398, 200], [400, 201], [401, 202], [402, 149], [403, 203], [404, 204], [405, 205], [406, 206], [329, 207], [328, 2], [415, 208], [407, 209], [408, 210], [409, 211], [410, 212], [411, 213], [412, 214], [413, 215], [414, 216], [299, 217], [298, 218], [315, 218], [130, 219], [514, 220], [513, 221], [515, 219], [295, 222], [303, 223], [248, 224], [522, 225], [523, 226], [243, 227], [239, 228], [240, 227], [241, 229], [238, 227], [242, 227], [247, 230], [246, 228], [244, 219], [245, 228], [249, 231], [250, 232], [516, 233], [521, 234], [520, 235], [524, 236], [518, 237], [517, 237], [519, 238], [237, 223], [525, 219], [526, 219], [527, 221], [529, 239], [530, 240], [528, 218], [531, 241], [533, 219], [532, 219], [534, 219], [293, 218], [535, 242], [294, 243], [269, 221], [270, 219], [274, 244], [545, 244], [546, 221], [547, 221], [271, 244], [549, 245], [548, 221], [272, 244], [277, 246], [275, 244], [543, 247], [542, 248], [278, 249], [539, 218], [541, 248], [540, 250], [276, 244], [273, 244], [268, 251], [537, 252], [538, 253], [544, 254], [267, 255], [279, 256], [536, 257], [266, 252], [550, 252], [551, 221], [567, 258], [553, 219], [554, 219], [555, 219], [289, 259], [286, 260], [287, 260], [288, 260], [285, 260], [284, 260], [283, 260], [290, 261], [291, 262], [235, 263], [232, 264], [222, 265], [220, 266], [234, 221], [233, 221], [230, 265], [231, 267], [229, 267], [264, 268], [263, 269], [254, 266], [253, 264], [261, 270], [259, 264], [258, 264], [256, 271], [260, 221], [257, 271], [280, 272], [566, 273], [281, 274], [562, 275], [561, 275], [565, 276], [560, 275], [563, 275], [558, 275], [559, 275], [564, 277], [556, 278], [262, 279], [282, 280], [225, 280], [557, 280], [228, 281], [224, 282], [252, 282], [227, 283], [223, 284], [251, 284], [255, 282], [226, 285], [221, 282], [552, 286], [292, 287], [265, 288], [219, 252], [569, 219], [572, 221], [574, 289], [134, 290], [216, 291], [573, 221], [217, 221], [218, 292], [575, 293], [571, 294], [133, 295], [570, 295], [578, 296], [236, 297], [568, 298], [576, 299], [577, 300], [132, 252], [579, 252], [313, 301], [128, 219], [580, 302], [129, 303], [127, 222], [300, 304], [581, 219], [582, 305], [583, 222], [584, 219], [585, 306], [586, 307], [587, 219], [312, 308], [588, 219], [297, 309], [589, 222], [296, 310], [590, 252], [131, 252], [314, 311], [301, 304], [302, 304], [77, 312], [78, 17], [79, 17], [85, 313], [80, 17], [81, 17], [82, 17], [83, 17], [84, 17], [102, 314], [86, 17], [87, 17], [88, 17], [90, 17], [97, 315], [91, 17], [92, 17], [94, 316], [93, 317], [96, 317], [95, 316], [89, 318], [98, 319], [99, 320], [100, 17], [101, 321], [75, 322], [74, 2], [108, 323], [103, 2], [104, 2], [105, 2], [106, 2], [107, 2], [124, 324], [121, 2], [123, 325], [118, 326], [76, 327], [122, 328], [117, 2], [116, 17], [119, 329], [113, 330], [111, 330], [115, 331], [112, 330], [114, 332], [110, 2], [109, 2], [73, 327], [72, 2], [120, 2]], "exportedModulesMap": [[320, 1], [318, 2], [62, 333], [65, 334], [64, 335], [63, 336], [430, 6], [427, 2], [428, 7], [429, 7], [431, 8], [323, 9], [319, 1], [321, 10], [322, 1], [424, 11], [599, 337], [423, 12], [153, 337], [138, 338], [154, 339], [137, 337], [420, 15], [426, 16], [425, 15], [125, 340], [421, 2], [184, 341], [185, 342], [183, 343], [186, 344], [187, 345], [188, 346], [189, 347], [190, 348], [191, 349], [192, 350], [193, 351], [194, 352], [195, 353], [416, 2], [59, 337], [418, 2], [419, 2], [304, 340], [57, 337], [60, 354], [61, 340], [417, 32], [422, 33], [126, 355], [600, 356], [598, 357], [597, 358], [596, 359], [630, 357], [634, 360], [631, 361], [635, 362], [601, 337], [649, 363], [602, 364], [603, 365], [640, 365], [650, 366], [641, 367], [648, 368], [633, 369], [632, 337], [592, 370], [595, 371], [591, 337], [330, 2], [58, 337], [604, 337], [316, 2], [317, 51], [626, 372], [624, 373], [625, 374], [613, 375], [614, 373], [621, 376], [612, 377], [617, 378], [627, 337], [618, 379], [623, 380], [628, 381], [611, 382], [619, 383], [620, 384], [615, 385], [622, 372], [616, 386], [594, 387], [593, 337], [70, 388], [71, 389], [69, 390], [67, 391], [66, 392], [68, 391], [178, 73], [179, 74], [168, 75], [175, 76], [176, 77], [181, 78], [177, 79], [174, 80], [173, 81], [172, 82], [182, 83], [170, 76], [171, 76], [180, 76], [198, 84], [208, 85], [202, 85], [210, 85], [213, 85], [200, 86], [201, 85], [203, 85], [206, 85], [209, 85], [205, 87], [207, 85], [204, 76], [147, 17], [151, 17], [141, 76], [144, 17], [149, 76], [150, 88], [143, 89], [146, 17], [148, 17], [145, 90], [136, 17], [135, 17], [215, 91], [212, 92], [165, 93], [164, 76], [162, 17], [163, 76], [166, 94], [167, 95], [160, 17], [156, 96], [159, 76], [158, 76], [157, 76], [152, 76], [161, 96], [211, 76], [197, 97], [199, 84], [196, 98], [214, 2], [169, 2], [142, 2], [140, 99], [610, 337], [643, 337], [636, 337], [647, 337], [511, 100], [460, 101], [473, 102], [435, 2], [487, 103], [489, 104], [488, 104], [462, 105], [461, 2], [463, 106], [490, 107], [494, 108], [492, 108], [471, 109], [470, 2], [479, 107], [438, 107], [466, 2], [507, 110], [482, 111], [484, 112], [502, 107], [437, 113], [454, 114], [469, 2], [504, 2], [475, 115], [491, 108], [495, 116], [493, 117], [508, 2], [477, 2], [451, 113], [443, 2], [442, 118], [467, 107], [468, 107], [441, 119], [474, 2], [436, 2], [453, 2], [481, 2], [509, 120], [448, 107], [449, 121], [496, 104], [498, 122], [497, 122], [433, 2], [452, 2], [459, 2], [450, 107], [480, 2], [447, 2], [506, 2], [446, 2], [444, 123], [445, 2], [483, 2], [476, 2], [503, 124], [457, 118], [455, 118], [456, 118], [472, 2], [439, 2], [499, 108], [501, 116], [500, 117], [486, 2], [485, 125], [478, 2], [465, 2], [505, 2], [510, 2], [434, 2], [464, 2], [458, 2], [440, 118], [11, 2], [12, 2], [14, 2], [13, 2], [2, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [20, 2], [21, 2], [22, 2], [3, 2], [4, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [8, 2], [49, 2], [46, 2], [47, 2], [48, 2], [50, 2], [9, 2], [51, 2], [52, 2], [53, 2], [54, 2], [55, 2], [1, 2], [10, 2], [56, 2], [346, 126], [353, 127], [345, 126], [360, 128], [337, 129], [336, 130], [359, 131], [354, 132], [357, 133], [339, 134], [338, 135], [334, 136], [333, 131], [356, 137], [335, 138], [340, 139], [341, 2], [344, 139], [331, 2], [362, 140], [361, 139], [348, 141], [349, 142], [351, 143], [347, 144], [350, 145], [355, 131], [342, 146], [343, 147], [352, 148], [332, 149], [358, 150], [139, 393], [155, 394], [639, 395], [642, 395], [645, 396], [638, 397], [637, 337], [644, 398], [651, 399], [652, 400], [646, 399], [653, 401], [608, 333], [629, 398], [607, 402], [606, 333], [609, 333], [605, 403], [306, 404], [307, 404], [308, 404], [309, 404], [310, 404], [311, 405], [305, 337], [512, 164], [432, 165], [364, 166], [365, 166], [366, 167], [367, 168], [368, 169], [369, 170], [324, 2], [327, 171], [325, 2], [326, 2], [370, 172], [371, 173], [372, 174], [373, 175], [374, 176], [375, 177], [376, 177], [378, 178], [377, 179], [379, 180], [380, 181], [381, 182], [363, 183], [382, 184], [383, 185], [384, 186], [385, 187], [386, 188], [387, 189], [388, 190], [389, 191], [390, 192], [391, 193], [392, 194], [393, 195], [394, 196], [395, 196], [396, 197], [397, 198], [399, 199], [398, 200], [400, 201], [401, 202], [402, 149], [403, 203], [404, 204], [405, 205], [406, 206], [329, 207], [328, 2], [415, 208], [407, 209], [408, 210], [409, 211], [410, 212], [411, 213], [412, 214], [413, 215], [414, 216], [298, 406], [315, 406], [130, 406], [514, 407], [513, 406], [515, 406], [295, 406], [248, 408], [522, 406], [523, 409], [243, 410], [239, 410], [240, 410], [241, 410], [238, 410], [242, 410], [247, 411], [246, 410], [244, 406], [245, 410], [249, 410], [250, 406], [516, 406], [521, 412], [520, 408], [524, 413], [518, 414], [517, 415], [519, 416], [525, 406], [526, 406], [527, 417], [529, 406], [530, 406], [528, 418], [531, 419], [533, 406], [532, 406], [534, 406], [535, 420], [294, 406], [269, 406], [270, 406], [274, 406], [545, 406], [546, 406], [547, 406], [271, 406], [549, 421], [548, 417], [272, 406], [277, 406], [275, 406], [543, 406], [542, 422], [278, 406], [539, 406], [541, 422], [540, 423], [276, 406], [273, 406], [268, 422], [537, 424], [538, 425], [544, 426], [267, 427], [279, 406], [536, 406], [266, 418], [550, 418], [551, 417], [567, 428], [553, 406], [554, 406], [555, 406], [289, 406], [286, 406], [287, 406], [288, 406], [285, 406], [284, 406], [283, 406], [290, 429], [291, 406], [235, 430], [232, 406], [222, 431], [220, 432], [234, 406], [233, 406], [230, 431], [231, 431], [229, 431], [264, 433], [263, 417], [254, 432], [253, 406], [261, 434], [259, 406], [258, 417], [256, 435], [260, 406], [257, 436], [280, 437], [566, 438], [281, 406], [562, 406], [561, 406], [565, 439], [560, 406], [563, 406], [558, 406], [559, 406], [564, 406], [556, 406], [262, 432], [225, 440], [228, 441], [224, 440], [252, 432], [227, 442], [223, 432], [251, 440], [255, 440], [226, 443], [221, 432], [552, 444], [292, 406], [265, 406], [219, 418], [569, 406], [572, 406], [574, 445], [134, 446], [216, 446], [573, 417], [217, 417], [218, 446], [575, 406], [571, 447], [133, 448], [570, 449], [578, 450], [236, 406], [568, 406], [576, 406], [577, 406], [132, 418], [579, 418], [313, 451], [128, 406], [580, 452], [129, 406], [127, 406], [300, 406], [581, 406], [582, 406], [583, 406], [584, 406], [585, 406], [586, 406], [587, 406], [312, 453], [588, 406], [297, 454], [589, 406], [296, 406], [590, 418], [131, 418], [314, 451], [301, 406], [302, 406], [77, 312], [78, 17], [79, 17], [85, 313], [80, 17], [81, 17], [82, 17], [83, 17], [84, 17], [102, 314], [86, 17], [87, 17], [88, 17], [90, 17], [97, 315], [91, 17], [92, 17], [94, 316], [93, 317], [96, 317], [95, 316], [89, 318], [98, 319], [99, 320], [100, 17], [101, 321], [75, 322], [74, 2], [108, 323], [103, 2], [104, 2], [105, 2], [106, 2], [107, 2], [124, 324], [121, 2], [123, 325], [118, 326], [76, 327], [122, 328], [117, 2], [116, 17], [119, 329], [113, 330], [111, 330], [115, 331], [112, 330], [114, 332], [110, 2], [109, 2], [73, 327], [72, 2], [120, 2]], "semanticDiagnosticsPerFile": [320, 318, 62, 65, 64, 63, 430, 427, 428, 429, 431, 323, 319, 321, 322, 424, 599, 423, 153, 138, 154, 137, 420, 426, 425, 125, 421, 184, 185, 183, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 416, 59, 418, 419, 304, 57, 60, 61, 417, 422, 126, 600, 598, 597, 596, 630, 634, 631, 635, 601, 649, 602, 603, 640, 650, 641, 648, 633, 632, 592, 595, 591, 330, 58, 604, 316, 317, 626, 624, 625, 613, 614, 621, 612, 617, 627, 618, 623, 628, 611, 619, 620, 615, 622, 616, 594, 593, 70, 71, 69, 67, 66, 68, 178, 179, 168, 175, 176, 181, 177, 174, 173, 172, 182, 170, 171, 180, 198, 208, 202, 210, 213, 200, 201, 203, 206, 209, 205, 207, 204, 147, 151, 141, 144, 149, 150, 143, 146, 148, 145, 136, 135, 215, 212, 165, 164, 162, 163, 166, 167, 160, 156, 159, 158, 157, 152, 161, 211, 197, 199, 196, 214, 169, 142, 140, 610, 643, 636, 647, 511, 460, 473, 435, 487, 489, 488, 462, 461, 463, 490, 494, 492, 471, 470, 479, 438, 466, 507, 482, 484, 502, 437, 454, 469, 504, 475, 491, 495, 493, 508, 477, 451, 443, 442, 467, 468, 441, 474, 436, 453, 481, 509, 448, 449, 496, 498, 497, 433, 452, 459, 450, 480, 447, 506, 446, 444, 445, 483, 476, 503, 457, 455, 456, 472, 439, 499, 501, 500, 486, 485, 478, 465, 505, 510, 434, 464, 458, 440, 11, 12, 14, 13, 2, 15, 16, 17, 18, 19, 20, 21, 22, 3, 4, 26, 23, 24, 25, 27, 28, 29, 5, 30, 31, 32, 33, 6, 37, 34, 35, 36, 38, 7, 39, 44, 45, 40, 41, 42, 43, 8, 49, 46, 47, 48, 50, 9, 51, 52, 53, 54, 55, 1, 10, 56, 346, 353, 345, 360, 337, 336, 359, 354, 357, 339, 338, 334, 333, 356, 335, 340, 341, 344, 331, 362, 361, 348, 349, 351, 347, 350, 355, 342, 343, 352, 332, 358, 139, 155, 639, 642, 645, 638, 637, 644, 651, 652, 646, 653, 608, 629, 607, 606, 609, 605, 306, 307, 308, 309, 310, 311, 305, 512, 432, 364, 365, 366, 367, 368, 369, 324, 327, 325, 326, 370, 371, 372, 373, 374, 375, 376, 378, 377, 379, 380, 381, 363, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 399, 398, 400, 401, 402, 403, 404, 405, 406, 329, 328, 415, 407, 408, 409, 410, 411, 412, 413, 414, [299, [{"file": "./src/app.tsx", "start": 0, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 298, 315, 130, [514, [{"file": "./src/components/molecules/profitlosscell.stories.tsx", "start": 289, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [513, [{"file": "./src/components/molecules/profitlosscell.tsx", "start": 265, "length": 210, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 276, "length": 23, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'useProfitLossFormatting'.", "category": 1, "code": 2305}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 303, "length": 11, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'LoadingCell'.", "category": 1, "code": 2305}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 318, "length": 20, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'profitLossBaseStyles'.", "category": 1, "code": 2305}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 342, "length": 17, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'getProfitLossSize'.", "category": 1, "code": 2305}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 363, "length": 19, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'getProfitLossColors'.", "category": 1, "code": 2305}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 386, "length": 20, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'getProfitLossVariant'.", "category": 1, "code": 2305}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 410, "length": 23, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'profitLossLoadingStyles'.", "category": 1, "code": 2305}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 1589, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 1776, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 1958, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 2208, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 2582, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 2947, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 3315, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/profitlosscell.tsx", "start": 3509, "length": 3, "messageText": "Cannot find name 'css'. Did you mean 'CSS'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 766759, "length": 3, "messageText": "'CSS' is declared here.", "category": 3, "code": 2728}]}]], 515, 295, 303, [248, [{"file": "./src/features/daily-guide/api/dailyguideapi.ts", "start": 4763, "length": 11, "code": 2739, "category": 1, "messageText": "Type '{ id: string; description: string; priority: TradingPlanPriority; completed: boolean; }[]' is missing the following properties from type 'TradingPlan': items, strategy, riskManagement, notes", "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3655, "length": 11, "messageText": "The expected type comes from property 'tradingPlan' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}]], 522, 523, [243, [{"file": "./src/features/daily-guide/components/keylevels.tsx", "start": 4071, "length": 16, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ label: string; onClick: () => void; icon: string; }[]' is not assignable to type 'ReactNode'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ label: string; onClick: () => void; icon: string; }[]' is not assignable to type 'ReactFragment'.", "category": 1, "code": 2322, "next": [{"messageText": "The types returned by '[Symbol.iterator]().next(...)' are incompatible between these types.", "category": 1, "code": 2201, "next": [{"messageText": "Type 'IteratorResult<{ label: string; onClick: () => void; icon: string; }, any>' is not assignable to type 'IteratorResult<ReactNode, any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'IteratorYieldResult<{ label: string; onClick: () => void; icon: string; }>' is not assignable to type 'IteratorResult<ReactNode, any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'IteratorYieldResult<{ label: string; onClick: () => void; icon: string; }>' is not assignable to type 'IteratorYieldResult<ReactNode>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ label: string; onClick: () => void; icon: string; }' is not assignable to type 'ReactNode'.", "category": 1, "code": 2322, "next": [{"messageText": "Object literal may only specify known properties, and 'label' does not exist in type 'ReactElement<any, string | JSXElementConstructor<any>> | ReactFragment | ReactPortal'.", "category": 1, "code": 2353}]}]}]}]}]}]}]}, "relatedInformation": [{"file": "../shared/dist/components/molecules/card.d.ts", "start": 793, "length": 7, "messageText": "The expected type comes from property 'actions' which is declared here on type 'IntrinsicAttributes & CardProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/components/keylevels.tsx", "start": 4379, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ children: (string | number)[]; variant: \"primary\"; style: { marginLeft: string; }; }' is not assignable to type 'IntrinsicAttributes & BadgeProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'style' does not exist on type 'IntrinsicAttributes & BadgeProps'.", "category": 1, "code": 2339}]}}]], 239, 240, [241, [{"file": "./src/features/daily-guide/components/marketoverview.tsx", "start": 2808, "length": 16, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ label: string; onClick: () => void; icon: string; }[]' is not assignable to type 'ReactNode'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ label: string; onClick: () => void; icon: string; }[]' is not assignable to type 'ReactFragment'.", "category": 1, "code": 2322, "next": [{"messageText": "The types returned by '[Symbol.iterator]().next(...)' are incompatible between these types.", "category": 1, "code": 2201, "next": [{"messageText": "Type 'IteratorResult<{ label: string; onClick: () => void; icon: string; }, any>' is not assignable to type 'IteratorResult<ReactNode, any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'IteratorYieldResult<{ label: string; onClick: () => void; icon: string; }>' is not assignable to type 'IteratorResult<ReactNode, any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'IteratorYieldResult<{ label: string; onClick: () => void; icon: string; }>' is not assignable to type 'IteratorYieldResult<ReactNode>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ label: string; onClick: () => void; icon: string; }' is not assignable to type 'ReactNode'.", "category": 1, "code": 2322, "next": [{"messageText": "Object literal may only specify known properties, and 'label' does not exist in type 'ReactElement<any, string | JSXElementConstructor<any>> | ReactFragment | ReactPortal'.", "category": 1, "code": 2353}]}]}]}]}]}]}]}, "relatedInformation": [{"file": "../shared/dist/components/molecules/card.d.ts", "start": 793, "length": 7, "messageText": "The expected type comes from property 'actions' which is declared here on type 'IntrinsicAttributes & CardProps'", "category": 3, "code": 6500}]}]], 238, [242, [{"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 6376, "length": 17, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ label: string; onClick: () => void; icon: string; }[]' is not assignable to type 'ReactNode'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ label: string; onClick: () => void; icon: string; }[]' is not assignable to type 'ReactFragment'.", "category": 1, "code": 2322, "next": [{"messageText": "The types returned by '[Symbol.iterator]().next(...)' are incompatible between these types.", "category": 1, "code": 2201, "next": [{"messageText": "Type 'IteratorResult<{ label: string; onClick: () => void; icon: string; }, any>' is not assignable to type 'IteratorResult<ReactNode, any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'IteratorYieldResult<{ label: string; onClick: () => void; icon: string; }>' is not assignable to type 'IteratorResult<ReactNode, any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'IteratorYieldResult<{ label: string; onClick: () => void; icon: string; }>' is not assignable to type 'IteratorYieldResult<ReactNode>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ label: string; onClick: () => void; icon: string; }' is not assignable to type 'ReactNode'.", "category": 1, "code": 2322, "next": [{"messageText": "Object literal may only specify known properties, and 'label' does not exist in type 'ReactElement<any, string | JSXElementConstructor<any>> | ReactFragment | ReactPortal'.", "category": 1, "code": 2353}]}]}]}]}]}]}]}, "relatedInformation": [{"file": "../shared/dist/components/molecules/card.d.ts", "start": 793, "length": 7, "messageText": "The expected type comes from property 'actions' which is declared here on type 'IntrinsicAttributes & CardProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 7665, "length": 7, "code": 2322, "category": 1, "messageText": "Type '\"icon\"' is not assignable to type 'ButtonVariant'.", "relatedInformation": [{"file": "../shared/dist/components/atoms/button.d.ts", "start": 426, "length": 7, "messageText": "The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & ButtonProps'", "category": 3, "code": 6500}]}]], 247, 246, 244, 245, [249, [{"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 225, "length": 16, "messageText": "'\"../types\"' has no exported member named 'DailyGuideAction'. Did you mean 'DailyGuideData'?", "category": 1, "code": 2724}, {"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 555, "length": 16, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ marketData: null; tradingPlan: undefined[]; keyLevels: undefined[]; marketNews: undefined[]; isLoading: false; error: null; currentDate: string; }' is not assignable to type 'DailyGuideState'.", "category": 1, "code": 2322, "next": [{"messageText": "Object literal may only specify known properties, and 'marketData' does not exist in type 'DailyGuideState'.", "category": 1, "code": 2353}]}}, {"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 1423, "length": 152, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ tradingPlan: any; data: DailyGuideData; isLoading: boolean; error: string; selectedDate: string; }' is not assignable to type 'DailyGuideState'.", "category": 1, "code": 2322, "next": [{"messageText": "Object literal may only specify known properties, and 'tradingPlan' does not exist in type 'DailyGuideState'.", "category": 1, "code": 2353}]}}, {"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 1442, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'tradingPlan' does not exist on type 'DailyGuideState'."}]], [250, [{"file": "./src/features/daily-guide/dailyguide.tsx", "start": 1421, "length": 11, "messageText": "Property 'currentDate' does not exist on type 'DailyGuideContextType'.", "category": 1, "code": 2339}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 2137, "length": 14, "code": 2741, "category": 1, "messageText": "Property 'marketOverview' is missing in type '{}' but required in type 'MarketOverviewProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/marketoverview.tsx", "start": 522, "length": 14, "messageText": "'marketOverview' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 2334, "length": 11, "code": 2741, "category": 1, "messageText": "Property 'tradingPlan' is missing in type '{}' but required in type 'TradingPlanProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 432, "length": 11, "messageText": "'tradingPlan' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 2526, "length": 9, "code": 2741, "category": 1, "messageText": "Property 'keyLevels' is missing in type '{}' but required in type 'KeyLevelsProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/keylevels.tsx", "start": 321, "length": 9, "messageText": "'keyLevels' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 2717, "length": 10, "code": 2741, "category": 1, "messageText": "Property 'events' is missing in type '{}' but required in type 'MarketNewsProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/marketnews.tsx", "start": 340, "length": 6, "messageText": "'events' is declared here.", "category": 3, "code": 2728}]}]], [516, [{"file": "./src/features/daily-guide/dailyguidecomposed.tsx", "start": 278, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'DashboardSection'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/dailyguidecomposed.tsx", "start": 1807, "length": 11, "messageText": "Property 'currentDate' does not exist on type 'DailyGuideContextType'.", "category": 1, "code": 2339}, {"file": "./src/features/daily-guide/dailyguidecomposed.tsx", "start": 2790, "length": 14, "code": 2741, "category": 1, "messageText": "Property 'marketOverview' is missing in type '{}' but required in type 'MarketOverviewProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/marketoverview.tsx", "start": 522, "length": 14, "messageText": "'marketOverview' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguidecomposed.tsx", "start": 3089, "length": 9, "code": 2741, "category": 1, "messageText": "Property 'keyLevels' is missing in type '{}' but required in type 'KeyLevelsProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/keylevels.tsx", "start": 321, "length": 9, "messageText": "'keyLevels' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguidecomposed.tsx", "start": 3387, "length": 10, "code": 2741, "category": 1, "messageText": "Property 'events' is missing in type '{}' but required in type 'MarketNewsProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/marketnews.tsx", "start": 340, "length": 6, "messageText": "'events' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguidecomposed.tsx", "start": 3723, "length": 11, "code": 2741, "category": 1, "messageText": "Property 'tradingPlan' is missing in type '{}' but required in type 'TradingPlanProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 432, "length": 11, "messageText": "'tradingPlan' is declared here.", "category": 3, "code": 2728}]}]], 521, [520, [{"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 114, "length": 8, "messageText": "'useState' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5211, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ sentiment: \"neutral\" | \"bullish\" | \"bearish\"; summary: string; indices: { symbol: string; name: string; value: number; change: number; changePercent: number; }[]; economicEvents: ({ title: string; ... 4 more ...; actual?: undefined; } | { ...; })[]; news: { ...; }[]; lastUpdated: string; }' is not assignable to type 'MarketOverview'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'economicEvents' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '({ title: string; time: string; importance: string; expected: string; previous: string; actual?: undefined; } | { title: string; time: string; importance: string; expected: string; previous: string; actual: string; })[]' is not assignable to type 'EconomicEvent[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ title: string; time: string; importance: string; expected: string; previous: string; actual?: undefined; } | { title: string; time: string; importance: string; expected: string; previous: string; actual: string; }' is not assignable to type 'EconomicEvent'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ title: string; time: string; importance: string; expected: string; previous: string; actual?: undefined; }' is not assignable to type 'EconomicEvent'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'importance' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type '\"high\" | \"low\" | \"medium\"'.", "category": 1, "code": 2322}]}]}]}]}]}]}, "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3588, "length": 14, "messageText": "The expected type comes from property 'marketOverview' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5251, "length": 11, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ items: { id: string; description: string; priority: string; completed: boolean; }[]; strategy: string; riskManagement: { maxRiskPerTrade: number; maxDailyLoss: number; maxTrades: number; positionSizing: string; }; notes: string; }' is not assignable to type 'TradingPlan'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'items' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ id: string; description: string; priority: string; completed: boolean; }[]' is not assignable to type 'TradingPlanItem[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; description: string; priority: string; completed: boolean; }' is not assignable to type 'TradingPlanItem'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'priority' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type 'TradingPlanPriority'.", "category": 1, "code": 2322}]}]}]}]}]}, "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3655, "length": 11, "messageText": "The expected type comes from property 'tradingPlan' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5355, "length": 10, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: string; title: string; source: string; timestamp: string; url: string; impact: string; }[]' is not assignable to type 'MarketNewsItem[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; title: string; source: string; timestamp: string; url: string; impact: string; }' is not assignable to type 'MarketNewsItem'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'impact' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type '\"high\" | \"low\" | \"medium\"'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3835, "length": 10, "messageText": "The expected type comes from property 'marketNews' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5603, "length": 9, "messageText": "'state' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [524, [{"file": "./src/features/daily-guide/index.ts", "start": 235, "length": 24, "messageText": "Module './components' has already exported a member named 'MarketOverview'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"file": "./src/features/daily-guide/index.ts", "start": 235, "length": 24, "messageText": "Module './components' has already exported a member named 'TradingPlan'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}]], 518, 517, 519, 237, 525, 526, [527, [{"file": "./src/features/performance-dashboard/components/recenttradespanel.tsx", "start": 176, "length": 18, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 529, [530, [{"file": "./src/features/performance-dashboard/dashboardcomposed.tsx", "start": 293, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'DashboardSection'.", "category": 1, "code": 2305}]], [528, [{"file": "./src/features/performance-dashboard/hooks/usedashboarddata.ts", "start": 166, "length": 18, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/performance-dashboard/hooks/usedashboarddata.ts", "start": 404, "length": 10, "messageText": "'setMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 531, 533, 532, 534, 293, 535, 294, 269, 270, [274, [{"file": "./src/features/trade-analysis/components/categoryperformancechart.tsx", "start": 216, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [545, [{"file": "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "start": 533, "length": 13, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'SortableTable'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "start": 551, "length": 14, "messageText": "'\"@adhd-trading-dashboard/shared\"' has no exported member named 'SortableColumn'. Did you mean 'TableColumn'?", "category": 1, "code": 2724}, {"file": "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "start": 569, "length": 23, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'useProfitLossFormatting'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "start": 569, "length": 23, "messageText": "'useProfitLossFormatting' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "start": 596, "length": 17, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'useDataFormatting'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "start": 617, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'LoadingSpinner'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/categoryperformancechartrefactored.tsx", "start": 4037, "length": 3, "messageText": "'row' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [546, [{"file": "./src/features/trade-analysis/components/distributionchart.tsx", "start": 178, "length": 87, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [547, [{"file": "./src/features/trade-analysis/components/equitycurve.tsx", "start": 157, "length": 87, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], 271, [549, [{"file": "./src/features/trade-analysis/components/index.ts", "start": 893, "length": 12, "messageText": "Module '\"./MetricsPanel\"' has no exported member 'MetricsPanel'. Did you mean to use 'import MetricsPanel from \"./MetricsPanel\"' instead?", "category": 1, "code": 2614}, {"file": "./src/features/trade-analysis/components/index.ts", "start": 940, "length": 17, "messageText": "'\"./DistributionChart\"' has no exported member named 'DistributionChart'. Did you mean 'DistributionBar'?", "category": 1, "code": 2724}, {"file": "./src/features/trade-analysis/components/index.ts", "start": 997, "length": 11, "messageText": "Module '\"./EquityCurve\"' has no exported member 'EquityCurve'. Did you mean to use 'import EquityCurve from \"./EquityCurve\"' instead?", "category": 1, "code": 2614}, {"file": "./src/features/trade-analysis/components/index.ts", "start": 1083, "length": 7, "messageText": "Module '\"./TradeAnalysisCharts\"' has no exported member 'default'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/index.ts", "start": 1155, "length": 7, "messageText": "Module '\"./TradeAnalysisFilter\"' has no exported member 'default'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/index.ts", "start": 1227, "length": 7, "messageText": "Module '\"./TradeAnalysisSummary\"' has no exported member 'default'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/index.ts", "start": 1301, "length": 7, "messageText": "Module '\"./TradeAnalysisTable\"' has no exported member 'default'.", "category": 1, "code": 2305}]], [548, [{"file": "./src/features/trade-analysis/components/metricspanel.tsx", "start": 176, "length": 17, "messageText": "'CompleteTradeData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [272, [{"file": "./src/features/trade-analysis/components/performancesummary.tsx", "start": 165, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/performancesummary.tsx", "start": 236, "length": 46, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 277, [275, [{"file": "./src/features/trade-analysis/components/timeperformancechart.tsx", "start": 182, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/timeperformancechart.tsx", "start": 253, "length": 43, "messageText": "'TimePerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/timeperformancechart.tsx", "start": 2781, "length": 5, "messageText": "'title' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [543, [{"file": "./src/features/trade-analysis/components/tradeanalysis.tsx", "start": 3274, "length": 9, "messageText": "'title' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 542, [278, [{"file": "./src/features/trade-analysis/components/tradeanalysiscontainer.tsx", "start": 479, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'LoadingSpinner'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysiscontainer.tsx", "start": 3370, "length": 11, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"trades\" | \"summary\" | \"charts\"'.", "relatedInformation": [{"file": "./src/features/trade-analysis/types.ts", "start": 3027, "length": 11, "messageText": "The expected type comes from property 'defaultView' which is declared here on type 'Partial<UserPreferences>'", "category": 3, "code": 6500}]}]], [539, [{"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 270, "length": 29, "messageText": "Cannot find module '../state/tradeAnalysisState' or its corresponding type declarations.", "category": 1, "code": 2307}]], 541, [540, [{"file": "./src/features/trade-analysis/components/tradeanalysistable.tsx", "start": 212, "length": 17, "messageText": "Module '\"../hooks/tradeAnalysisState\"' declares 'CompleteTradeData' locally, but it is not exported.", "category": 1, "code": 2459, "relatedInformation": [{"file": "./src/features/trade-analysis/hooks/tradeanalysisstate.ts", "start": 204, "length": 17, "messageText": "'CompleteTradeData' is declared here.", "category": 3, "code": 2728}]}]], [276, [{"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 161, "length": 33, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 4133, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 4656, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 4715, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 4916, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 5088, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'exitTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 5278, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 5303, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'exitTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 5476, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'entryPrice' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 5649, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'exitPrice' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 5819, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'quantity' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 5978, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timeframe' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 6136, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Trade'."}]], [273, [{"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 194, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 3719, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 3753, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 4150, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 4172, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 4256, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 4279, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 8165, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 8317, "length": 9, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { className?: string; children: ReactNode; onClick?: () => void; max?: number; direction: TradeDirection; size?: BadgeSize; solid?: boolean; variant?: BadgeVariant; ... 6 more ...; counter?: boolean; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"Long\" | \"Short\"' is not assignable to type 'TradeDirection'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<FC<BadgeProps>, any, { direction: TradeDirection; }, never, FC<BadgeProps>, FC<BadgeProps>>): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"Long\" | \"Short\"' is not assignable to type 'TradeDirection'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 2127, "length": 9, "messageText": "The expected type comes from property 'direction' which is declared here on type 'IntrinsicAttributes & { className?: string; children: ReactNode; onClick?: () => void; max?: number; direction: TradeDirection; size?: BadgeSize; ... 8 more ...; counter?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 2127, "length": 9, "messageText": "The expected type comes from property 'direction' which is declared here on type 'IntrinsicAttributes & { className?: string; children: ReactNode; onClick?: () => void; max?: number; direction: TradeDirection; size?: BadgeSize; ... 8 more ...; counter?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 8392, "length": 15, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '\"Long\" | \"Short\"' is not assignable to parameter of type 'TradeDirection'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '\"Long\"' is not assignable to type 'TradeDirection'. Did you mean '\"long\"'?", "category": 1, "code": 2820}]}}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 8612, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'entryPrice' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 8644, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'exitPrice' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 8914, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 8973, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 9137, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 9195, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 9285, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}]], [268, [{"file": "./src/features/trade-analysis/hooks/tradeanalysiscontext.tsx", "start": 323, "length": 17, "messageText": "'TradeAnalysisData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [537, [{"file": "./src/features/trade-analysis/hooks/tradeanalysisstate.ts", "start": 178, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/hooks/tradeanalysisstate.ts", "start": 187, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [538, [{"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 775, "length": 8, "messageText": "'dispatch' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 1839, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'action' does not exist on type 'TradeFilter'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 2347, "length": 11, "messageText": "Block-scoped variable 'fetchTrades' used before its declaration.", "category": 1, "code": 2448, "relatedInformation": [{"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 5744, "length": 11, "messageText": "'fetchTrades' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 2963, "length": 6, "code": 2345, "category": 1, "messageText": "Argument of type 'Trade[]' is not assignable to parameter of type 'CompleteTradeData[]'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 3516, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'date' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 3545, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'date' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 3746, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'profit' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 3802, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'date' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 4727, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'profit' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 4756, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'profit' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 5024, "length": 610, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ winRate: number; profitFactor: number; averageWin: number; averageLoss: number; totalTrades: number; netProfit: number; expectancy: number; maxDrawdown: number; sharpeRatio: number; successStreak: number; }' is not assignable to parameter of type 'SetStateAction<PerformanceMetrics>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ winRate: number; profitFactor: number; averageWin: number; averageLoss: number; totalTrades: number; netProfit: number; expectancy: number; maxDrawdown: number; sharpeRatio: number; successStreak: number; }' is missing the following properties from type 'PerformanceMetrics': winningTrades, losingTrades, breakeven, totalProfitLoss, and 3 more.", "category": 1, "code": 2740}]}}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 5872, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'getTradingData' does not exist on type 'TradeStorageService'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6544, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'date' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6566, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'symbol' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6590, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'action' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6614, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'price' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6637, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'quantity' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6663, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'profit' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6687, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'fees' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6713, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'notes' does not exist on type 'CompleteTradeData'."}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6749, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'tags' does not exist on type 'CompleteTradeData'."}]], [544, [{"file": "./src/features/trade-analysis/index.ts", "start": 114, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/index.ts", "start": 1192, "length": 28, "messageText": "Cannot find module './state/tradeAnalysisState' or its corresponding type declarations.", "category": 1, "code": 2307}]], [267, [{"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 98, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 6237, "length": 9, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'TradeDirection' is not assignable to type '\"Long\" | \"Short\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"long\"' is not assignable to type '\"Long\" | \"Short\"'. Did you mean '\"Long\"'?", "category": 1, "code": 2820}]}, "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8999, "length": 9, "messageText": "The expected type comes from property 'direction' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 6723, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 6757, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 6960, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 7033, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 7104, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 7948, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 8006, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'exitTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 9991, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 11514, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 12653, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"file": "./src/features/trade-analysis/services/tradeanalysisapi.ts", "start": 13548, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}]], 279, [536, [{"file": "./src/features/trade-analysis/tradeanalysiscomposed.tsx", "start": 284, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'DashboardSection'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/tradeanalysiscomposed.tsx", "start": 2401, "length": 4, "messageText": "'data' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/tradeanalysiscomposed.tsx", "start": 2718, "length": 11, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'ViewType' is not assignable to type '\"trades\" | \"summary\" | \"charts\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"time\"' is not assignable to type '\"trades\" | \"summary\" | \"charts\"'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./src/features/trade-analysis/types.ts", "start": 3027, "length": 11, "messageText": "The expected type comes from property 'defaultView' which is declared here on type 'Partial<UserPreferences>'", "category": 3, "code": 6500}]}]], [266, [{"file": "./src/features/trade-analysis/types.ts", "start": 514, "length": 5, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}]], [550, [{"file": "./src/features/trade-analysis/types/index.ts", "start": 238, "length": 5, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-analysis/types/index.ts", "start": 245, "length": 13, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-analysis/types/index.ts", "start": 260, "length": 18, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}]], 551, 567, 553, 554, 555, [289, [{"file": "./src/features/trade-journal/components/trade-dol-analysis/dolanalysiscomposed.tsx", "start": 264, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'DashboardSection'.", "category": 1, "code": 2305}]], 286, 287, [288, [{"file": "./src/features/trade-journal/components/trade-dol-analysis/doleffectivenessrating.tsx", "start": 192, "length": 25, "messageText": "'DOL_EFFECTIVENESS_OPTIONS' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 285, 284, 283, 290, 291, [235, [{"file": "./src/features/trade-journal/components/trade-form/index.ts", "start": 96, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [232, [{"file": "./src/features/trade-journal/components/trade-form/tradeformactions.tsx", "start": 209, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [222, [{"file": "./src/features/trade-journal/components/trade-form/tradeformbasicfields.tsx", "start": 187, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-form/tradeformbasicfields.tsx", "start": 196, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-form/tradeformbasicfields.tsx", "start": 232, "length": 12, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'SetupBuilder'.", "category": 1, "code": 2305}]], [220, [{"file": "./src/features/trade-journal/components/trade-form/tradeformheader.tsx", "start": 175, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [234, [{"file": "./src/features/trade-journal/components/trade-form/tradeformloading.tsx", "start": 160, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [233, [{"file": "./src/features/trade-journal/components/trade-form/tradeformmessages.tsx", "start": 170, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [230, [{"file": "./src/features/trade-journal/components/trade-form/tradeformriskfields.tsx", "start": 173, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-form/tradeformriskfields.tsx", "start": 1676, "length": 16, "messageText": "'validationErrors' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [231, [{"file": "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "start": 119, "length": 8, "messageText": "'useState' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "start": 195, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "start": 204, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "start": 240, "length": 12, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'SetupBuilder'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "start": 540, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'SelectDropdown'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "start": 2322, "length": 16, "messageText": "'validationErrors' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [229, [{"file": "./src/features/trade-journal/components/trade-form/tradeformtimingfields.tsx", "start": 166, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-form/tradeformtimingfields.tsx", "start": 423, "length": 10, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'TimePicker'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-journal/components/trade-form/tradeformtimingfields.tsx", "start": 435, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'SelectDropdown'.", "category": 1, "code": 2305}]], [264, [{"file": "./src/features/trade-journal/components/trade-journal/index.ts", "start": 102, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [263, [{"file": "./src/features/trade-journal/components/trade-journal/tradejournalcontent.tsx", "start": 183, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/trade-journal/tradejournalcontent.tsx", "start": 2616, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'Trade[]' is not assignable to type 'CompleteTradeData[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'trade' is missing in type 'Trade' but required in type 'CompleteTradeData'.", "category": 1, "code": 2741}]}, "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8380, "length": 5, "messageText": "'trade' is declared here.", "category": 3, "code": 2728}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 2011, "length": 6, "messageText": "The expected type comes from property 'trades' which is declared here on type 'IntrinsicAttributes & TradeListProps'", "category": 3, "code": 6500}]}]], [254, [{"file": "./src/features/trade-journal/components/trade-journal/tradejournalfilters.tsx", "start": 160, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [253, [{"file": "./src/features/trade-journal/components/trade-journal/tradejournalheader.tsx", "start": 222, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [261, [{"file": "./src/features/trade-journal/components/trade-list/index.ts", "start": 96, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [259, [{"file": "./src/features/trade-journal/components/trade-list/tradelistempty.tsx", "start": 205, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [258, [{"file": "./src/features/trade-journal/components/trade-list/tradelistexpandedrow.tsx", "start": 5646, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'execution_quality' does not exist on type 'TradeAnalysis'."}, {"file": "./src/features/trade-journal/components/trade-list/tradelistexpandedrow.tsx", "start": 5839, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'lessons_learned' does not exist on type 'TradeAnalysis'."}, {"file": "./src/features/trade-journal/components/trade-list/tradelistexpandedrow.tsx", "start": 6030, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'emotional_state' does not exist on type 'TradeAnalysis'."}, {"file": "./src/features/trade-journal/components/trade-list/tradelistexpandedrow.tsx", "start": 6223, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'market_conditions' does not exist on type 'TradeAnalysis'."}]], [256, [{"file": "./src/features/trade-journal/components/trade-list/tradelistheader.tsx", "start": 152, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [260, [{"file": "./src/features/trade-journal/components/trade-list/tradelistloading.tsx", "start": 173, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], 257, 280, 566, [281, [{"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 4814, "length": 12, "messageText": "This comparison appears to be unintentional because the types 'ScoreRange' and '\"\"' have no overlap.", "category": 1, "code": 2367}, {"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 6138, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 6329, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 6523, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 6708, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 6889, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 7077, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "start": 7268, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}]], 562, 561, 565, 560, 563, 558, [559, [{"file": "./src/features/trade-journal/components/trade-setup-classification/secondarysetupselector.tsx", "start": 3034, "length": 12, "messageText": "'primarySetup' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [564, [{"file": "./src/features/trade-journal/components/trade-setup-classification/setupclassificationcomposed.tsx", "start": 289, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'DashboardSection'.", "category": 1, "code": 2305}]], [556, [{"file": "./src/features/trade-journal/components/trade-setup-classification/setupclassificationsection.tsx", "start": 169, "length": 8, "messageText": "'useState' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [262, [{"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 214, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 3801, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 3831, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 3940, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 3982, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'setup_action' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 4022, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_variable' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 4064, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}]], [282, [{"file": "./src/features/trade-journal/constants/dolanalysis.ts", "start": 101, "length": 73, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], 225, [557, [{"file": "./src/features/trade-journal/constants/setupclassification.ts", "start": 97, "length": 160, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [228, [{"file": "./src/features/trade-journal/hooks/index.ts", "start": 100, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [224, [{"file": "./src/features/trade-journal/hooks/usetradecalculations.ts", "start": 119, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [252, [{"file": "./src/features/trade-journal/hooks/usetradefilters.ts", "start": 121, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [227, [{"file": "./src/features/trade-journal/hooks/usetradeform.ts", "start": 105, "length": 9, "messageText": "'useEffect' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradeform.ts", "start": 179, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradeform.ts", "start": 2494, "length": 8, "messageText": "'navigate' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradeform.ts", "start": 4832, "length": 9, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'CompleteTradeData' is not assignable to parameter of type 'Trade'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'CompleteTradeData' is missing the following properties from type 'Trade': id, symbol, date, direction, and 9 more.", "category": 1, "code": 2740}]}}]], [223, [{"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 165, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 635, "length": 10, "messageText": "'isNewTrade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 7298, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 7328, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 7406, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 7464, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'setup_action' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 7532, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_variable' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 7599, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}]], [251, [{"file": "./src/features/trade-journal/hooks/usetradejournal.ts", "start": 153, "length": 17, "messageText": "'CompleteTradeData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradejournal.ts", "start": 1107, "length": 6, "code": 2322, "category": 1, "messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'."}, {"file": "./src/features/trade-journal/hooks/usetradejournal.ts", "start": 1930, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'CompleteTradeData' is not assignable to type 'Trade'.", "category": 1, "code": 2322}]}}]], [255, [{"file": "./src/features/trade-journal/hooks/usetradelist.ts", "start": 134, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [226, [{"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 1041, "length": 10, "messageText": "'isNewTrade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 3348, "length": 18, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ clarity: string; confluence: string; context: string; risk: string; reward: string; timeframe: string; volume: string; }' is not assignable to parameter of type 'Record<string, ScoreRange>'.", "category": 1, "code": 2345, "next": [{"messageText": "Property 'clarity' is incompatible with index signature.", "category": 1, "code": 2530, "next": [{"messageText": "Type 'string' is not assignable to type 'ScoreRange'.", "category": 1, "code": 2322}]}]}}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 3521, "length": 19, "code": 2551, "category": 1, "messageText": "Property 'patternQualityScore' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string; takeProfit?: string; profit: string; modelType?: string; session?: string; ... 43 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'. Did you mean 'patternQuality'?", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9754, "length": 14, "messageText": "'patternQuality' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 3889, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'dolAnalysis' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string; takeProfit?: string; profit: string; modelType?: string; session?: string; ... 43 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'."}]], [221, [{"file": "./src/features/trade-journal/hooks/usetradevalidation.ts", "start": 130, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [552, [{"file": "./src/features/trade-journal/index.ts", "start": 102, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [292, [{"file": "./src/features/trade-journal/tradeform.tsx", "start": 280, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/tradeform.tsx", "start": 413, "length": 8, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'TabPanel'.", "category": 1, "code": 2305}]], [265, [{"file": "./src/features/trade-journal/tradejournal.tsx", "start": 229, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/tradejournal.tsx", "start": 1234, "length": 6, "code": 2345, "category": 1, "messageText": "Argument of type 'Trade[]' is not assignable to parameter of type 'CompleteTradeData[]'."}, {"file": "./src/features/trade-journal/tradejournal.tsx", "start": 1514, "length": 14, "code": 2322, "category": 1, "messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-journal/tradejournalcontent.tsx", "start": 1193, "length": 14, "messageText": "The expected type comes from property 'filteredTrades' which is declared here on type 'IntrinsicAttributes & TradeJournalContentProps'", "category": 3, "code": 6500}]}]], [219, [{"file": "./src/features/trade-journal/types/index.ts", "start": 4718, "length": 5, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4727, "length": 32, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4799, "length": 15, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4818, "length": 10, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4832, "length": 13, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4849, "length": 17, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4870, "length": 12, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4886, "length": 18, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4908, "length": 8, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4920, "length": 5, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4929, "length": 10, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trade-journal/types/index.ts", "start": 4943, "length": 14, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}]], 569, [572, [{"file": "./src/features/trading-dashboard/components/f1header.tsx", "start": 449, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'LoadingSpinner'.", "category": 1, "code": 2305}]], [574, [{"file": "./src/features/trading-dashboard/components/index.ts", "start": 928, "length": 23, "messageText": "Duplicate identifier 'useTradingDashboardData'.", "category": 1, "code": 2300}, {"file": "./src/features/trading-dashboard/components/index.ts", "start": 1065, "length": 23, "messageText": "Duplicate identifier 'useTradingDashboardData'.", "category": 1, "code": 2300}]], 134, 216, [573, [{"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 517, "length": 12, "messageText": "'\"@adhd-trading-dashboard/shared\"' has no exported member named 'useFormField'. Did you mean 'FormField'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "../shared/dist/components/molecules/formfield.d.ts", "start": 640, "length": 9, "messageText": "'FormField' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 534, "length": 15, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'useLoadingState'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 553, "length": 15, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'validationRules'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 589, "length": 19, "messageText": "'tradeStorageService' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 7367, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9386, "length": 8, "messageText": "The expected type comes from property 'quantity' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 7382, "length": 10, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9408, "length": 10, "messageText": "The expected type comes from property 'entryPrice' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 7399, "length": 9, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9432, "length": 9, "messageText": "The expected type comes from property 'exitPrice' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 7415, "length": 6, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9503, "length": 6, "messageText": "The expected type comes from property 'profit' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 7901, "length": 8, "messageText": "'autoSave' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 7920, "length": 16, "messageText": "'autoSaveInterval' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 8157, "length": 9, "messageText": "'lastSaved' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 8168, "length": 12, "messageText": "'setLastSaved' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 10165, "length": 6, "messageText": "'profit' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 10625, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9386, "length": 8, "messageText": "The expected type comes from property 'quantity' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 10673, "length": 10, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9408, "length": 10, "messageText": "The expected type comes from property 'entryPrice' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 10725, "length": 9, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9432, "length": 9, "messageText": "The expected type comes from property 'exitPrice' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/components/quicktradeform.tsx", "start": 10775, "length": 6, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9503, "length": 6, "messageText": "The expected type comes from property 'profit' which is declared here on type 'TradeFormData'", "category": 3, "code": 6500}]}]], [217, [{"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 165, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 3797, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'setup' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 3848, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 3992, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'market' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 4150, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'win' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 4162, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'rMultiple' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 4233, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/components/recenttradestable.tsx", "start": 4246, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}]], [218, [{"file": "./src/features/trading-dashboard/components/setupanalysis.tsx", "start": 168, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [575, [{"file": "./src/features/trading-dashboard/components/tradingdashboardcontainer.tsx", "start": 437, "length": 13, "messageText": "Module '\"react\"' has no exported member 'ErrorBoundary'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/components/tradingdashboardcontainer.tsx", "start": 437, "length": 13, "messageText": "'ErrorBoundary' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/components/tradingdashboardcontainer.tsx", "start": 784, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'LoadingSpinner'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/components/tradingdashboardcontainer.tsx", "start": 8867, "length": 18, "messageText": "'enableFeatureFlags' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [571, [{"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 537, "length": 17, "messageText": "'\"@adhd-trading-dashboard/shared\"' has no exported member named 'PerformanceMetric'. Did you mean 'PerformanceMetrics'?", "category": 1, "code": 2724}, {"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 558, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'ChartDataPoint'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 576, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'SetupPerformance'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 596, "length": 18, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'SessionPerformance'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 666, "length": 23, "messageText": "Individual declarations in merged declaration 'useTradingDashboardData' must be all exported or all local.", "category": 1, "code": 2395}, {"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 666, "length": 23, "messageText": "Import declaration conflicts with local declaration of 'useTradingDashboardData'.", "category": 1, "code": 2440}, {"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 2305, "length": 11, "messageText": "'dataFetcher' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "start": 5625, "length": 23, "messageText": "Individual declarations in merged declaration 'useTradingDashboardData' must be all exported or all local.", "category": 1, "code": 2395}]], [133, [{"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 182, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 570, "length": 10, "messageText": "'mockTrades' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 602, "length": 2, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8945, "length": 2, "messageText": "The expected type comes from property 'id' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 712, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9050, "length": 5, "messageText": "The expected type comes from property 'entry' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 735, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9069, "length": 4, "messageText": "The expected type comes from property 'exit' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 1097, "length": 2, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8945, "length": 2, "messageText": "The expected type comes from property 'id' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 1208, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9050, "length": 5, "messageText": "The expected type comes from property 'entry' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 1231, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9069, "length": 4, "messageText": "The expected type comes from property 'exit' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 1572, "length": 2, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8945, "length": 2, "messageText": "The expected type comes from property 'id' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 1687, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9050, "length": 5, "messageText": "The expected type comes from property 'entry' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 1710, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9069, "length": 4, "messageText": "The expected type comes from property 'exit' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 2062, "length": 2, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8945, "length": 2, "messageText": "The expected type comes from property 'id' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 2173, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9050, "length": 5, "messageText": "The expected type comes from property 'entry' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 2196, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9069, "length": 4, "messageText": "The expected type comes from property 'exit' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 2550, "length": 2, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8945, "length": 2, "messageText": "The expected type comes from property 'id' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 2670, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9050, "length": 5, "messageText": "The expected type comes from property 'entry' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 2693, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9069, "length": 4, "messageText": "The expected type comes from property 'exit' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 3069, "length": 13, "messageText": "'mockChartData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 3985, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'win' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 4140, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 4240, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'rMultiple' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 4795, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'setup' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 5160, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'win' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 5323, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 5448, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'rMultiple' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 5959, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 6348, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'win' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 6513, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 6640, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'rMultiple' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 7353, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 7824, "length": 1628, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: string; date: string; model: string; session: string; setup: string; entry: string; exit: string; direction: \"Long\" | \"Short\"; market: string; rMultiple: number; patternQuality: number; win: boolean; ... 7 more ...; drawOnLiquidity: string; }[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; date: string; model: string; session: string; setup: string; entry: string; exit: string; direction: \"Long\" | \"Short\"; market: string; rMultiple: number; patternQuality: number; win: boolean; ... 7 more ...; drawOnLiquidity: string; }' is missing the following properties from type 'Trade': symbol, size, stopLoss, takeProfit, and 4 more.", "category": 1, "code": 2740}]}}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 8131, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 8155, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 8250, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 8282, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'setup_action' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 8312, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_variable' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "start": 8344, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}]], [570, [{"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 496, "length": 15, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'useLoadingState'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 516, "length": 17, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'useDataFormatting'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 516, "length": 17, "messageText": "'useDataFormatting' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 1570, "length": 2819, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: string; date: string; model: string; session: string; setup: string; entry: string; exit: string; direction: \"Long\" | \"Short\"; market: string; rMultiple: number; patternQuality: number; win: boolean; ... 7 more ...; drawOnLiquidity: string; }[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; date: string; model: string; session: string; setup: string; entry: string; exit: string; direction: \"Long\" | \"Short\"; market: string; rMultiple: number; patternQuality: number; win: boolean; ... 7 more ...; drawOnLiquidity: string; }' is missing the following properties from type 'Trade': symbol, size, stopLoss, takeProfit, and 4 more.", "category": 1, "code": 2740}]}}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 1930, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 1954, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 2057, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 2093, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'setup_action' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 2127, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_variable' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 2163, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'setup_entry' does not exist on type 'TradeRecord'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 4954, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'win' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 5092, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 5177, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'rMultiple' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 6468, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 7576, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'setup' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 7981, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'win' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 8149, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 8273, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'rMultiple' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 9031, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 9453, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'win' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 9623, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'pnl' does not exist on type 'Trade'."}, {"file": "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "start": 9749, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'rMultiple' does not exist on type 'Trade'."}]], [578, [{"file": "./src/features/trading-dashboard/index.ts", "start": 94, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [236, [{"file": "./src/features/trading-dashboard/tradingdashboard.tsx", "start": 185, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/tradingdashboard.tsx", "start": 7068, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}, {"file": "./src/features/trading-dashboard/tradingdashboard.tsx", "start": 7085, "length": 10, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}, {"file": "./src/features/trading-dashboard/tradingdashboard.tsx", "start": 7104, "length": 9, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}, {"file": "./src/features/trading-dashboard/tradingdashboard.tsx", "start": 7122, "length": 6, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}]], [568, [{"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 280, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'DashboardSection'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 298, "length": 12, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'TradeMetrics'.", "category": 1, "code": 2305}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4239, "length": 6, "messageText": "'trades' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4275, "length": 9, "messageText": "'chartData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4290, "length": 16, "messageText": "'setupPerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4312, "length": 18, "messageText": "'sessionPerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4559, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'totalTrades' does not exist on type 'PerformanceMetric[]'."}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4628, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'winRate' does not exist on type 'PerformanceMetric[]'."}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4680, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'winRate' does not exist on type 'PerformanceMetric[]'."}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4752, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'totalPnL' does not exist on type 'PerformanceMetric[]'."}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4804, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'totalPnL' does not exist on type 'PerformanceMetric[]'."}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4847, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'totalPnL' does not exist on type 'PerformanceMetric[]'."}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4920, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'avgRMultiple' does not exist on type 'PerformanceMetric[]'."}, {"file": "./src/features/trading-dashboard/tradingdashboardcomposed.tsx", "start": 4974, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'avgRMultiple' does not exist on type 'PerformanceMetric[]'."}]], 576, [577, [{"file": "./src/features/trading-dashboard/tradingdashboardwithfeatureflag.tsx", "start": 6869, "length": 13, "messageText": "'setShowToggle' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [132, [{"file": "./src/features/trading-dashboard/types/index.ts", "start": 205, "length": 5, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}, {"file": "./src/features/trading-dashboard/types/index.ts", "start": 212, "length": 18, "messageText": "Re-exporting a type when the '--isolatedModules' flag is provided requires using 'export type'.", "category": 1, "code": 1205}]], [579, [{"file": "./src/features/trading-dashboard/utils/datavalidation.ts", "start": 359, "length": 17, "messageText": "'CompleteTradeData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trading-dashboard/utils/datavalidation.ts", "start": 6546, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9050, "length": 5, "messageText": "The expected type comes from property 'entry' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"file": "./src/features/trading-dashboard/utils/datavalidation.ts", "start": 6621, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9069, "length": 4, "messageText": "The expected type comes from property 'exit' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}]], 313, 128, 580, 129, 127, 300, 581, 582, 583, 584, 585, 586, 587, 312, 588, 297, 589, 296, [590, [{"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 191, "length": 15, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'TradeJournalApi'.", "category": 1, "code": 2305}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 210, "length": 18, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'TradeJournalEvents'.", "category": 1, "code": 2305}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 232, "length": 17, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'TradeJournalState'.", "category": 1, "code": 2305}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 262, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 899, "length": 13, "code": 2740, "category": 1, "messageText": "Type 'CompleteTradeData' is missing the following properties from type 'Trade': id, symbol, date, direction, and 9 more."}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 2304, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'date' does not exist on type 'CompleteTradeData'."}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 2333, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'date' does not exist on type 'CompleteTradeData'."}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 2448, "length": 20, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'CompleteTradeData' is not assignable to type 'Trade'.", "category": 1, "code": 2322}]}}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 2993, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'win_loss' does not exist on type 'Trade'."}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 3158, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'profit_loss' does not exist on type 'Trade'. Did you mean 'profitLoss'?", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9133, "length": 10, "messageText": "'profitLoss' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 4023, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'entry_price' does not exist on type 'Trade'."}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 4052, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'exit_price' does not exist on type 'Trade'."}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 4102, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'profit_loss' does not exist on type 'Trade'. Did you mean 'profitLoss'?", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9133, "length": 10, "messageText": "'profitLoss' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 4131, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'win_loss' does not exist on type 'Trade'."}, {"file": "./src/services/contracts/tradejournalapiimpl.ts", "start": 4157, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'setup_constant' does not exist on type 'Trade'."}]], 131, 314, 301, 302, 77, 78, 79, 85, 80, 81, 82, 83, 84, 102, 86, 87, 88, 90, 97, 91, 92, 94, 93, 96, 95, 89, 98, 99, 100, 101, 75, 74, 108, 103, 104, 105, 106, 107, 124, 121, 123, 118, 76, 122, 117, 116, 119, 113, 111, 115, 112, 114, 110, 109, 73, 72, 120], "latestChangedDtsFile": "./dist/services/contracts/TradeJournalApiImpl.d.ts"}, "version": "4.9.4"}